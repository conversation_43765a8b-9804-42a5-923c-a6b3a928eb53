import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:developer' as dev;
import 'dart:io';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/controller/history_controller.dart';
import 'package:mposxs/app/controller/mqtt_controller.dart';
import 'package:mposxs/app/controller/payment_init_controller.dart';
import 'package:mposxs/app/data/model/base_response.dart';
import 'package:mposxs/app/data/model/detail_transaction_history_model.dart';
import 'package:mposxs/app/data/model/settle_transaction_detail_model.dart';
import 'package:mposxs/app/data/model/settle_transaction_history_model.dart';
import 'package:mposxs/app/data/model/transaction_history_model.dart' as thm;
import 'package:mposxs/app/data/provider/api_client.dart';
import 'package:mposxs/app/data/provider/local_storage.dart';
import 'package:mposxs/app/data/provider/native_bridge.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/route/app_pages.dart';
import 'package:mposxs/app/ui/screen/main/widget/bottom_sheet_confirm_password.dart';
import 'package:mposxs/app/ui/screen/payment/widget/payment_finish_p12.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/widget/printer/temp_printer_bill_qr_widget.dart';
import 'package:mposxs/app/ui/widget/printer/temp_printer_bill_widget.dart';
import 'package:mposxs/app/util/TTSControl.dart';
import 'package:mposxs/app/util/api_constant.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/app_validation.dart';
import 'package:mposxs/app/util/mpos_constant.dart';
import 'package:screenshot/screenshot.dart';
import 'package:cashiermodule/model_instance/app_configuration.dart' as cashierModule;

import '../data/provider/logger.dart';
import '../ui/widget/base_dialog.dart';
import '../ui/widget/quick_draw_widget.dart';
import '../util/np_tingting_speak_handler.dart';
import 'main_controller.dart';

class PaymentFinishController extends GetxController {
  late BuildContext context;
  MyAppController _appController = Get.find<MyAppController>();
  RxBool showAnimation = false.obs;
  RxBool isTransSkipSignature = false.obs;
  RxBool allowQuickDraw = false.obs;
  RxBool isPaymentInstallmentErr = false.obs;
  RxString iconLottieName = AppImages.lottieResultSuccess.obs;
  PaymentFinishArguments? inputData;
  Map? wfInfo;
  Map? transBankDetails;
  int statePayment = 0;
  int amountPayment = 0;
  String? feeQuickDraw = '';
  int timeWaitPrintSecond = 6000;
  RxInt numAutoPrint = 0.obs;
  String? notePayment = '';
  String? transactionId = '';
  String transactionTime = '';
  Color statusColor = AppColors.blackText;
  Color bgTopColor = AppColors.white;
  String titlePaymentText = '';
  String? _printRef = '';
  String? _printInvoice = '';
  String? _printBatch = '';
  String? _printApprove = '';
  String? _printTid = '';
  String? _printMid = '';
  String imageB64 = '';
  DateTime _dateTimePrintDefault = DateTime.now();
  Rx<Widget> tempPrinterWidget = Container().obs;
  final ScreenshotController screenshotController = ScreenshotController();
  Rx<PaymentResult> paymentResult = PaymentResult().obs;
  RxInt countDownAutoBack = 15.obs;
  Timer? timerAutoBack;

  String? cardHolderName;
  String? pan;

  @override
  void onInit() {
    LoggerMp().writeLog('init finish screen');
    imageB64 = '';
    if (_appController.paymentInfoSession?.needResetAmount != null) {
      _appController.paymentInfoSession!.needResetAmount = true;
    }
    statePayment = _initDataUIByType();
    _initDataUIByStatus(statePayment);
    if(MyAppController.isKozenP12orN4()) {
      paymentResult.value = PaymentResult(statePayment: statePayment, holderName: cardHolderName, pan: pan, amountPayment: amountPayment, transactionTime: transactionTime, transId: transactionId, notePayment: notePayment);
    }
    LoggerMp().writeLog('---- end of init ----${paymentResult==null?'null':'not null'} --- ${DateTime.now()}');
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
    // await Future.delayed(Duration(milliseconds: 600));

    showAnimation.value = true;
    if (statePayment == MposConstant.PAYMENT_STATE_SUCCESS) {
      if (_appController.userInfo!.isRunMacq() && wfInfo!=null) {
        _printRef     = wfInfo!['rrn'];
        _printInvoice = wfInfo!['invoiceNo'];
        _printBatch   = wfInfo!['batchNo'];
        _printApprove = wfInfo!['authCode'];
        _printTid = wfInfo!['tid'];
        _printMid = wfInfo!['mid'];
        isTransSkipSignature.value = wfInfo!['flagNoSignature'];
      } else if(transBankDetails!=null) {
        _printRef     = transBankDetails!['RREFNo'];
        _printInvoice = transBankDetails!['invoiceNumber'];
        _printBatch   = transBankDetails!['batchNo'];
        _printApprove = transBankDetails!['approvalCode'];
      } else {
        _getTransactionStatus();
      }
      if (inputData!.serviceType == MposConstant.CARD_PAYMENT ||
          inputData!.serviceType == MposConstant.UNSIGN_CARD_PAYMENT ||
          inputData!.serviceType == MposConstant.INSTALLMENT_CARD_PAYMENT ||
          inputData!.serviceType == MposConstant.VAS_CARD_PAYMENT) {
        // tự động in và gửi thông tin in lên server
        autoPrintReceipt('NORMAL');
        if (!MyAppController.isKozenP12orN4()) {
          _sendDataPrint();
        }
      } else if (inputData!.serviceType == MposConstant.QR_PAYMENT) {
        autoPrintReceipt('QR');
      }
    }
    _sendNeededRequest();
    _promotionSound(inputData);
    if (MyAppController.isKozenP12orN4() && (inputData!.serviceType != MposConstant.INSTALLMENT_CARD_PAYMENT)) {
      // case trả góp có webview nên ko được tự động đóng màn hình
      timerAutoBack = Timer.periodic(Duration(seconds: 1), (Timer timer) {
        if (countDownAutoBack.value > 0) {
          countDownAutoBack.value = countDownAutoBack.value - 1;
        } else {
          onPressGoHome();
        }
      });
    }
    LoggerMp().writeLog('Show scren Finnish ready ${DateTime.now()}');
  }

  int _initDataUIByType() {
    inputData = Get.arguments;
    int statePaymentTemp = 0;
    try {
      if (inputData != null) {
        LoggerMp().writeLog('serviceType: ${inputData!.serviceType}');

        if (inputData!.autoCloseFinishScreen != null) {
          countDownAutoBack.value = inputData!.autoCloseFinishScreen!;
        }

        switch (inputData!.serviceType) {
          case MposConstant.CARD_PAYMENT:
          case MposConstant.INSTALLMENT_CARD_PAYMENT:
          case MposConstant.UNSIGN_CARD_PAYMENT:
            if (inputData!.paymentData != null) {
              Map? result = inputData!.paymentData!['result'];
              Map? userCard = inputData!.paymentData!['userCard'];
              Map error = result != null ? (result['error'] ?? {}) : {};
              transactionId = result != null ? (result['transactionId'] ?? '') : '';
              transactionTime = DateFormat('HH:mm, dd/MM/yyyy').format(DateTime.now());
              String status = result != null ? (result['status'] ?? '') : '';
              amountPayment = Platform.isIOS
                  ? int.parse(result != null ? (result['amount'] ?? '0').toString() : '0')
                  : int.parse(userCard != null ? (userCard['amountAuthorized'] ?? '0').toString() : '0');
              if (status == 'APPROVED') {
                soundPaymentSuccessForMposPro();
                wfInfo = inputData!.paymentData!['wfInfo'];
                transBankDetails = inputData!.paymentData!['transactionDetail'];

                cardHolderName = wfInfo?['cardHolderName'];
                pan = wfInfo?['pan'];

                String trxType = wfInfo?['trxType'] ?? "";
                _checkQuickDraw(trxType, amountPayment);

                statePaymentTemp = MposConstant.PAYMENT_STATE_SUCCESS;
              } else if (status == 'UNSIGN') {
                statePaymentTemp = MposConstant.PAYMENT_STATE_REVIEW;
                notePayment = AppStrings.getString(AppStrings.unSignPaymentResult);
              } else {
                statePaymentTemp = MposConstant.PAYMENT_STATE_FAIL;
                notePayment = !isNullEmpty(inputData!.errorMsg)
                    ? inputData!.errorMsg
                    :(error['message'] ?? '');
              }
            } else {
              statePaymentTemp = MposConstant.PAYMENT_STATE_FAIL;
              notePayment = inputData!.errorMsg;
            }
            break;
          case MposConstant.QR_PAYMENT:
          case MposConstant.CREATE_LINK_PAYMENT:
            isTransSkipSignature.value = false;
            cardHolderName = inputData!.paymentData!['cardHolderName'] ?? '';
            pan = inputData!.paymentData!['pan'] ?? '';
            transactionId = isNullEmpty(inputData!.paymentData!['txid'])
                ? 'MPQR_' + (inputData!.paymentData!['orderCode'] ?? '')
                : inputData!.paymentData!['txid'] ?? '';
            transactionTime = DateFormat('HH:mm, dd/MM/yyyy').format(DateTime.now());
            statePaymentTemp = MposConstant.PAYMENT_STATE_SUCCESS;
            String? amountString = inputData!.paymentData!['amount'].toString();
            if (!isNullEmpty(amountString)) {
              amountPayment = int.parse(inputData!.paymentData!['amount'].toString().replaceFirst('.0', ''));
            }
            break;
        }
      }
    }catch (e) {
      LoggerMp().writeLog('initDataUIByType ${e.toString()}', actionName: LoggerMp.LOGGER_TYPE_ERROR);

    }
    return statePaymentTemp;
  }

  _promotionSound(PaymentFinishArguments? finishArguments){
    if (finishArguments != null){
      String jsonResultTransaction = jsonEncode(finishArguments.paymentData);
      _appController.playSoundPromotion(jsonResultTransaction);
    }
  }

  _initDataUIByStatus(int status) async {
    if (status == MposConstant.PAYMENT_STATE_SUCCESS) {
      iconLottieName.value = AppImages.lottieResultSuccess;
      statusColor = AppColors.success;
      bgTopColor = AppColors.bgButton;
      titlePaymentText = AppStrings.getString(AppStrings.paymentStateSuccess) ?? '';
    } else if (status == MposConstant.PAYMENT_STATE_FAIL) {
      iconLottieName.value = AppImages.lottieResultError;
      statusColor = AppColors.error;
      bgTopColor = AppColors.red;
      titlePaymentText = AppStrings.getString(AppStrings.paymentStateFail) ?? '';
    } else if (status == MposConstant.PAYMENT_STATE_REVIEW) {
      iconLottieName.value = AppImages.lottieResultProcessing;
      statusColor = AppColors.warning;
      bgTopColor = AppColors.warning;
      titlePaymentText = AppStrings.getString(AppStrings.paymentStateReview) ?? '';
    }
  }

  _sendNeededRequest() async {
    if (inputData?.serviceType == MposConstant.INSTALLMENT_CARD_PAYMENT && statePayment == MposConstant.PAYMENT_STATE_SUCCESS) {
      _paymentService();
      Map? userCard = inputData!.paymentData?['userCard'];
      Map? result = inputData!.paymentData?['result'];
      // todo need check trans is successfully
      if (userCard != null && result != null) {
        bool check = _checkCardInstallment(userCard['pan']);
        if (check) {
          if (statePayment == MposConstant.PAYMENT_STATE_SUCCESS) {
            String urlGuideInstallment =
                'https://mpos.vn/public/installment-guide/${_appController.installmentInfoSession?.selectedBank?.bankName ?? ''}.html';

            /// Máy smartPOS lỗi webview flutter, phải dùng webview native tạm
            // showDialogAlert(context,
            //     title: getLangString(Strings.titleInstallmentGuide)
            //         .replaceFirst('{bankName}', _installmentFlowStore.selectedBank?.bankName),
            //     text1stButton: getLangString(Strings.close),
            //     widgetDescription: Container(
            //       height: 300,
            //       child: WebView(
            //         javascriptMode: JavascriptMode.unrestricted,
            //         initialUrl: Uri.encodeFull(urlGuideInstallment),
            //       ),
            //     ));

            NativeBridge.getInstance().nativeShowDialogWebView(
                urlGuideInstallment,
                AppStrings.getString(AppStrings.titleInstallmentGuide)!
                    .replaceFirst('{bankName}', _appController.installmentInfoSession?.selectedBank?.bankName ?? ''),
                AppStrings.getString(AppStrings.close) ?? '');
          }
          _updateTransactionStatusInMpos(
              result['paymentIdentifier'], amountPayment, userCard['pan'], userCard['cardHolderName'], transactionId);

          _sendReceiptWithTransaction(transactionId, _appController.userInfo?.emailMerchant, false);
        }
      }
    }
  }

  bool _checkCardInstallment(String? binPaid) {
    List<String>? binList = _appController.installmentInfoSession!.selectedBank?.binList;
    if ((binList ?? []).length > 0) {
      if (!isNullEmpty(binPaid) && binPaid!.length > 6) {
        String binPaidCheck = binPaid.substring(0, 6);
        bool check = false;
        for (int i = 0; i < binList!.length; i++) {
          String bin = binList[i];
          if (bin.indexOf(binPaidCheck) > -1) {
            check = true;
            break;
          }
        }
        if (!check) {
          paymentResult.value.statePayment = MposConstant.PAYMENT_STATE_FAIL;
          allowQuickDraw.value = false;
          isPaymentInstallmentErr.value = true;

          // AppUtils.showDialogError(
          //   context,
          //   AppStrings.errorInstallmentNotSupportCard.tr.replaceFirst('%1s', binPaid),
          //   title: AppStrings.errorInstallmentTitle.tr,
          // );
          return false;
        }
      }
    }
    return true;
  }

  _paymentService() {
    var result = inputData?.paymentData != null ? inputData!.paymentData!['result'] : {};
    if (result != null) {
      var udid = result['paymentIdentifier'];
      var txid = result['transactionId'];
      if (!isNullEmpty(udid) && !isNullEmpty(txid)) {
        Map paramsPay = {
          'serviceName': 'PAY_SERVICE_TRANSACTION',
          'udid': udid,
          'txid': txid,
          'muid': _appController.loginAccount,
        };
        ApiClient.instance.request(data: json.encode(paramsPay));
      }
    }
  }

  _updateTransactionStatusInMpos(String? udid, int amount, String? pan, String? cardHolder, String? txId) {
    Map params = {
      "serviceName": "UPDATE_TRANSACTION_STATUS",
      "udid": _appController.userInfo?.merchantId,
      "amount": amount,
      "pan": pan,
      "cardHolder": cardHolder,
      "status": 'APPROVED',
      "txId": txId,
      "muid": _appController.loginAccount,
    };
    ApiClient.instance.request(url: ApiConstant.urlApi, data: json.encode(params));
  }

  _sendReceiptWithTransaction(String? transactionID, String? email, bool showPopup) async {
    Map params = {
      "transactionID": transactionID,
      "email": email,
    };
    _appController.showLoading();
    NativeResponseModel nativeResponseModel = await NativeBridge.getInstance().nativeSendReceiptWithTransaction(params);
    _appController.hideLoading();
    if (nativeResponseModel.isSuccess) {
      if (showPopup) {
        AppUtils.showDialogAlert(context, description: AppStrings.getString(AppStrings.success));
      }
    } else {
      if (showPopup) {
        AppUtils.showDialogErrorNative(context, nativeResponseModel.error!);
      }
    }
  }

  _getTransactionStatus() async {
    _appController.appendLog('getTransactionStatus');
    var result = inputData?.paymentData != null ? inputData!.paymentData!['result'] : {};
    if (result != null) {
      var udid = result['paymentIdentifier'];
      String transactionID = result != null ? (result['transactionId'] ?? '') : '';
      Map params = {
        "udid": udid,
        "transactionID": transactionID,
      };
      _appController.showLoading();
      NativeResponseModel nativeResponseModel = await NativeBridge.getInstance().nativeGetTransactionStatus(params);
      _appController.hideLoading();
      if (nativeResponseModel.isSuccess) {
        final String detailSettleTransaction = nativeResponseModel.data;
        Map responseMap = json.decode(detailSettleTransaction);
        TransactionDetail detailSettle = TransactionDetail.fromJson(responseMap as Map<String, dynamic>);
        _printRef = detailSettle.rREFNo;
        _printInvoice = detailSettle.invoiceNumber;
        _printBatch = detailSettle.batchNo;
        _printApprove = detailSettle.approvalCode;
      } else {
        AppUtils.showDialogErrorNative(context, nativeResponseModel.error!);
      }
    }
  }

  _sendDataPrint() async {
    if (inputData!.paymentData != null) {
      Map userCard = inputData!.paymentData!['userCard'];
      Map result = inputData!.paymentData!['result'];
      Map dataPrintStore = {
        'serviceName': 'RECEIPT_BUILD_RECEIPT',
        "paymentMethod": "CARD",
        'targetBase64Receipt': userCard['userSignature'],
        'txid': result['transactionId'],
        'amount': userCard['amountAuthorized'],
        'cardHolderName': userCard['cardHolderName'],
        'pan': userCard['pan'],
        'transactionDate': userCard['transactionDate'] ?? _dateTimePrintDefault.millisecondsSinceEpoch,
        'issuerCode': userCard['applicationLabel'],
        'merchantId': _appController.userInfo?.merchantId,
        'acquirer': _appController.userInfo?.bankName,
        'invoiceNo': _printInvoice ?? '',
        'batchNo': _printBatch ?? '',
        'approvalCode': _printApprove ?? '',
        'refNo': _printRef ?? '',
        'description': _appController.paymentInfoSession?.description,
        'saveToServer': false,
        'macq': _appController.userInfo!.isMacqFlow == 1,
        'flagNoSignature': isTransSkipSignature.value,
        'notReceipt': true, // don't need to server return base64_receipt
      };
      final storage = FlutterSecureStorage();
      storage.write(key: result['transactionId'], value: json.encode(dataPrintStore));
      BaseResponse baseResponse =
          await ApiClient.instance.request(url: ApiConstant.urlReceipt, data: json.encode(dataPrintStore));
      if (baseResponse.result!) {
        dataPrintStore['saveToServer'] = true;
        storage.write(key: result['transactionId'], value: json.encode(dataPrintStore));
      } else {
        dataPrintStore['saveToServer'] = false;
        storage.write(key: result['transactionId'], value: json.encode(dataPrintStore));
      }
    }
  }

  onPressGoHome() async {
    _appController.appendLog('onPressGoHome');
    timerAutoBack?.cancel();
    Get.until(ModalRoute.withName(AppRoute.main_screen));
    if (MyAppController.isKozenP12orN4()) {
      MainController _mc = Get.find<MainController>();
      // _mc.currentScreen.value = MainController.MAIN_SCREEN_P12_QR;
      _mc.initScreenP12();
      _mc.currentAmount.value = "0";
      PaymentInitController initController = Get.find<PaymentInitController>();
      initController.valueInput.value = '0';
    }
    LoggerMp().processPushLog();
  }

  onPressNewTransaction() async {
    timerAutoBack?.cancel();
    _appController.appendLog('onPressNewTransaction');
    LoggerMp().processPushLog();
    switch (inputData?.serviceType) {
      case MposConstant.CARD_PAYMENT:
      case MposConstant.UNSIGN_CARD_PAYMENT:
      case MposConstant.QR_PAYMENT:
        Get.until(ModalRoute.withName(AppRoute.main_screen));
        MainController _mc = Get.find<MainController>();
        if (MyAppController.isKozenP12orN4()) {
          PaymentInitController initController = Get.find<PaymentInitController>();
          initController.valueInput.value = '0';

          _mc.currentAmount.value = "0";
          if (inputData?.serviceType == MposConstant.CARD_PAYMENT) {
            _mc.currentScreen.value = MainController.MAIN_SCREEN_P12_ENTER_AMOUNT;
          } else {
            _mc.initScreenP12();
            // _mc.currentScreen.value = MainController.MAIN_SCREEN_P12_QR;
          }
        }
        else {
          _mc.currentScreen.value = MainController.MAIN_SCREEN_NORMAL_PAY;
        }
        break;
      case MposConstant.INSTALLMENT_CARD_PAYMENT:
        Get.until(ModalRoute.withName(AppRoute.installment_list_bank_screen));
        break;
      default:
        Get.until(ModalRoute.withName(AppRoute.main_screen));
        MainController _mc = Get.find<MainController>();
        _mc.currentScreen.value = MainController.MAIN_SCREEN_NORMAL_PAY;
        _mc.currentAmount.value = "0";
        PaymentInitController initController = Get.find<PaymentInitController>();
        initController.valueInput.value = '0';
        break;
    }
  }

  onPressQuickDraw() async {
    timerAutoBack?.cancel();
    LoggerMp().writeLog('onPress Quick Draw');
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => BaseDialog(
              title: AppStrings.getString(AppStrings.titleNotice),
              widgetDesc: QuickDrawWidget(feeQuickDraw),
              isTwoButton: true,
              nameBtn1st: AppStrings.getString(AppStrings.cancel),
              nameBtn2nd: AppStrings.getString(AppStrings.buttonConfirm),
              onPress2ndButton: () {
                LoggerMp().writeLog('onPress Confirm Quick Draw');
                Get.back();
                markQuickWithDraw();
              },
            ));
  }

  onPressTransactionId() async {
    if (inputData?.serviceType == MposConstant.CARD_PAYMENT ||
        inputData?.serviceType == MposConstant.UNSIGN_CARD_PAYMENT ||
        inputData?.serviceType == MposConstant.INSTALLMENT_CARD_PAYMENT ||
        inputData?.serviceType == MposConstant.VAS_CARD_PAYMENT) {
      _appController.showLoading();
      NativeResponseModel nativeResponseModel = await NativeBridge.getInstance()
          .nativeCheckLoginLevel2(_appController.loginAccount, _appController.loginPassword);
      _appController.hideLoading();
      if (nativeResponseModel.isSuccess == true) {
        _goToDetailTransaction();
      } else {
        showModalBottomSheet(
          context: context,
          builder: (context) {
            return BottomSheetConfirmPassword(
              onPressConfirm: _onPressConFirmPassword,
            );
          },
          isScrollControlled: true,
          isDismissible: false,
        );
      }
    } else {
      _goToDetailTransaction();
    }
  }

  _onPressConFirmPassword(String password) async {
    if (_appController.userInfo!.isMacqFlow == 1) {
      _processMacqComparePass(password);
    } else {
      _processLoginBankLevel2(password);
    }
  }

  _processMacqComparePass(String password) async {
    String accountPass = await LocalStorage().getPassword();
    if (password == accountPass) {
      _goToDetailTransaction();
    } else {
      AppUtils.showDialogError(context, AppStrings.getString(AppStrings.errorPassNotMatch));
    }
  }

  _processLoginBankLevel2(String password) async {
    _appController.showLoading();
    NativeResponseModel nativeResponseModel =
        await NativeBridge.getInstance().nativeLoginLevel2(_appController.loginAccount, password);
    _appController.hideLoading();
    if (nativeResponseModel.isSuccess) {
      _goToDetailTransaction();
    } else {
      AppUtils.showDialogErrorNative(context, nativeResponseModel.error!);
    }
  }

  onPressPrint(){
    if (inputData!.serviceType == MposConstant.QR_PAYMENT) {
      printTransactionReceiptLocalQR();
    }
    else {
      printTransactionReceiptLocal();
    }
  }

  _goToDetailTransaction() async {
    if (inputData?.serviceType == MposConstant.CARD_PAYMENT ||
        inputData?.serviceType == MposConstant.UNSIGN_CARD_PAYMENT ||
        inputData?.serviceType == MposConstant.INSTALLMENT_CARD_PAYMENT ||
        inputData?.serviceType == MposConstant.VAS_CARD_PAYMENT) {
      HistoryController historyController = Get.find<HistoryController>();
      if (historyController.canVoid == null || historyController.canSettle == null) {
        _appController.showLoading();
        NativeResponseModel nativeResponseModel = await NativeBridge.getInstance().nativeGetListTransactionComplete();
        _appController.hideLoading();
        if (nativeResponseModel.isSuccess) {
          final String listTransaction = nativeResponseModel.data;
          if (listTransaction.length > 0) {
            Map? responseMap = json.decode(listTransaction);
            if (responseMap != null) {
              SettleTransactionHistoryModel itemObject = SettleTransactionHistoryModel.fromJson(responseMap as Map<String, dynamic>);
              historyController.canVoid = itemObject.canVoid;
              historyController.canSettle = itemObject.canSettle;
              PaymentItems data = PaymentItems();
              data.mId = transactionId;
              Map param = {'data': data, "isNormal": false, "isDomestic": false};
              var dataBackVar = await Get.toNamed(AppRoute.history_detail_screen, arguments: param);
              Map? dataBack = jsonDecode(dataBackVar.toString());
              if (dataBack != null && (dataBack['isQuickPress'] == true || dataBack['isCancel'] == true)) {
                onPressNewTransaction();
              }
            }
          }
        } else {
          AppUtils.showDialogErrorNative(context, nativeResponseModel.error!);
        }
      } else {
        PaymentItems data = PaymentItems();
        data.mId = transactionId;
        Map param = {'data': data, "isNormal": false, "isDomestic": false};
        var dataBackVar = await Get.toNamed(AppRoute.history_detail_screen, arguments: param);
        Map? dataBack = jsonDecode(dataBackVar.toString());
        if (dataBack != null && (dataBack['isQuickPress'] == true || dataBack['isCancel'] == true)) {
          onPressNewTransaction();
        }
      }
    } else {
      thm.Data data = thm.Data();
      data.txid = transactionId;
      Map param = {'data': data, "isNormal": true, "isDomestic": false};
      var dataBackVar = await Get.toNamed(AppRoute.history_detail_screen, arguments: param);
      Map? dataBack = jsonDecode(dataBackVar.toString());
      if (dataBack != null && (dataBack['isQuickPress'] == true || dataBack['isCancel'] == true)) {
        onPressNewTransaction();
      }
    }
  }

  printTransactionReceiptLocalQR() async {
    Map params = {
      "serviceName": "TRANSACTION_VIEW",
      "txid": transactionId,
    };
    _appController.showLoading();
    BaseResponse response = await ApiClient.instance.request<DetailTransactionHistoryModel>(
        url: ApiConstant.urlApi,
        data: json.encode(params),
        fromJsonModel: (data) => DetailTransactionHistoryModel.fromJson(data));
    _appController.hideLoading();
    if (response.result!) {
      DetailTransactionHistoryModel? detailTransactionHistoryModel = (response.data as DetailTransactionHistoryModel?);
      _printTransactionReceiptLocalQRStep2(detailTransactionHistoryModel?.rrn,
          detailTransactionHistoryModel?.issuerName, detailTransactionHistoryModel?.authCode);
    } else {
      _printTransactionReceiptLocalQRStep2(null, null, null);
    }
  }

  _printTransactionReceiptLocalQRStep2(String? refNo, String? qrName, String? authCode) async {
    if (imageB64.isEmpty) {
      // NativeResponseModel nativeResponseModelTID = await NativeBridge.getInstance().nativeGetTID();
      // NativeResponseModel nativeResponseModelMID = await NativeBridge.getInstance().nativeGetMID();
      NativeResponseModel nativeResponseModel = await NativeBridge.getInstance().nativeGetTidAndMid();
      Map data = jsonDecode(nativeResponseModel.data);
      String tid = data['tid'] ?? '';
      String mid = data['mid'] ?? '';

      DateTime dateTime = _dateTimePrintDefault;
      if (inputData!.paymentData != null && !isNullEmpty(inputData!.paymentData!['createdDate'])) {
        dateTime =
            DateTime.fromMillisecondsSinceEpoch((int.tryParse(inputData!.paymentData!['createdDate']) ?? 0), isUtc: true)
                .add(Duration(hours: 7));
      }
      _appController.showLoading();
      tempPrinterWidget.value = Container(
        child: TempPrinterBillQrWidget(
          printTID: tid,
          printMID: mid,
          printRef: refNo,
          printAmount: amountPayment,
          printDate: DateFormat('dd/MM/yyyy').format(dateTime),
          printTime: DateFormat('HH:mm:ss').format(dateTime),
          printMCName: _appController.userInfo?.businessName,
          printMCAddress: _appController.userInfo?.businessAddress,
          printTxid: transactionId,
          printDes: _appController.paymentInfoSession?.description,
          printQRName: qrName ?? _appController.paymentInfoSession?.selectedQrSource?.longNameChild ?? '',
          printAuthCode: authCode,
          screenshotController: screenshotController,
        ),
      );
      screenshotController
          .capture(
        delay: Duration(seconds: 2),
        pixelRatio: 1.0,
      )
          .then((Uint8List? captureImage) async {
        imageB64 = base64Encode(captureImage!);
        _callNativePrint(imageB64);
        // await NativeBridge.getInstance().nativePrintBase64(imageB64);
        // // await NativeBridge.getInstance().nativePrintPush(4);
        // tempPrinterWidget.value = Container();
        _appController.hideLoading();
      }, onError: (e) {
        tempPrinterWidget.value = Container();
        _appController.hideLoading();
      });
    } else {
      _callNativePrint(imageB64);
      // await NativeBridge.getInstance().nativePrintBase64(imageB64);
      // // await NativeBridge.getInstance().nativePrintPush(4);
      // tempPrinterWidget.value = Container();
      _appController.hideLoading();
    }
  }

  printTransactionReceiptLocal() async {
    timerAutoBack?.cancel();
    countDownAutoBack.value = 0;
    if (imageB64.isEmpty) {
      // NativeResponseModel nativeResponseModelTID = await NativeBridge.getInstance().nativeGetTID();
      // NativeResponseModel nativeResponseModelMID = await NativeBridge.getInstance().nativeGetMID();
      // Map dataTidMid = jsonDecode(nativeResponseModel.data);

      Map userCard = inputData!.paymentData!['userCard'];
      Map result = inputData!.paymentData!['result'];
      String transactionDate = userCard['transactionDate'];
      DateTime dateTime =
      DateTime.fromMillisecondsSinceEpoch((int.tryParse(transactionDate) ?? 0), isUtc: true).add(Duration(hours: 7));
      _appController.showLoading();
      tempPrinterWidget.value = Container(
        child: TempPrinterBillWidget(
          printTID: _printTid,
          printMID: _printMid,
          printRef: _printRef,
          printInvoice: _printInvoice,
          printBatch: _printBatch,
          printApprove: _printApprove,
          printPan: userCard['pan'],
          printHolder: userCard['cardHolderName'],
          printType: userCard['applicationLabel'],
          printAmount: int.parse(userCard['amountAuthorized']?.toString() ?? '0'),
          printDate: DateFormat('dd/MM/yyyy').format(dateTime),
          printTime: DateFormat('HH:mm:ss').format(dateTime),
          printSign: userCard['userSignature'],
          printMCName: _appController.userInfo?.businessName,
          printMCAddress: _appController.userInfo?.businessAddress,
          printTxid: result['transactionId'],
          printDes: _appController.paymentInfoSession?.description,
          printIsVoid: false,
          isTransSkipSignature: isTransSkipSignature.value,
          screenshotController: screenshotController,
        ),
      );
      screenshotController.capture(
        delay: Duration(seconds: 1),
        pixelRatio: 1.0,
      ).then((Uint8List? captureImage) async {
        _appController.hideLoading();
        imageB64 = base64Encode(captureImage!);
        _callNativePrint(imageB64);
        // await NativeBridge.getInstance().nativePrintBase64(imageB64);
        // // await NativeBridge.getInstance().nativePrintPush(4);
        // tempPrinterWidget.value = Container();
        // // _appController.hideLoading();
      }, onError: (e) {
        tempPrinterWidget.value = Container();
        // _appController.hideLoading();
      });
    } else {
      _callNativePrint(imageB64);
      // await NativeBridge.getInstance().nativePrintBase64(imageB64);
      // // await NativeBridge.getInstance().nativePrintPush(4);
      // tempPrinterWidget.value = Container();
    }
  }

  _callNativePrint(String imageB64) async {
    await NativeBridge.getInstance().nativePrintBase64(imageB64);
    // await NativeBridge.getInstance().nativePrintPush(4);
    tempPrinterWidget.value = Container();
  }

  _checkQuickDraw(String tranxType, int amount) {
    if (_appController.enableQuickDraw == false) {
      return;
    }

    // check amount allow
    // check tranxType
    try {
      if ((tranxType == "1") && checkMinMaxAmount(amountPayment.toString(), _appController.userInfo!.quickWithdrawInfo!.amountMin!, _appController.userInfo!.quickWithdrawInfo!.amountMax!)) {
        allowQuickDraw.value = true;
        feeQuickDraw = AppUtils().checkFeeQuickDraw(amountPayment.toString(), _appController.userInfo!.quickWithdrawInfo!.jsonQuickWithdrawList!);
      }
    }catch (e) {
      LoggerMp().writeLog('checkQuickDraw ${e.toString()}');
    }
  }

  void markQuickWithDraw() async {
    Map params = {
      'serviceName': 'MARK_QUICK_WITHDRAW',
      'txId': transactionId,
      'muid': _appController.loginAccount,
      'merchantId': _appController.userInfo?.merchantId ?? 0,
    };
    LoggerMp().writeLog('quickWithDraw: ${json.encode(params)}');
    _appController.showLoading();
    BaseResponse response = await ApiClient.instance
        .request(url: ApiConstant.urlApi, data: json.encode(params));
    _appController.hideLoading();

    if (response.result!) {
      LoggerMp().writeLog('quickWithDraw success');
      AppUtils.showDialogAlert(context,
          description: AppStrings.getString(AppStrings.tv_quick_money_mark_success), onPress1stButton: () {
            onPressGoHome();
          });
    }else {
      LoggerMp().writeLog('quickWithDraw fail ${response.message}');
      AppUtils.showDialogError(context, '${response.code ?? ''}: ${response.message ?? ''}');
    }
  }

  void autoPrintReceipt(String type) async {
    bool isSupportPrinter = MyAppController.isSupportPrinter();
    bool deviceSupportPrinter = MyAppController.isKozenP12orN4();
    LoggerMp().writeLog("AutoPrintReceipt_WithDeviceNotSupport: $isSupportPrinter | $deviceSupportPrinter");
    if(isSupportPrinter) {
      numAutoPrint.value = await _appController.localStorage.getData(LocalStorage.KEY_AUTO_PRINT, 1);
      print('numAutoPrint ${numAutoPrint.value}');
      if (numAutoPrint.value > 0) {
        if (type == 'NORMAL') {
          printTransactionReceiptLocal();
        }else if (type == 'QR'){
          printTransactionReceiptLocalQR();
        }
        if (numAutoPrint.value == 2) {
          await Future.delayed(Duration(milliseconds: timeWaitPrintSecond));
          if (type == 'NORMAL') {
            printTransactionReceiptLocal();
          }else if (type == 'QR'){
            printTransactionReceiptLocalQR();
          }
        }
      }
    }
  }

  void soundPaymentSuccessForMposPro() async {
    if (MyAppController.isKozenMposPro()) {
      try {
        await NPTingTingSpeakerHandler.receivedCurrencyAmountSound(amountPayment, hasMerge: false);
      } catch (e) {
        TtsControl().speak(AppStrings.getString(AppStrings.msg_noti_payment_success)!.replaceFirst('%s', amountPayment.toString()));
      }
    }
  }
}

class PaymentFinishArguments {
  final String serviceType;
  final String? errorMsg;
  final Map? paymentData;
  int? autoCloseFinishScreen;

  PaymentFinishArguments(this.serviceType, this.paymentData, this.errorMsg, {this.autoCloseFinishScreen});
}
