import 'dart:async';

import 'package:cashiermodule/constants/style.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/route/app_pages.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:share/share.dart';
import 'package:url_launcher/url_launcher.dart';

import '../data/provider/logger.dart';
import '../res/image/app_images.dart';
import '../ui/theme/app_colors.dart';
import '../ui/theme/app_dimens.dart';
import '../ui/widget/base_bottom_sheet.dart';
import '../ui/widget/touchable_widget.dart';
import 'app_controller.dart';

class InstallmentLinkController extends GetxController {
  late BuildContext context;
  final MyAppController _appController = Get.find<MyAppController>();
  RxBool showMessageCopySuccess = false.obs;
  RxBool isExpireDate = false.obs;

  @override
  void onReady() {
    super.onReady();
  }

  onPressClose() {
    LoggerMp().writeLog("onPressClose");
    if (MyAppController.isKozenP12orN4()) {
      showModalBottomSheet(
        context: context,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
        ),
        builder: (context) {
          return BaseBottomSheet(
            isCloseHeader: true,
            hideCloseButton: true,
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 20, horizontal: 30),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Image.asset(AppImages.ic_scan_qr),
                  SizedBox(
                    height: 10,
                  ),
                  Text(
                    AppStrings.getString(AppStrings.msg_warning_close_qr_screen) ?? '',
                    style: style_S16_W600_BlackColor,
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(
                    height: 15,
                  ),
                  Row(
                    children: [
                      Expanded(
                        flex: 1,
                        child: TouchableWidget(
                          height: AppDimens.heightButton,
                          padding: EdgeInsets.all(0),
                          decoration: BoxDecoration(
                              color: AppColors.lightGreyText,
                              borderRadius:
                              BorderRadius.circular(AppDimens.radiusSmall)),
                          child: Text(
                            AppStrings.getString(AppStrings.skip) ?? '',
                            style: TextStyle(
                              fontSize: AppDimens.textSizeLarge20,
                              fontWeight: FontWeight.w500,
                              fontFamily: kFontFamilyBeVietnamPro,
                              color: AppColors.white,
                            ),
                          ),
                          // margin: EdgeInsets.only(left: 5, right: 5),
                          onPressed: () {
                            Get.back();
                          },
                        ),
                      ),
                      SizedBox(
                        width: 10,
                      ),
                      Expanded(
                        flex: 1,
                        child: TouchableWidget(
                          width: 50,
                          height: AppDimens.heightButton,
                          padding: EdgeInsets.all(0),
                          decoration: BoxDecoration(
                              color: AppColors.redButton,
                              borderRadius:
                              BorderRadius.circular(AppDimens.radiusSmall)),
                          child: Text(
                            AppStrings.getString(AppStrings.close) ?? '',
                            style: TextStyle(
                              fontSize: AppDimens.textSizeLarge20,
                              fontWeight: FontWeight.w500,
                              fontFamily: kFontFamilyBeVietnamPro,
                              color: AppColors.white,
                            ),
                          ),
                          // margin: EdgeInsets.only(left: 5, right: 5),
                          onPressed: () {
                            Get.until(ModalRoute.withName(AppRoute.installment_list_bank_screen));
                          },
                        ),
                      )
                    ],
                  )
                ],
              ),
            ),
          );
        },
        isScrollControlled: true,
        isDismissible: false,
      );
    }else {
      AppUtils.showDialogAlert(context,
          description: AppStrings.getString(AppStrings.installmentLinkCloseAlert),
          isTwoButton: true,
          text1stButton: AppStrings.getString(AppStrings.cancel),
          text2ndButton: AppStrings.getString(AppStrings.close), onPress2ndButton: () {
            Get.until(ModalRoute.withName(AppRoute.installment_list_bank_screen));
          });
    }
  }

  void onPressCopy() async {
    await Clipboard.setData(ClipboardData(text: _appController.installmentInfoSession?.linkCheckout ?? ''));
    if (!showMessageCopySuccess.value) {
      showMessageCopySuccess.value = true;
      Timer(Duration(seconds: 3), () {
        showMessageCopySuccess.value = false;
      });
    }
  }

  void onPressShare() {
    Share.share(_appController.installmentInfoSession?.linkCheckout ?? '');
  }

  void onPressLink() async {
    String url = _appController.installmentInfoSession?.linkCheckout ?? '';
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      AppUtils.showDialogError(context, AppStrings.getString(AppStrings.errorOpenUrl));
    }
  }

  void showPopUpExpireDate() {
    isExpireDate.value = true;
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
      ),
      builder: (context) {
        return BaseBottomSheet(
          isCloseHeader: true,
          hideCloseButton: true,
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 20, horizontal: 30),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Image.asset(AppImages.ic_scan_qr),
                SizedBox(
                  height: 10,
                ),
                Text(
                  "Đơn hàng hết hạn & vẫn chưa được khách hàng thanh toán",
                  style: style_S16_W600_BlackColor,
                  textAlign: TextAlign.center,
                ),
                SizedBox(
                  height: 15,
                ),
                Row(
                  children: [
                    Expanded(
                      flex: 1,
                      child: TouchableWidget(
                        width: 50,
                        height: AppDimens.heightButton,
                        padding: EdgeInsets.all(0),
                        decoration: BoxDecoration(
                            color: AppColors.redButton,
                            borderRadius:
                            BorderRadius.circular(AppDimens.radiusSmall)),
                        child: Text(
                          "Tạo đơn hàng mới",
                          style: TextStyle(
                            fontSize: AppDimens.textSizeLarge20,
                            fontWeight: FontWeight.w500,
                            fontFamily: kFontFamilyBeVietnamPro,
                            color: AppColors.white,
                          ),
                        ),
                        // margin: EdgeInsets.only(left: 5, right: 5),
                        onPressed: () {
                          Get.until(ModalRoute.withName(AppRoute.main_screen));
                        },
                      ),
                    )
                  ],
                )
              ],
            ),
          ),
        );
      },
      isScrollControlled: true,
      isDismissible: false,
    );
  }
}
