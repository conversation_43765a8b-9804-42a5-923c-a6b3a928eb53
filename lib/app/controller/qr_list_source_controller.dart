import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/data/model/mp_data_login_model.dart';
import 'package:mposxs/app/route/app_pages.dart';

import 'app_controller.dart';

class QrListSourceController extends GetxController {
  BuildContext? context;
  List<MPBankQR> listBankQr = [];
  RxList<MPBankQR> listBankQrExpanded = RxList<MPBankQR>();
  final MyAppController _appController = Get.find<MyAppController>();
  String? des = '';

  @override
  void onInit() {
    listBankQr = _appController?.userInfo?.listBankQR ?? [];
    if (listBankQr.length > 0) {
      listBankQrExpanded.add(listBankQr[0]);
    }
    des = Get.arguments;
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  onPressItemSource(MPQrChild item, MPBankQR bankQr) async {
    Get.toNamed(AppRoute.qr_enter_info_screen, arguments: {'qrGroup': bankQr, 'qrSource': item, 'des': des});
  }

  onPressQrGroup(MPBankQR bankQr) {
    if (listBankQrExpanded.contains(bankQr)) {
      listBankQrExpanded.remove(bankQr);
    } else {
      listBankQrExpanded.add(bankQr);
    }
  }
}
