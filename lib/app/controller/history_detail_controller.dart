import 'dart:convert';
import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mposxs/app/controller/history_controller.dart';
import 'package:mposxs/app/controller/payment_finish_controller.dart';
import 'package:mposxs/app/data/model/base_response.dart';
import 'package:mposxs/app/data/model/detail_transaction_history_model.dart';
import 'package:mposxs/app/data/model/settle_transaction_detail_model.dart';
import 'package:mposxs/app/data/model/settle_transaction_history_model.dart';
import 'package:mposxs/app/data/model/transaction_history_model.dart' as thm;
import 'package:mposxs/app/data/provider/api_client.dart';
import 'package:mposxs/app/data/provider/local_storage.dart';
import 'package:mposxs/app/data/provider/logger.dart';
import 'package:mposxs/app/data/provider/native_bridge.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/route/app_pages.dart';
import 'package:mposxs/app/ui/screen/history/widget/bottom_sheet_send_email.dart';
import 'package:mposxs/app/ui/screen/history/widget/bottom_sheet_void_transaction.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/widget/printer/bottom_sheet_test_bill_printer.dart';
import 'package:mposxs/app/ui/widget/printer/temp_printer_bill_qr_widget.dart';
import 'package:mposxs/app/util/api_constant.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/app_validation.dart';
import 'package:mposxs/app/util/mpos_constant.dart';
import 'package:screenshot/screenshot.dart';

import '../ui/widget/base_dialog.dart';
import '../ui/widget/quick_draw_widget.dart';
import 'app_controller.dart';

class HistoryDetailController extends GetxController {
  final MyAppController _appController = Get.find<MyAppController>();
  final HistoryController _historyController = Get.find<HistoryController>();
  late BuildContext context;
  String title = AppStrings.getString(AppStrings.titleDetailTransaction) ?? '';
  RxBool isLayoutNormal = true.obs;
  bool isVaymuon = false;
  bool isNormal = false;
  Map? paramInput;
  Rx<DetailTransactionHistoryModel?> detailTransaction = DetailTransactionHistoryModel().obs;
  Rx<TransactionDetail> detailSettleTransaction = TransactionDetail().obs;
  Rx<Color> errMessageTextColor = AppColors.gray.obs;
  Rx<Color> errMessageBackground = AppColors.gray.obs;
  RxString errMessage = ''.obs;
  RxString description = ''.obs;
  Rx<Color> errVMMessageBackground = AppColors.gray.obs;
  Rx<Color> errVMMessageTextColor = AppColors.gray.obs;
  RxString errVMMessage = ''.obs;
  RxString paymentMethod = ''.obs;
  RxBool isPayInstallment = false.obs;
  RxBool isPayCashBack = false.obs;
  RxBool enableVoidTransaction = false.obs;
  RxBool enableQuickDraw = false.obs;
  RxBool enableBtnContinue = false.obs;
  // RxBool enableBtnSendEmail = false.obs;
  RxBool enableBtnSendEmail = true.obs;
  RxBool enableBtnPrint = false.obs;
  RxBool enableBtnCallSupport = false.obs;
  RxString textStatus = ''.obs;
  Rx<Color> colorStatus = AppColors.gray.obs;
  bool _isCancel = false;
  bool _needReloadList = false;
  Rx<Widget> tempPrinterWidget = Container().obs;
  final ScreenshotController screenshotController = ScreenshotController();

  @override
  void onInit() {
    paramInput = Get.arguments;
    _initUI();
    super.onInit();
  }

  _initUI() {
    if (paramInput!["isNormal"] == true) {
      thm.Data? data = paramInput!['data'] is thm.Data ? (paramInput!['data'] as thm.Data?) : null;
      if (data?.accquirer == 'MPQR') {
        title = AppStrings.getString(AppStrings.titleQrCodeTransaction) ?? '';
      } else {
        if (data?.transactionType == 'NORMAL') {
          title = AppStrings.getString(AppStrings.titleCardTransaction) ?? '';
        } else if (data?.transactionType == 'INSTALLMENT') {
          title = AppStrings.getString(AppStrings.titleInstallmentTransaction) ?? '';
        }
      }

      isLayoutNormal.value = true;
      isNormal = true;

      if (paramInput!["isDomestic"] == false) {
        enableBtnSendEmail.value = false;
        if (data != null && data.transactionPushType == "VAYMUONQR") {
          isVaymuon = true;
          isNormal = false;
        } else if (data != null && data.transactionPushType == "LINKCARD") {
          isVaymuon = false;
          isNormal = true;
        } else {
          isVaymuon = false;
          isNormal = true;
        }
      }

      // if (paramInput!["isDomestic"] == true) {
      //   enableBtnSendEmail.value = false;
      // } else {
      //   if (data != null && data.transactionPushType == "VAYMUONQR") {
      //     isVaymuon = true;
      //     isNormal = false;
      //   } else if (data != null && data.transactionPushType == "LINKCARD") {
      //     isVaymuon = false;
      //     isNormal = true;
      //   } else {
      //     isVaymuon = false;
      //     isNormal = true;
      //   }
      // }
    } else {
      PaymentItems? data = paramInput!['data'] is PaymentItems ? (paramInput!['data'] as PaymentItems?) : null;
      if (data?.trxType == '2') {
        title = AppStrings.getString(AppStrings.titleServiceTransaction) ?? '';
      } else {
        title = AppStrings.getString(AppStrings.titleCardTransaction) ?? '';
      }

      // enableBtnSendEmail.value = false;
      isVaymuon = false;
      isNormal = false;
      isLayoutNormal.value = false;
    }
  }

  @override
  void onReady() {
    super.onReady();
    _initData();
  }

  _getStatus(int? statusCode, bool isVaymuon) {
    switch (statusCode) {
      case 100:
      case 105:
        textStatus.value = AppStrings.getString(AppStrings.success) ?? '';
        colorStatus.value = AppColors.green;
        enableBtnPrint.value = true;
        break;
      case 105:
        if (isVaymuon) {
          textStatus.value = AppStrings.getString(AppStrings.labelWaitForDisbursement) ?? '';
          colorStatus.value = AppColors.orangeDark;
        } else {
          textStatus.value = AppStrings.getString(AppStrings.success) ?? '';
          colorStatus.value = AppColors.green;
        }
        break;
      case 99:
        if (isVaymuon) {
          textStatus.value = AppStrings.getString(AppStrings.canceled) ?? '';
          colorStatus.value = AppColors.redText2;
          enableBtnCallSupport.value = false;
        } else {
          textStatus.value = AppStrings.getString(AppStrings.refunded) ?? '';
          colorStatus.value = AppColors.tabUnSelected;
          enableBtnCallSupport.value = false;
        }
        break;
      case 102:
        textStatus.value = AppStrings.getString(AppStrings.canceled) ?? '';
        colorStatus.value = AppColors.redText2;
        enableBtnCallSupport.value = false;
        enableBtnPrint.value = true;
        _isCancel = true;
        break;
      case 103:
        textStatus.value = AppStrings.getString(AppStrings.labelPendingSignature) ?? '';
        colorStatus.value = AppColors.orangeDark;
        break;
      case 101:
        textStatus.value = AppStrings.getString(AppStrings.labelReversal) ?? '';
        colorStatus.value = AppColors.redText2;
        break;
      case 104:
        textStatus.value = AppStrings.getString(AppStrings.settled) ?? '';
        colorStatus.value = AppColors.blue;
        enableBtnPrint.value = true;
        break;
      case 106:
        textStatus.value = AppStrings.getString(AppStrings.settled) ?? '';
        colorStatus.value = AppColors.blue;
        break;
      case 97:
        textStatus.value = AppStrings.getString(AppStrings.failure) ?? '';
        colorStatus.value = AppColors.redText2;
        enableBtnCallSupport.value = true;
        break;
      case 98:
        textStatus.value = AppStrings.getString(AppStrings.processing) ?? '';
        colorStatus.value = AppColors.orangeDark;
        enableBtnCallSupport.value = true;
        break;
      default:
        break;
    }
  }

  String _getDescOfPayService(String desc) {
    String result = "";
    String type = "";
    if (desc.startsWith(MposConstant.PREFIX_DESCRIPTION_SERVICE_PREPAID)) {
      type = AppStrings.getString(AppStrings.prepaid) ?? '';
      desc = desc.substring(MposConstant.PREFIX_DESCRIPTION_SERVICE_PREPAID.length);
    } else if (desc.startsWith(MposConstant.PREFIX_DESCRIPTION_SERVICE_POSTPAID)) {
      type = AppStrings.getString(AppStrings.postpaid) ?? '';
      desc = desc.substring(MposConstant.PREFIX_DESCRIPTION_SERVICE_POSTPAID.length);
    } else if (desc.startsWith(MposConstant.PREFIX_DESCRIPTION_SERVICE_BUY_CARD)) {
      type = AppStrings.getString(AppStrings.phoneCard) ?? '';
      desc = desc.substring(MposConstant.PREFIX_DESCRIPTION_SERVICE_BUY_CARD.length);
    }
    result += type +
        "-" +
        (desc.startsWith(MposConstant.PREFIX_MOBILE_DESCRIPTION_SERVICE)
            ? desc.substring(MposConstant.PREFIX_MOBILE_DESCRIPTION_SERVICE.length)
            : desc);
    return result;
  }

  _handleDataDomestic() {
    bool _isServicePay = false;
    if (!isNullEmpty(detailTransaction.value?.description?.trim())) {
      String upperCaseDes = detailTransaction.value!.description!.trim();
      if (upperCaseDes.startsWith(MposConstant.PREFIX_DESCRIPTION_SERVICE_PREPAID) ||
          upperCaseDes.startsWith(MposConstant.PREFIX_DESCRIPTION_SERVICE_POSTPAID) ||
          upperCaseDes.startsWith(MposConstant.PREFIX_DESCRIPTION_SERVICE_BUY_CARD) ||
          detailTransaction.value?.applicationUsageControl == '2') {
        _isServicePay = true;
        description.value = _getDescOfPayService(upperCaseDes);
      } else if (upperCaseDes.startsWith(MposConstant.PREFIX_DESCRIPTION_INSTALLMENT)) {
        isPayInstallment.value = true;
        description.value =
            "${AppStrings.getString(AppStrings.titleInstallmentTransaction)!}: ${detailTransaction.value!.description!.trim().substring(MposConstant.PREFIX_DESCRIPTION_INSTALLMENT.length)}";
      } else if (upperCaseDes.startsWith(MposConstant.PREFIX_DESCRIPTION_CASHBACK)) {
        isPayCashBack.value = true;
        description.value =
            detailTransaction.value!.description!.trim().substring(MposConstant.PREFIX_DESCRIPTION_CASHBACK.length);
      } else {
        description.value = upperCaseDes;
      }
    } else if (detailTransaction.value?.applicationUsageControl == '2') {
      _isServicePay = true;
    }

    if ((!isNullEmptyOrFalse(_historyController.canVoid) || _appController.userInfo?.permitVoid == 1) && (paramInput!["isNormal"] == false)) {
      enableVoidTransaction.value = true;
    }

    _getStatus(detailTransaction.value!.status, false);

    if (_isServicePay == true) {
      enableVoidTransaction.value = false;
      // enableQuickDraw.value = false;
    }
    // else if (_appController.enableQuickDraw && checkMinMaxAmount(detailSettleTransaction.value?.amountAuthorized ?? '0', _appController.userInfo!.quickWithdrawInfo!.amountMin!, _appController.userInfo!.quickWithdrawInfo!.amountMax!)) {
    // // }else if (_appController.enableQuickDraw) {
    //   enableQuickDraw.value = true;
    // }

    switch (detailTransaction.value!.status) {
      case 99:
      case 101:
      case 102:
      case 104:
        enableVoidTransaction.value = false;
        enableQuickDraw.value = false;
        break;
      case 103:
        enableBtnContinue.value = true;
        enableQuickDraw.value = false;
        break;
    }

    // if (detailTransaction.value!.status == 102 || detailTransaction.value!.status == 101) {
    //   enableVoidTransaction.value = false;
    // } else if (detailTransaction.value!.status == 104) {
    //   enableVoidTransaction.value = false;
    // } else if (detailTransaction.value!.status == 103) {
    //   enableBtnContinue.value = true;
    //   // enableBtnSendEmail.value = false;
    // }
    // if (detailTransaction.value?.status == 99) {
    //   enableVoidTransaction.value = false;
    // }

    if (detailTransaction.value?.transactionType == 'INSTALLMENT') {
      paymentMethod.value = AppStrings.getString(AppStrings.installmentSwipeCard) ?? '';
    } else {
      paymentMethod.value = AppStrings.getString(AppStrings.labelScanCard) ?? '';
    }

    // thm.Data? data = paramInput!['data'] is thm.Data ? (paramInput!['data'] as thm.Data?) : null;
    // if (data?.accquirer == 'MPQR') {
    //   enableQuickDraw.value = false;
    // }else if (!((detailTransaction.value?.transactionType == 'INSTALLMENT') || (detailTransaction.value?.transactionType == 'NORMAL'))) {
    //   enableQuickDraw.value = false;
    // }
  }

  _handleDataNormal() {
    String fullDes = detailTransaction.value?.description ?? '';
    if (detailTransaction.value?.transactionPush != null &&
        detailTransaction.value?.transactionPush?.errorCode != null &&
        detailTransaction.value?.transactionPush?.errorMessage != null) {
      if (detailTransaction.value?.status == 98) {
        errMessageTextColor.value = AppColors.orangeDark;
        errMessageBackground.value = AppColors.orangeDark.withOpacity(0.1);
      } else {
        errMessageTextColor.value = AppColors.error;
        errMessageBackground.value = AppColors.error.withOpacity(0.11);
      }
      String errCode = detailTransaction.value?.transactionPush?.errorCode ?? '';
      String errMess = detailTransaction.value?.transactionPush?.errorMessage != null
          ? '- ${detailTransaction.value?.transactionPush?.errorMessage}'
          : '';
      errMessage.value = '${AppStrings.getString(AppStrings.labelErrCode)}: $errCode $errMess';
    }

    if (detailTransaction.value?.transactionPushType == 'LINKCARD') {
      String issuerBank =
          isNullEmpty(detailTransaction.value?.issuerBank) ? '' : '${detailTransaction.value?.issuerBank} - ';
      fullDes = '$issuerBank${detailTransaction.value?.description}';
    }
    if (detailTransaction.value?.transactionInstallment != null &&
        detailTransaction.value?.transactionInstallment?.periodType != null) {
      if (detailTransaction.value?.transactionInstallment?.periodType == 'month') {
        fullDes =
            '${AppStrings.getString(AppStrings.labelPaymentPeriod)} ${detailTransaction.value?.transactionInstallment?.period} ${AppStrings.getString(AppStrings.month)}${isNullEmpty(fullDes) ? '' : ' - $fullDes'}';
      } else if (detailTransaction.value?.transactionInstallment?.periodType == 'day') {
        fullDes =
            '${AppStrings.getString(AppStrings.labelPaymentPeriod)} ${detailTransaction.value?.transactionInstallment?.period} ${AppStrings.getString(AppStrings.day)}${isNullEmpty(fullDes) ? '' : ' - $fullDes'}';
      } else {
        fullDes =
            '${AppStrings.getString(AppStrings.labelPaymentPeriod)} ${detailTransaction.value?.transactionInstallment?.period}${isNullEmpty(fullDes) ? '' : ' - $fullDes'}';
      }
    }
    description.value = fullDes;

    if (detailTransaction.value?.status == 102 ||
        detailTransaction.value?.status == 99 && !isNullEmpty(detailTransaction.value?.voidReason)) {
      errVMMessage.value = '${AppStrings.getString(AppStrings.labelVoidReason)} ${detailTransaction.value?.voidReason}';
      errVMMessageTextColor.value = AppColors.error;
      errVMMessageBackground.value = AppColors.error.withOpacity(0.11);
    }
    if (detailTransaction.value?.status == 105) {
      errVMMessage.value = AppStrings.getString(AppStrings.labelStatusWaitExplain) ?? '';
      errVMMessageTextColor.value = AppColors.orangeDark;
      errVMMessageBackground.value = AppColors.orangeDark.withOpacity(0.1);
    } else if (detailTransaction.value?.status == 104) {
      errVMMessage.value = AppStrings.getString(AppStrings.labelStatusKtExplain) ?? '';
      errVMMessageTextColor.value = AppColors.blue;
      errVMMessageBackground.value = AppColors.blue.withOpacity(0.1);
    }
    _getStatus(detailTransaction.value?.status, detailTransaction.value?.transactionPushType == 'VAYMUONQR');

    if (detailTransaction.value?.accquirer == 'MPQR') {
      paymentMethod.value =
          (Get.locale!.languageCode == 'vi' ? detailTransaction.value?.issuerName! : detailTransaction.value?.issuerNameEn!)!;
    } else if (detailTransaction.value?.transactionType == 'INSTALLMENT') {
      paymentMethod.value = AppStrings.getString(AppStrings.createLink) ?? '';
    } else if (detailTransaction.value?.transactionType == 'NORMAL') {
      paymentMethod.value = AppStrings.getString(AppStrings.labelEnterCardPayment) ?? '';
    }

    // isDomesticInMVisa
    if (paramInput!["isDomestic"] == true) {
      _handleDataDomestic();
    }
  }

  _handleDataSettle() {
    bool _isServicePay = false;
    if (!isNullEmpty(detailSettleTransaction.value.itemDescription?.trim())) {
      String upperCaseDes = detailSettleTransaction.value.itemDescription!.trim();
      if (upperCaseDes.startsWith(MposConstant.PREFIX_DESCRIPTION_SERVICE_PREPAID) ||
          upperCaseDes.startsWith(MposConstant.PREFIX_DESCRIPTION_SERVICE_POSTPAID) ||
          upperCaseDes.startsWith(MposConstant.PREFIX_DESCRIPTION_SERVICE_BUY_CARD) ||
          detailSettleTransaction.value.trxType == 2) {
        _isServicePay = true;
        description.value = _getDescOfPayService(upperCaseDes);
      } else if (upperCaseDes.startsWith(MposConstant.PREFIX_DESCRIPTION_INSTALLMENT)) {
        isPayInstallment.value = true;
        description.value =
            "${AppStrings.getString(AppStrings.titleInstallmentTransaction)}: ${detailSettleTransaction.value.itemDescription!.trim().substring(MposConstant.PREFIX_DESCRIPTION_INSTALLMENT.length)}";
      } else if (upperCaseDes.startsWith(MposConstant.PREFIX_DESCRIPTION_CASHBACK)) {
        isPayCashBack.value = true;
        description.value = detailSettleTransaction.value.itemDescription!
            .trim()
            .substring(MposConstant.PREFIX_DESCRIPTION_CASHBACK.length);
      } else {
        description.value = upperCaseDes;//detailSettleTransaction.value.itemDescription?.trim();
      }
    } else if (detailSettleTransaction.value.trxType == 2) {
      _isServicePay = true;
    }

    paymentMethod.value = (detailSettleTransaction.value.udid ?? '').contains('INSTALLMENT')
        ? AppStrings.getString(AppStrings.installmentSwipeCard) ?? ''
        : AppStrings.getString(AppStrings.labelScanCard) ?? '';
    _getStatus(detailSettleTransaction.value.transactionStatus, false);

    if ((_historyController.canVoid! || _appController.userInfo?.permitVoid == 1) && (paramInput!["isNormal"] == false)) {
      enableVoidTransaction.value = true;
    }

    if (_isServicePay) {
      enableVoidTransaction.value = false;
      enableQuickDraw.value = false;
    }else {
      if (_appController.enableQuickDraw && checkMinMaxAmount(detailSettleTransaction.value?.amountAuthorized ?? '0', _appController.userInfo!.quickWithdrawInfo!.amountMin!, _appController.userInfo!.quickWithdrawInfo!.amountMax!)) {
      // if (_appController.enableQuickDraw) {
        enableQuickDraw.value = true;
      }
    }

    switch (detailSettleTransaction.value.transactionStatus) {
      case 99:
      case 101:
      case 102:
      case 104:
        enableVoidTransaction.value = false;
        enableQuickDraw.value = false;
        break;
      case 103:
        enableVoidTransaction.value = false;
        enableBtnContinue.value = true;
        enableQuickDraw.value = false;
        break;
    }

    // if (detailSettleTransaction.value.transactionStatus == 102 ||
    //     detailSettleTransaction.value.transactionStatus == 101) {
    //   enableVoidTransaction.value = false;
    // } else if (detailSettleTransaction.value.transactionStatus == 104) {
    //   enableVoidTransaction.value = false;
    // } else if (detailSettleTransaction.value.transactionStatus == 103) {
    //   enableBtnContinue.value = true;
    //   // enableBtnSendEmail.value = false;
    // }
  }

  _getDetailTransactionHistorySettle() async {
    PaymentItems? item = paramInput!["data"];
    Map params = {
      "transactionID": item?.mId,
    };
    _appController.showLoading();
    NativeResponseModel nativeResponseModel = await NativeBridge.getInstance().nativeGetTransactionDetail(params);
    _appController.hideLoading();
    if (nativeResponseModel.isSuccess) {
      final String detailSettleTransactionString = nativeResponseModel.data;
      Map responseMap = json.decode(detailSettleTransactionString);
      detailSettleTransaction.value = TransactionDetail.fromJson(responseMap as Map<String, dynamic>);
      _handleDataSettle();
    } else {
      AppUtils.showDialogErrorNative(context, nativeResponseModel.error!);
    }
  }

  _getDetailTransactionHistory() async {
    thm.Data item = paramInput!["data"];
    Map params = {
      "serviceName": "TRANSACTION_VIEW",
      "txid": item.txid,
    };
    _appController.showLoading();
    BaseResponse response = await ApiClient.instance.request<DetailTransactionHistoryModel>(
        url: ApiConstant.urlApi,
        data: json.encode(params),
        fromJsonModel: (data) => DetailTransactionHistoryModel.fromJson(data));
    _appController.hideLoading();
    if (response.result!) {
      detailTransaction.value = (response.data as DetailTransactionHistoryModel?);
      _handleDataNormal();
    } else {
      AppUtils.showDialogError(context, '${response.code ?? ''}: ${response.message ?? ''}');
    }
  }

  _initData() {
    if (paramInput!["isNormal"] == true) {
      _getDetailTransactionHistory();
    } else {
      _getDetailTransactionHistorySettle();
    }
  }

  _sendReceiptWithTransaction(String? transactionID, String? email, bool isShowSuccess) async {
    Map params = {
      "transactionID": transactionID,
      "email": email,
    };
    _appController.showLoading();
    NativeResponseModel nativeResponseModel = await NativeBridge.getInstance().nativeSendReceiptWithTransaction(params);
    _appController.hideLoading();
    if (nativeResponseModel.isSuccess) {
      if (isShowSuccess == true) {
        AppUtils.showDialogAlert(context, description: AppStrings.getString(AppStrings.success) ?? '');
      }
    } else {
      AppUtils.showDialogErrorNative(context, nativeResponseModel.error!);
    }
  }

  _checkUpdateTransToMpos() async {
    String? transactionId =
        paramInput!["isNormal"] == true ? detailTransaction.value?.txid : detailSettleTransaction.value.transactionID;
    int? amount = paramInput!["isNormal"] == true
        ? detailTransaction.value?.amount
        : int.parse(detailSettleTransaction.value.amountAuthorized!.replaceAll(',', ''));
    if (isPayInstallment.value || isPayCashBack.value) {
      Map params = {
        "serviceName": "UPDATE_TRANSACTION_STATUS",
        "udid": _appController.userInfo?.merchantId,
        "amount": amount,
        "status": 'VOIDED',
        "txId": transactionId,
        "muid": _appController.loginAccount,
      };
      _appController.showLoading();
      await ApiClient.instance.request(url: ApiConstant.urlApi, data: json.encode(params));
      _appController.hideLoading();
    } else {
      _sendReceiptWithTransaction(transactionId, _appController.userInfo?.emailMerchant, false);
    }
  }

  onPressContinueUnSignPayment() async {
    // Get.toNamed(AppRoute.continue_payment_screen, arguments: dataUnSignPayment);
    LocalStorage().setPaymentErrorUnsign(null);
    // Map callingAppMap = await LocalStorage().getDataCallingApp();

    String strDescription = description.value;
    String paymentIdentify = detailSettleTransaction.value.udid!;
    String strOrderId = '';
    if (paymentIdentify.contains('MERCHANT_INTEGRATED')) {
      List params = paymentIdentify.split('-');
      if (params.length > 1) {
        strOrderId = params[1];
      }

      // if (callingAppMap != null && callingAppMap['description'] != null) {
      //   strDescription = callingAppMap['description'];
      // } else {
        strDescription = strOrderId + '-Thanh_toan_hoa_don/_Pay_order';
      // }
    }

    Map dataUnSignPayment = {
      'amount': detailSettleTransaction.value.amountAuthorized,
      'pan': detailSettleTransaction.value.maskedPAN,
      'trxType': detailSettleTransaction.value.trxType,
      'itemDesc': detailSettleTransaction.value.itemDescription,
      'cardholderName': detailSettleTransaction.value.cardHolderName,
      'transReqId': detailSettleTransaction.value.transactionID,
      'transactionDate': detailSettleTransaction.value.transactionDate,
      'paymentIdentify': detailSettleTransaction.value.udid,
      'wfId': detailSettleTransaction.value.wfId,
      'description_payment': strDescription,
    };

    // Map nativeParams = {
    //   'paymentIdentify': detailSettleTransaction.value.udid,
    //   'description_payment': strDescription,
    // };
    NativeResponseModel nativeResponseModel =
        await NativeBridge.getInstance().nativeContinueTransactionUnsign(dataUnSignPayment);
    if (nativeResponseModel.isSuccess) {
      final Map responseMap = json.decode(nativeResponseModel.data);
      responseMap['description_payment'] = strDescription;
      Get.offAndToNamed(AppRoute.payment_finish_screen,
          arguments: PaymentFinishArguments(MposConstant.UNSIGN_CARD_PAYMENT, responseMap, null));
    } else {
      Get.offAndToNamed(AppRoute.payment_finish_screen,
          arguments: PaymentFinishArguments(
              MposConstant.UNSIGN_CARD_PAYMENT,
              null,
              AppUtils.getErrorByCode(context, nativeResponseModel.error?.code,
                  defaultMessage: nativeResponseModel.error?.message)));
    }
  }

  onPressClosePage() {
    Get.back(
        result: jsonEncode({
      'needReloadList': _needReloadList,
      'isQuickPress': false,
      'isCancel': _isCancel,
    }));
  }

  onPressQuickPayment() {
    Get.back(result: jsonEncode({'needReloadList': false, 'isQuickPress': true}));
  }

  onPressQuickDraw() async {
    String transactionId = !isLayoutNormal.value ? detailSettleTransaction.value.transactionID ?? '' : detailTransaction.value?.txid ?? '';
    String amount = !isLayoutNormal.value
        ? '${detailSettleTransaction.value.amountAuthorized ?? '0'}'
        : '${(detailTransaction.value?.amount ?? 0).toString()}';
    String feeQuickDraw = AppUtils().checkFeeQuickDraw(amount, _appController.userInfo!.quickWithdrawInfo!.jsonQuickWithdrawList!);

    LoggerMp().writeLog('onPress Quick Draw');
    showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => BaseDialog(
          title: AppStrings.titleNotice.tr,
          widgetDesc: QuickDrawWidget(feeQuickDraw),
          isTwoButton: true,
          nameBtn1st: AppStrings.cancel.tr,
          nameBtn2nd: AppStrings.buttonConfirm.tr,
          onPress2ndButton: () {
            LoggerMp().writeLog('onPress Confirm Quick Draw');
            Get.back();
            markQuickWithDraw(transactionId);
          },
        ));
  }

  void markQuickWithDraw(String transactionId) async {
    Map params = {
      'serviceName': 'MARK_QUICK_WITHDRAW',
      'txId': transactionId,
      'muid': _appController.loginAccount,
      'merchantId': _appController.userInfo?.merchantId ?? 0,
    };
    LoggerMp().writeLog('markQuickWithDraw: ${json.encode(params)}');
    _appController.showLoading();
    BaseResponse response = await ApiClient.instance
        .request(url: ApiConstant.urlApi, data: json.encode(params));
    _appController.hideLoading();

    if (response.result!) {
      LoggerMp().writeLog('quick_money_mark_success');
      AppUtils.showDialogAlert(context,
          description: AppStrings.tv_quick_money_mark_success.tr, onPress1stButton: () {
            Get.back();
          });
    }else {
      LoggerMp().writeLog('quick_money_mark_fail ${response.message}');
      AppUtils.showDialogError(context, '${response.code ?? ''}: ${response.message ?? ''}');
    }
  }

  _onPressConfirmVoid() async {
    String? transactionId;
    if (_appController.userInfo!.isMacqFlow == 1) {
      transactionId = detailSettleTransaction.value.wfId;
    } else {
      transactionId =
          paramInput!["isNormal"] == true ? detailTransaction.value?.txid : detailSettleTransaction.value.transactionID;
    }
    _appController.appendLog('select confirm void transId=$transactionId');

    Get.back();
    Map params = {
      "transactionID": transactionId,
    };
    _appController.showLoading();
    NativeResponseModel nativeResponseModel = await NativeBridge.getInstance().nativeVoidTransaction(params);
    _appController.hideLoading();
    if (nativeResponseModel.isSuccess) {
      _checkUpdateTransToMpos();
      detailSettleTransaction.value.transactionStatus = 102;
      _handleDataSettle();
      _needReloadList = true;
      _appController.appendLog('void trans success');
    } else {
      AppUtils.showDialogErrorNative(context, nativeResponseModel.error!);
      _appController.appendLog('void trans fail');
    }
  }

  onPressVoidTransaction() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
      ),
      builder: (context) {
        String amount = !isLayoutNormal.value
            ? '${AppUtils.formatCurrency(int.parse((detailSettleTransaction.value.amountAuthorized ?? '0').replaceAll(',', '')))}đ'
            : '${AppUtils.formatCurrency(detailTransaction.value?.amount ?? 0)}đ';
        String cardType = !isLayoutNormal.value
            ? detailSettleTransaction.value.applicationLabel ?? ''
            : detailTransaction.value?.issuerName ?? '';
        String cardNumber =
            !isLayoutNormal.value ? detailSettleTransaction.value.maskedPAN ?? '' : detailTransaction.value?.pan ?? '';
        String authCode = !isLayoutNormal.value
            ? detailSettleTransaction.value.approvalCode ?? ''
            : detailTransaction.value?.authCode ?? '';
        String rrnNo =
            !isLayoutNormal.value ? detailSettleTransaction.value.invoiceNumber ?? '' : detailTransaction.value?.rrn ?? '';
        String transactionId =
            !isLayoutNormal.value ? detailSettleTransaction.value.transactionID ?? '' : detailTransaction.value?.txid ?? '';
        _appController.appendLog('show void trans: amount=$amount transId=$transactionId');

        return BottomSheetVoidTransaction(
          amount: amount,
          cardType: cardType,
          cardNumber: cardNumber,
          authCode: authCode,
          rrnNo: rrnNo,
          transactionId: transactionId,
          onPressConfirmVoid: _onPressConfirmVoid,
        );
      },
      isScrollControlled: true,
      isDismissible: false,
    );
  }

  _onPressConfirmEmail(String email) {
    String? transactionId =
        paramInput!["isNormal"] == true ? detailTransaction.value?.txid : detailSettleTransaction.value.transactionID;
    _sendReceiptWithTransaction(transactionId, email, true);
  }

  onPressSendReceipt() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
      ),
      builder: (context) {
        return BottomSheetSendEmail(
          onPressConfirmEmail: _onPressConfirmEmail,
        );
      },
      isScrollControlled: true,
      isDismissible: false,
    );
  }

  printTransactionReceiptLocal(String transId) async {
    _appController.printTransactionReceipt(context, transId, _isCancel, screenshotController, tempPrinterWidget);
  }

  printTransactionReceiptLocalQR() async {
    NativeResponseModel nativeResponseModelTID = await NativeBridge.getInstance().nativeGetTID();
    NativeResponseModel nativeResponseModelMID = await NativeBridge.getInstance().nativeGetMID();
    DateTime dateTime = DateTime.fromMillisecondsSinceEpoch((detailTransaction.value?.createdDate ?? 0), isUtc: true)
        .add(Duration(hours: 7));
    _appController.showLoading();
    tempPrinterWidget.value = Container(
      child: TempPrinterBillQrWidget(
        printTID: nativeResponseModelTID?.data,
        printMID: nativeResponseModelMID?.data,
        printRef: detailTransaction.value?.rrn,
        printAmount: detailTransaction.value?.amount,
        printDate: DateFormat('dd/MM/yyyy').format(dateTime),
        printTime: DateFormat('HH:mm:ss').format(dateTime),
        printMCName: _appController.userInfo?.businessName,
        printMCAddress: _appController.userInfo?.businessAddress,
        printTxid: detailTransaction.value?.txid,
        printDes: detailTransaction.value?.description,
        printQRName: detailTransaction.value?.issuerName,
        printAuthCode: detailTransaction.value?.authCode,
        screenshotController: screenshotController,
      ),
    );
    screenshotController
        .capture(
      delay: Duration(seconds: 2),
      pixelRatio: 1.0,
    )
        .then((Uint8List? captureImage) async {
      String imageB64 = base64Encode(captureImage!);
      await NativeBridge.getInstance().nativePrintBase64(imageB64);
      // await NativeBridge.getInstance().nativePrintPush(4);
      tempPrinterWidget.value = Container();
      _appController.hideLoading();
    }, onError: (e) {
      tempPrinterWidget.value = Container();
      _appController.hideLoading();
    });
  }

  showBottomSheetBill(Widget value) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
      ),
      builder: (context) {
        return BottomSheetTestBillPrinter(widgetChild: value);
      },
      isScrollControlled: true,
      isDismissible: false,
    );
  }
}
