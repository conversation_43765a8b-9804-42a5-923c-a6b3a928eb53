import 'dart:convert';
import 'dart:io';

import 'package:cashiermodule/Utilities/Logger.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/data/model/base_response.dart';
import 'package:mposxs/app/data/provider/api_client.dart';
import 'package:mposxs/app/data/provider/local_storage.dart';
import 'package:mposxs/app/data/provider/native_bridge.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/route/app_pages.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/app_validation.dart';

import 'app_controller.dart';

class ChangePassController extends GetxController {
  late BuildContext context;
  final MyAppController _appController = Get.find<MyAppController>();
  TextEditingController textOldPassController = TextEditingController();
  TextEditingController textNewPassController = TextEditingController();
  TextEditingController textReNewPassController = TextEditingController();
  RxString errorOldPassword = ''.obs;
  RxString errorNewPassword = ''.obs;
  RxString errorReNewPassword = ''.obs;
  RxBool isShowOldPassword = false.obs;
  RxBool isShowNewPassword = false.obs;
  RxBool isShowReNewPassword = false.obs;

  @override
  void onReady() {
    super.onReady();
  }

  onPressShowHideOldPassword() {
    isShowOldPassword.value = !isShowOldPassword.value;
  }

  onPressShowHideNewPassword() {
    isShowNewPassword.value = !isShowNewPassword.value;
  }

  onPressShowHideReNewPassword() {
    isShowReNewPassword.value = !isShowReNewPassword.value;
  }

  onChangedOldPassword(text) {
    errorOldPassword.value = '';
  }

  onChangedNewPassword(text) {
    errorNewPassword.value = '';
  }

  onChangedReNewPassword(text) {
    errorReNewPassword.value = '';
  }

  String? validateInfo() {
    String? errorOldPassword;
    String? errorNewPassword;
    String? errorReNewPassword;
    String oldPassword = textOldPassController.text;
    String newPassword = textNewPassController.text;
    String reNewPassword = textReNewPassController.text;
    if (isNullEmpty(oldPassword)) {
      errorOldPassword = AppStrings.getString(AppStrings.errorOldPasswordEmpty) ?? '';
    }
    // else {
    //   String password = _appController.loginPassword;
    //   if (oldPassword != password) {
    //     errorOldPassword = AppStrings.error_3001.tr;
    //   }
    // }
    if (isNullEmpty(newPassword)) {
      errorNewPassword = AppStrings.getString(AppStrings.errorNewPasswordEmpty) ?? '';
    } else if (newPassword.length < 6) {
      errorNewPassword = AppStrings.getString(AppStrings.errorNewPasswordLength) ?? '';
    }
    if (isNullEmpty(reNewPassword)) {
      errorReNewPassword = AppStrings.getString(AppStrings.errorReNewPasswordEmpty) ?? '';
    } else if (reNewPassword != newPassword) {
      errorReNewPassword = AppStrings.getString(AppStrings.errorReNewPasswordMatch) ?? '';
    }
    this.errorOldPassword.value = errorOldPassword??'';
    this.errorNewPassword.value = errorNewPassword??'';
    this.errorReNewPassword.value = errorReNewPassword??'';
    return errorOldPassword ?? errorNewPassword ?? errorReNewPassword;
  }

  onPressChangePassword() async {
    AppUtils.hideKeyboard(context);
    if (isNullEmpty(validateInfo())) {
      Logger().write("onPressChangePassword nativeVerifySerialNumber");
      String oldPassword = textOldPassController.text;
      _appController.showLoading();

      NativeResponseModel nativeResponseModel =
            await NativeBridge.getInstance().nativeVerifySerialNumber(_appController.serialNumber, oldPassword);

      if (nativeResponseModel.isSuccess) {
        String newPassword = textNewPassController.text;
        Logger().write("onPressChangePassword ${newPassword}");
        NativeResponseModel nativeResponseModel =
              await NativeBridge.getInstance().nativeCallChangePass(oldPassword, newPassword);
        _appController.hideLoading();
        if (nativeResponseModel.isSuccess) {
          Logger().write("onPressChangePassword success");
          AppUtils.showDialogAlert(
            context,
            description: AppStrings.getString(AppStrings.messageChangePasswordSuccess) ?? '',
            onPress1stButton: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
              // _doLogout();
            },
          );
        } else {
          Logger().write("onPressChangePassword fail");
          AppUtils.showDialogErrorNative(context, nativeResponseModel.error!);
        }
      } else {
        _appController.hideLoading();
        String msgError = nativeResponseModel.error!.message??'';
        if(msgError.startsWith('3043')) {
          this.errorOldPassword.value = AppStrings.getString(AppStrings.error_3001) ?? '';
        } else{
          AppUtils.showDialogErrorNative(context, nativeResponseModel.error!);
        }
      }
    }
  }

  _doLogout() async {
    Map params = {
      "serviceName": "GATEWAY_MERCHANT_LOGOUT",
      "merchantId": _appController?.userInfo?.merchantId,
      "readerSerial": _appController?.serialNumber,
      "deviceIdentifier": '',
      "os": Platform.isAndroid ? 'Android' : 'iOS',
      "muid": _appController?.loginAccount,
    };

    _appController.showLoading();
    BaseResponse response = await ApiClient.instance.request(data: json.encode(params));
    _appController.hideLoading();
    if (response.result!) {
      NativeBridge.getInstance().nativeClearDataAuto();
      LocalStorage().clearAll();
      _appController.loginAccount = '';
      _appController.serialNumber = '';
      MyAppController.readerType = -1;
      _appController.userInfo = null;
      _appController.deviceIdentifier = '';
      Get.offAllNamed(AppRoute.login_screen);
    } else {
      AppUtils.showDialogError(context, '${response.code ?? ''}: ${response.message ?? ''}');
    }
  }
}
