import 'dart:convert';
import 'dart:developer' as dev;

import 'package:cashiermodule/Class/brightness_control.dart';
import 'package:cashiermodule/Pages/push_payment_page/pass_code_page/passs_code_page.dart';
import 'package:cashiermodule/constants/style.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/controller/main_controller.dart';
import 'package:mposxs/app/data/provider/local_storage.dart';
import 'package:mposxs/app/data/provider/logger.dart';
import 'package:mposxs/app/data/provider/native_bridge.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/screen/settings/bottomsheet_setting_language.dart';
import 'package:mposxs/app/ui/screen/settings/bottomsheet_setting_order_payment.dart';
import 'package:mposxs/app/ui/screen/settings/bottomsheet_setting_sound_tingbox.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/mpos_constant.dart';
import 'package:mposxs/app/util/np_tingting_speak_handler.dart';
import 'package:package_info/package_info.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import '../data/model/base_response.dart';
import '../data/provider/api_client.dart';
import '../route/app_pages.dart';
import '../ui/theme/app_colors.dart';
import '../ui/widget/base_bottom_sheet.dart';
import '../ui/widget/common_button.dart';
import '../util/api_constant.dart';

class SettingsController extends GetxController {
  BuildContext? context;

  RxString appVer = ''.obs;

  final MyAppController _appController = Get.find<MyAppController>();
  final MainController _mainController = Get.find<MainController>();

  RxBool isDisableStatusBar = false.obs;
  RxBool isDisableNavBar = false.obs;
  RxBool isAutoDismissDlg = false.obs;
  RxBool showDefaultVietQr = false.obs;
  RxBool isHoldScreenQr = false.obs;

  @override
  Future<void> onInit() async {
    super.onInit();
    PackageInfo packageInfo = await PackageInfo.fromPlatform();
    appVer.value = packageInfo.version;

    if (_appController.isShowAdvancedSettings) {
      isDisableStatusBar.value = await LocalStorage().getData(LocalStorage.disableStatusBar, false);
      isDisableNavBar.value = !(await LocalStorage().getData(LocalStorage.displayNavbar, true));
      isAutoDismissDlg.value = await NativeBridge.getInstance().getIsAutoDismissDlg();
    }

    showDefaultVietQr.value = await LocalStorage().getData(LocalStorage.isShowDefaultVietQr, true);
    isHoldScreenQr.value = await LocalStorage().getData(LocalStorage.isHoldScreenQr, false);
    print('show default viet qr ${showDefaultVietQr.value}');
  }

  @override
  void onReady() {
    super.onReady();
  }

  onPressLanguage() {
    _appController.changeLanguageApp(context);
  }

  onPressSoundTingbox() async {
    dev.log('---onPressSoundTingbox--');
    String currLangSound = await _appController.localStorage.getLanguageSoundTingbox();
    if (currLangSound.isNotEmpty) {
      List<ObjSettingSelect> listLanguage = [
        ObjSettingSelect('Giọng 1', NPTingTingSpeakerHandler.soundNorth),
        ObjSettingSelect('Giọng 2', NPTingTingSpeakerHandler.soundSouth),
      ];

      showModalBottomSheet(
        context: context!,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
        ),
        builder: (context) => SettingSoundTingbox(
          titleScreen: 'Giọng đọc',
          onSelectItem: (settingSelected){
            AppUtils.log('select item ${settingSelected.value}');
            NPTingTingSpeakerHandler.playDemoVoice(settingSelected.value);
            // handlerSelectLanguage(settingSelected.value);
            currLangSound = settingSelected.value;
          },
          onPressConfirm: () async {
            await _appController.localStorage.setLanguageSoundTingbox(currLangSound);
          },
          listSetting: listLanguage,
          valueSelected: ObjSettingSelect('', currLangSound),
        ),
        isScrollControlled: true,
        isDismissible: false,
      );
    }
  }

  onPressLogout(){
    AppUtils.showDialogAlert(
      context!,
      description: AppStrings.getString(AppStrings.messageConfirmLogout),
      isTwoButton: true,
      text1stButton: AppStrings.getString(AppStrings.cancel),
      text2ndButton: AppStrings.getString(AppStrings.ok),
      onPress2ndButton: () {
        LoggerMp().writeLog('doLogout');
        LoggerMp().processPushLog();
        Get.back();
        _mainController.doLogout();
      },
    );
  }

  onPressAccountProtect() async {
    Get.back();
    Get.toNamed(AppRoute.change_pass_screen);
  }

  onPressPrinter() async {
    List<ObjSettingSelect> listTypePrints = [
      ObjSettingSelect(AppStrings.getString(AppStrings.setting_auto_print_none)!, 0),
      ObjSettingSelect(AppStrings.getString(AppStrings.setting_auto_print_1)!, 1),
      ObjSettingSelect(AppStrings.getString(AppStrings.setting_auto_print_2)!, 2),
      ObjSettingSelect(AppStrings.getString(AppStrings.setting_auto_print_3)!, 3),
    ];

    int selected = await _appController.localStorage.getData(LocalStorage.KEY_AUTO_PRINT, MposConstant.AUTO_PRINT_DEFAULT);

    AppUtils.log('selected value= ${_appController.localStorage.getData(LocalStorage.KEY_AUTO_PRINT, MposConstant.AUTO_PRINT_DEFAULT)}');

    showModalBottomSheet(
      context: context!,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
      ),
      builder: (context) => SettingLanguage(
        titleScreen: AppStrings.getString(AppStrings.setting_print),
        onSelectItem: (settingSelected){
          Get.back();
          AppUtils.log('select item ${settingSelected.value}');
          _appController.localStorage.saveData(LocalStorage.KEY_AUTO_PRINT, settingSelected.value);
          NativeBridge.getInstance().nativeSetPrintMoreReceipt(settingSelected.value.toString());
        },
        listSetting: listTypePrints,
        valueSelected: ObjSettingSelect('', selected)),
      isScrollControlled: true,
      isDismissible: false,
    );
  }

  onPressOrderTypePay(){
    showModalBottomSheet(
      context: context!,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
      ),
      builder: (context) => SettingOrderPayment(
      ),
      isScrollControlled: true,
      isDismissible: false,
    );
  }

  void onPressSettingDisableNavigationBar({bool? disableStatusBar, bool? disableNavbar}) {
    if (disableStatusBar != null) {
      LoggerMp().writeLog('disableStatusBar: ${disableStatusBar}');
      LocalStorage().saveData(LocalStorage.disableStatusBar, disableStatusBar);
      isDisableStatusBar.value = disableStatusBar;
      NativeBridge.getInstance().setDisableStatusBar(disableStatusBar);
    }
    if (disableNavbar != null) {
      LoggerMp().writeLog('disableNavbar: ${disableNavbar}');
      isDisableNavBar.value = disableNavbar;
      LocalStorage().saveData(LocalStorage.displayNavbar, !disableNavbar);
      NativeBridge.getInstance().setDisplayNavbar(!disableNavbar);
    }
  }

  void onPressAutoDismissDlg(bool autoDismissDlg) {
    LoggerMp().writeLog('autoDismissDlg: $autoDismissDlg');
    isAutoDismissDlg.value = autoDismissDlg;
    NativeBridge.getInstance().setIsAutoDismissDlg(autoDismissDlg);
  }

  void onPressShowWaitTransScreen() {
    LoggerMp().writeLog('show wait trans screen:');
    Get.to(PassCodePage(
      autoCloseKeyboard: true,
      confirmLogoutEnable: false,
      localPassword: _mainController.pwWaitScreen,
      onPressConfirm: (value) {
        if (value.isNotEmpty){
          _mainController.pwWaitScreen = value;
          _mainController.isShowWaitingTcpScreen.value = true;
          BrightnessControl().initScreens([AppRoute.main_screen]);
          _mainController.setBrightnessControl(0, timeout: 5);
          LocalStorage().saveData(LocalStorage.isShowWaitingTcpScreen, true);
          LocalStorage().saveData(LocalStorage.pwWaitingTcpScreen, value);
          Get.back();
        }
      },
    ));
  }

  void onPressChangeHoldLightScreen() async {
    isHoldScreenQr.value = !isHoldScreenQr.value;
    await LocalStorage().saveData(LocalStorage.isHoldScreenQr, isHoldScreenQr.value);

    if (isHoldScreenQr.value) {
      WakelockPlus.enable();
    } else {
      WakelockPlus.disable();
    }
  }

  void onPressSelectDefautQr() {
    showModalBottomSheet(
      context: context!,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
      ),
      builder: (context) => BaseBottomSheet(
        child: Container(
          padding: EdgeInsets.all(10),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Image.asset(_appController.isShowVietQr.value ? AppImages.ic_qr_ag_on : AppImages.ic_qr_ag_off),
              SizedBox(height: 20,),
              Text('${_appController.isShowVietQr.value ? AppStrings.getString(AppStrings.tv_enable_display_ag_qr) : AppStrings.getString(AppStrings.tv_disable_display_ag_qr)}', style: style_S18_W600_BlackColor,),
              SizedBox(height: 20,),
              Text('${_appController.isShowVietQr.value
                  ? AppStrings.getString(AppStrings.tv_des_enable_display_ag_qr)
                  : AppStrings.getString(AppStrings.tv_des_disable_display_ag_qr)}', style: style_S16_W400_BlackColor, textAlign: TextAlign.center,),
              SizedBox(height: 20,),
              CommonButton(
                onPressed: () async {
                  Map params = {
                    'serviceName': 'CHANGE_STATIC_QR_CODE',
                    'merchantId': _appController.userInfo?.merchantId,
                    'muid': _appController.loginAccount,
                    'qrType': _appController.isShowVietQr.value == true ? 'LINKCARD' : 'VAQR',
                  };
                  BaseResponse response = await ApiClient.instance
                      .request(url: ApiConstant.urlApi, data: json.encode(params));
                  if (response.result!) {
                    _appController.saveShowDefaultVietQr(!_appController.isShowVietQr.value);
                    showDefaultVietQr.value = _appController.isShowVietQr.value;
                    Get.back();
                  }
                },
                color: AppColors.redButton,
                textColor: AppColors.white,
                textSize: 18,
                title: AppStrings.getString(AppStrings.ok)!.toUpperCase(),
                minWidth: MediaQuery.of(context).size.width - 30,
                elevation: 0,
                height: 50,
              )
            ],
          ),
        ),
      ),
      isScrollControlled: true,
    );
  }
}
