import 'dart:convert';

import 'package:flutter/widgets.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/payment_finish_controller.dart';
import 'package:mposxs/app/data/model/base_response.dart';
import 'package:mposxs/app/data/provider/api_client.dart';
import 'package:mposxs/app/data/provider/native_bridge.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/route/app_pages.dart';
import 'package:mposxs/app/util/api_constant.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/app_validation.dart';
import 'package:mposxs/app/util/mpos_constant.dart';
import 'package:uuid/uuid.dart';

import '../data/model/data_session.dart';
import 'app_controller.dart';

class InstallmentConfirmController extends GetxController {
  BuildContext? context;
  final MyAppController _appController = Get.find<MyAppController>();
  TextEditingController textDesController = TextEditingController();
  TextEditingController textPhoneController = TextEditingController();
  TextEditingController textEmailController = TextEditingController();
  TextEditingController textIdentityController = TextEditingController();
  RxString errorTextDes = ''.obs;
  RxString errorTextPhone = ''.obs;
  RxString errorTextEmail = ''.obs;
  RxString errorTextIdentity = ''.obs;
  RxBool keyboardVisible = false.obs;

  @override
  void onInit() {
    var keyboardVisibilityController = KeyboardVisibilityController();
    keyboardVisibilityController.onChange.listen((bool visible) {
      print('keyboard show=$visible');
      keyboardVisible.value = visible;
    });
    String? des = Get.arguments;
    if (!isNullEmpty(des)) {
      textDesController.text = des!;
    }
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  onChangedDes(text) {
    errorTextDes.value = '';
  }

  onChangedPhone(text) {
    String formattedText = AppUtils.formatPhoneNumber(text.replaceAll(' ', ''))!;
    textPhoneController.value =
        TextEditingValue(text: formattedText, selection: TextSelection.collapsed(offset: formattedText.length));
    errorTextPhone.value = '';
  }

  onChangedEmail(text) {
    errorTextEmail.value = '';
  }

  onChangedIdentity(text) {
    errorTextIdentity.value = '';
  }

  _callNativeMethodScanCard(params) async {
    _appController.showLoading();
    NativeResponseModel nativeResponseModel = await NativeBridge.getInstance().nativePaymentScanCard(params);
    _appController.hideLoading();
    if (nativeResponseModel.isSuccess) {
      final Map responseMap = json.decode(nativeResponseModel.data);
      responseMap['emailSendReceipt'] = textEmailController.text;
      Get.toNamed(AppRoute.payment_finish_screen,
          arguments: PaymentFinishArguments(MposConstant.INSTALLMENT_CARD_PAYMENT, responseMap, null));
    } else {
      Get.toNamed(AppRoute.payment_finish_screen,
          arguments: PaymentFinishArguments(
              MposConstant.INSTALLMENT_CARD_PAYMENT,
              null,
              AppUtils.getErrorByCode(context, nativeResponseModel.error?.code,
                  defaultMessage: nativeResponseModel.error?.message)));
    }
  }

  String generateUdidPayment() {
    return 'INSTALLMENT-${'%1s Installment: %2s Months/%1s Tra gop: %2s Thang-'
        .replaceAll('%1s', _appController.installmentInfoSession?.selectedBank?.bankName??'')
        .replaceAll('%2s', _appController.installmentInfoSession?.selectedPeriod?.period??'')
        .replaceAll(' ', '_')}|${Uuid().v4()}';
  }

  void checkInfoInstallmentOrMvisa(String udid) async {
    Map installmentInfo = {
      'installmentOutId': _appController.installmentInfoSession?.selectedPeriod?.installmentOutId,
      'amount': _appController.installmentInfoSession?.finalAmountPay,
      'customerMobile': textPhoneController.text.replaceAll(' ', ''),
      'checkFeeChange': _appController.installmentInfoSession?.allowChangeFee == true ? '1' : '0',
      'checkFeeTrans': _appController.installmentInfoSession?.enableFeeTrans == true ? '1' : '0',
      'checkFeeInstallment': _appController.installmentInfoSession?.enableFeeInstallment == true ? '1' : '0',
      'issuerCode': _appController.installmentInfoSession?.selectedCardTypeMap['type'],
      'originalAmount': _appController.installmentInfoSession?.amount,
      'idCardNumber': textIdentityController.text,
    };
    Map params = {
      'serviceName': 'PREPARE_TRANSACTION',
      'udid': udid,
      'deviceIdentifier': '',
      'muid': _appController.loginAccount,
      'installmentInfo': installmentInfo,
      'merchantId': _appController.userInfo?.merchantId ?? 0,
    };
    _appController.showLoading();
    BaseResponse response = await ApiClient.instance.request(url: ApiConstant.urlApi, data: json.encode(params));
    _appController.hideLoading();
    if (response.result!) {
      Map nativeParams = {
        'amount': _appController.installmentInfoSession?.finalAmountPay.toString(),
        'email': textEmailController.text,
        'description': 'INSTALLMENT-' + (_appController.installmentInfoSession?.selectedBank?.bankName??'')
            + ' Installment: ' + (_appController.installmentInfoSession?.selectedPeriod?.period??'')
            + ' Months/' + (_appController.installmentInfoSession?.selectedBank?.bankName??'')
            + ' Tra gop: ' + (_appController.installmentInfoSession?.selectedPeriod?.period??'')
            + ' Thang-' + textDesController.text,
        'paymentID': udid,
      };
      _appController.paymentInfoSession = PaymentInfoSession()..description = textDesController.text;
      _callNativeMethodScanCard(nativeParams);
    } else {
      AppUtils.showDialogError(context!, '${response.code ?? ''}: ${response.message ?? ''}');
    }
  }

  onPressCreateLink() async {
    String errorDes = "";
    String des = textDesController.text;
    // if (_appController.userInfo?.requiredInsDescription == true && isNullEmpty(des)) {
    //   errorDes = AppStrings.descriptionEmpty.tr;
    // }
    if (!isNullEmpty(des) && des.length < 5) {
      errorDes = AppStrings.getString(AppStrings.errorInvalidDescriptionLength)!;
    }
    errorTextDes.value = errorDes;
    if (!isNullEmpty(errorDes)) {
      return;
    }
    String udid = generateUdidPayment();
    Map installmentInfo = {
      'installmentOutId': _appController.installmentInfoSession?.selectedPeriod?.installmentOutId,
      'amount': _appController.installmentInfoSession?.finalAmountPay,
      'bankName': _appController.installmentInfoSession?.selectedBank?.bankName,
      'cycle': _appController.installmentInfoSession?.selectedPeriod?.period,
      'cardType': _appController.installmentInfoSession?.selectedCardTypeMap['code'],
      'customerInstallmentFee': _appController.installmentInfoSession?.enableFeeInstallment == true
          ? _appController.installmentInfoSession?.feeInstallment
          : null,
      'customerPaymentFee': _appController.installmentInfoSession?.enableFeeTrans == true
          ? _appController.installmentInfoSession?.feeCard
          : null,
      'originalAmount': _appController.installmentInfoSession?.amount,
    };
    Map params = {
      'serviceName': 'PREPARE_TRANSACTION',
      'paymentMethod': 'LINKCARD',
      'udid': udid,
      'merchantId': _appController.userInfo?.merchantId ?? 0,
      'customerEmail': '',
      'customerMobile': '',
      'customerIdentify': '',
      'description': textDesController.text,
      'installmentInfo': installmentInfo,
      'muid': _appController.loginAccount,
    };
    _appController.showLoading();
    BaseResponse response = await ApiClient.instance.request(url: ApiConstant.urlApi, data: json.encode(params));
    _appController.hideLoading();
    if (response.result!) {
      _appController.installmentInfoSession!.linkCheckout = response.data['linkCheckout'];
      _appController.installmentInfoSession!.minuteExpired = int.tryParse(response.data['minuteExpired']) ?? 0;
      Get.toNamed(AppRoute.installment_link_screen);
    } else {
      AppUtils.showDialogError(context!, '${response.code ?? ''}: ${response.message ?? ''}');
    }
  }

  onPressScanCard() {
    String? errorPhone;
    String? errorEmail;
    String? errorDes;
    String? errorIdentity;
    String phone = textPhoneController.text;
    String email = textEmailController.text;
    String des = textDesController.text;
    String identity = textIdentityController.text;

    if (isNullEmpty(phone)) {
      errorPhone = AppStrings.getString(AppStrings.emptyPhone)!;
    } else {
      errorPhone = checkValidPhone(context, phone);
    }
    if (!isNullEmpty(email)) {
      errorEmail = checkValidEmail(context, email);
    }
    // if (_appController.userInfo?.requiredInsDescription == true && isNullEmpty(des)) {
    //   errorDes = AppStrings.descriptionEmpty.tr;
    // }
    if (!isNullEmpty(des) && des.length < 5) {
      errorDes = AppStrings.getString(AppStrings.errorInvalidDescriptionLength)!;
    }
    if (_appController.installmentInfoSession!.selectedBank?.idCardNumber == 'REQUIRED' && isNullEmpty(identity)) {
      errorIdentity = AppStrings.getString(AppStrings.emptyIdentity)!;
    }
    if (!isNullEmpty(identity)) {
      RegExp regex = RegExp("^[A-Za-z0-9_-]*\$");
      if (!regex.hasMatch(identity) || identity.length < 9) {
        errorIdentity = AppStrings.getString(AppStrings.invalidIdentity)!;
      }
    }
    errorTextPhone.value = errorPhone??'';
    errorTextEmail.value = errorEmail??'';
    errorTextDes.value = errorDes??'';
    errorTextIdentity.value = errorIdentity??'';
    if (!isNullEmpty(errorPhone) || !isNullEmpty(errorEmail) || !isNullEmpty(errorDes) || !isNullEmpty(errorIdentity)) {
      return;
    }
    String udid = generateUdidPayment();
    checkInfoInstallmentOrMvisa(udid);
  }
}
