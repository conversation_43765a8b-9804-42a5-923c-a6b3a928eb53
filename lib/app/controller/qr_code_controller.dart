import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/payment_finish_controller.dart';
import 'package:mposxs/app/data/model/base_response.dart';
import 'package:mposxs/app/data/model/mp_data_login_model.dart';
import 'package:mposxs/app/data/provider/api_client.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/route/app_pages.dart';
import 'package:mposxs/app/util/api_constant.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/mpos_constant.dart';

import 'app_controller.dart';

class QrCodeController extends GetxController {
  late BuildContext context;
  RxBool enableCheck = true.obs;
  final MyAppController _appController = Get.find<MyAppController>();
  MPBankQR? qrGroup;

  @override
  void onInit() {
    // Nguồn QR mới ko có field checkStatus, đặt mặc định là true
    // enableCheck.value = _appController.paymentInfoSession.selectedQrSource?.checkStatus == '1';
    qrGroup = Get.arguments['qrGroup'];
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  _functionOther() {
    _appController.paymentInfoSession = null;
    Get.until(ModalRoute.withName(AppRoute.main_screen));
  }

  _functionClose() {
    Get.back();
  }

  onPressClose() {
    if (enableCheck.value) {
      onPressCheck(_functionClose);
    } else {
      _functionClose();
    }
  }

  onPressOther() {
    if (enableCheck.value) {
      onPressCheck(_functionOther);
    } else {
      _functionOther();
    }
  }

  onPressCheck(Function? callback) async {
    Map params = {
      'serviceName': 'TRANSACTION_PUSH_CHECK_STATUS',
      'udid': _appController.paymentInfoSession?.udid,
      'type': _appController.paymentInfoSession?.selectedQrSource?.qrType,
    };
    _appController.showLoading();
    BaseResponse response =
        await ApiClient.instance.request(url: ApiConstant.urlCheckStatusMVISA, data: json.encode(params));
    _appController.hideLoading();
    if (response.result!) {
      String? transactionStatus = response.data['transactionStatus'];
      if (transactionStatus == 'APPROVED') {
        Get.toNamed(AppRoute.payment_finish_screen,
            arguments: PaymentFinishArguments(MposConstant.QR_PAYMENT, response.data, ''));
      } else {
        if (callback != null) {
          AppUtils.showDialogAlert(
            context,
            title: AppStrings.getString(AppStrings.titleNotice) ?? '',
            description: AppStrings.getString(AppStrings.stillClose) ?? '',
            isTwoButton: true,
            text1stButton: AppStrings.getString(AppStrings.cancel) ?? '',
            text2ndButton: AppStrings.getString(AppStrings.close) ?? '',
            onPress2ndButton: () {
              Get.back();
              callback();
            },
          );
        } else {
          AppUtils.showDialogError(
            context,
            AppStrings.getString(AppStrings.errorCheckTransactionQr)!.replaceFirst('%1s', MposConstant.SUPPORT_PHONE),
            title: AppStrings.getString(AppStrings.titleNotice) ?? '',
          );
        }
      }
    } else {
      if (callback != null) {
        AppUtils.showDialogAlert(
          context,
          title: AppStrings.getString(AppStrings.titleNotice) ?? '',
          description: AppStrings.getString(AppStrings.stillClose) ?? '',
          isTwoButton: true,
          text1stButton: AppStrings.getString(AppStrings.cancel) ?? '',
          text2ndButton: AppStrings.getString(AppStrings.close) ?? '',
          onPress2ndButton: () {
            Get.back();
            callback();
          },
        );
      } else {
        AppUtils.showDialogError(context, '${response.code ?? ''}: ${response.message ?? ''}');
      }
    }
  }
}
