import 'dart:convert';
import 'dart:typed_data';

import 'package:collection/collection.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/data/model/base_response.dart';
import 'package:mposxs/app/data/model/transaction_history_model.dart';
import 'package:mposxs/app/data/provider/api_client.dart';
import 'package:mposxs/app/data/provider/native_bridge.dart';
import 'package:mposxs/app/route/app_pages.dart';
import 'package:mposxs/app/ui/widget/printer/temp_printer_bill_qr_widget.dart';
import 'package:mposxs/app/util/api_constant.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:screenshot/screenshot.dart';

class HistoryAllController extends GetxController {
  final MyAppController _appController = Get.find<MyAppController>();
  RxList<GroupDataByTimeTransactionHistoryModel> listGroupData = RxList();
  TransactionHistoryModel? _mapTransaction;
  int _pageIndex = 0;
  late BuildContext context;
  late RefreshController refreshController;
  Rx<Widget> tempPrinterWidget = Container().obs;
  final ScreenshotController screenshotController = ScreenshotController();

  @override
  void onReady() {
    super.onReady();
    _onInitData();
  }

  _groupData(isFresh) {
    List<Data> _listTransaction = (_mapTransaction?.data != null ? _mapTransaction?.data! : [])!;
    Map arrTemp1 = groupBy(_listTransaction, (dynamic item) {
      String date =
          '${DateFormat('dd/MM/yyyy', 'vi_VN').format(DateTime.fromMillisecondsSinceEpoch(item?.createdDate, isUtc: true).add(Duration(hours: 7)))}';
      return date;
    });
    if (isFresh) {
      listGroupData.value = [];
    }
    List<GroupDataByTimeTransactionHistoryModel> listGD = [];
    arrTemp1.keys.forEach((key) {
      listGD.add(GroupDataByTimeTransactionHistoryModel(date: key, data: arrTemp1[key]));
    });
    listGroupData.value = [...listGD];
  }

  onLoadMoreData() async {
    _pageIndex += 1;
    Map params = {
      "serviceName": "TRANSACTION_LIST",
      "merchantId": _appController.userInfo?.merchantId,
      "pageIndex": _pageIndex,
      "muid": _appController.loginAccount,
      "pageSize": 20,
    };
    BaseResponse response = await ApiClient.instance.request<TransactionHistoryModel>(
        url: ApiConstant.urlApi,
        data: json.encode(params),
        fromJsonModel: (data) => TransactionHistoryModel.fromJson(data));
    if (response.result!) {
      _mapTransaction!.data!.addAll((response.data as TransactionHistoryModel).data!);
      _groupData(true);
      refreshController.loadComplete();
    } else {
      refreshController.loadFailed();
      AppUtils.showDialogError(context, '${response.code ?? ''}: ${response.message ?? ''}');
    }
  }

  onRefreshData() async {
    _pageIndex = 0;
    Map params = {
      "serviceName": "TRANSACTION_LIST",
      "merchantId": _appController.userInfo?.merchantId,
      "pageIndex": _pageIndex,
      "muid": _appController.loginAccount,
      "pageSize": 20,
    };
    BaseResponse response = await ApiClient.instance.request<TransactionHistoryModel>(
        url: ApiConstant.urlApi,
        data: json.encode(params),
        fromJsonModel: (data) => TransactionHistoryModel.fromJson(data));
    if (response.result!) {
      _mapTransaction = (response.data as TransactionHistoryModel?);
      _groupData(true);
      refreshController.refreshCompleted();
    } else {
      refreshController.refreshFailed();
      AppUtils.showDialogError(context, '${response.code ?? ''}: ${response.message ?? ''}');
    }
  }

  _onInitData() async {
    Map params = {
      "serviceName": "TRANSACTION_LIST",
      "merchantId": _appController.userInfo?.merchantId,
      "pageIndex": 0,
      "muid": _appController.loginAccount,
      "pageSize": 20,
    };
    BaseResponse response = await ApiClient.instance.request<TransactionHistoryModel>(
        url: ApiConstant.urlApi,
        data: json.encode(params),
        fromJsonModel: (data) => TransactionHistoryModel.fromJson(data));
    if (response.result!) {
      _mapTransaction = (response.data as TransactionHistoryModel?);
      _groupData(true);
    } else {
      AppUtils.showDialogError(context, '${response.code ?? ''}: ${response.message ?? ''}');
    }
  }

  onPressQuickPayment() {
    Get.back(result: jsonEncode({'needReloadList': false, 'isQuickPress': true}));
  }

  onPressItem(Data? data) async {
    Map param = {};
    if (data?.transactionPushType != null) {      // case payment QR
      param = {"data": data, "isNormal": true, "isDomestic": false};
    } else {
      param = {"data": data, "isNormal": true, "isDomestic": true};
    }
    var dataBackVar = await Get.toNamed(AppRoute.history_detail_screen, arguments: param);
    Map? dataBack = jsonDecode(dataBackVar.toString());
    if (dataBack != null) {
      if (dataBack['needReloadList'] == true) {
        onRefreshData();
      }
      if (dataBack['isQuickPress'] == true) {
        Get.back(result: jsonEncode({'needReloadList': false, 'isQuickPress': true}));
      }
    }
  }

  /*_printTransactionReceipt(String transId, String transRqId) async {
    _appController.showLoading();
    NativeResponseModel nativeResponseModel =
        await NativeBridge.getInstance().nativeGetTransactionReceipt(transId, transRqId);
    _appController.hideLoading();
    if (nativeResponseModel.isSuccess) {
      _appController.showLoading();
      NativeResponseModel nativeResponseModelPrint =
          await NativeBridge.getInstance().nativePrintBase64(nativeResponseModel.data);
      _appController.hideLoading();
      if (!nativeResponseModelPrint.isSuccess) {
        AppUtils.showDialogErrorNative(context, nativeResponseModelPrint?.error);
      }
    } else {
      AppUtils.showDialogErrorNative(context, nativeResponseModel?.error);
    }
  }*/

  printTransactionReceiptLocal(String transId, bool isCancel) async {
    _appController.printTransactionReceipt(context, transId, isCancel, screenshotController, tempPrinterWidget);
  }
  /*printTransactionReceiptLocalabc(String transId, bool isCancel) async {
    _appController.showLoading();
    NativeResponseModel nativeResponseModelTID = await NativeBridge.getInstance().nativeGetTID();
    NativeResponseModel nativeResponseModelMID = await NativeBridge.getInstance().nativeGetMID();
    var dataPrint;
    try {
      final storage = FlutterSecureStorage();
      String value = await storage.read(key: transId) ?? '';
      if (!isNullEmpty(value)) {
        dataPrint = json.decode(value);
      }
    } on Exception catch (e) {}
    _appController.hideLoading();
    if (dataPrint != null) {
      int transactionDate = int.tryParse(dataPrint['transactionDate']) ?? 0;
      DateTime dateTime =
          DateTime.fromMillisecondsSinceEpoch((transactionDate ?? 0), isUtc: true).add(Duration(hours: 7));
      if (dateTime == null) {
        dateTime = DateTime.now();
      }
      _appController.showLoading();
      tempPrinterWidget.value = Container(
        child: TempPrinterBillWidget(
          printTID: nativeResponseModelTID?.data,
          printMID: nativeResponseModelMID?.data,
          printRef: dataPrint['refNo'],
          printInvoice: dataPrint['invoiceNo'],
          printBatch: dataPrint['batchNo'],
          printApprove: dataPrint['approvalCode'],
          printPan: dataPrint['pan'],
          printHolder: dataPrint['cardHolderName'],
          printType: dataPrint['issuerCode'],
          printAmount: dataPrint['amount'],
          printDate: DateFormat('dd/MM/yyyy').format(dateTime),
          printTime: DateFormat('HH:mm:ss').format(dateTime),
          printSign: dataPrint['targetBase64Receipt'],
          printMCName: _appController.userInfo?.businessName,
          printMCAddress: _appController.userInfo?.businessAddress,
          printTxid: transId,
          printDes: dataPrint['description'],
          printIsVoid: isCancel,
          screenshotController: screenshotController,
        ),
      );
      screenshotController
          .capture(
        delay: Duration(seconds: 2),
        pixelRatio: 1.0,
      )
          .then((Uint8List captureImage) async {
        String imageB64 = base64Encode(captureImage);
        await NativeBridge.getInstance().nativePrintBase64(imageB64);
        // await NativeBridge.getInstance().nativePrintPush(4);
        tempPrinterWidget.value = Container();
        _appController.hideLoading();
      }, onError: (e) {
        tempPrinterWidget.value = Container();
        _appController.hideLoading();
      });
    } else {
      _appController.showLoading();
      BaseResponse response = await ApiClient.instance.request(
          url: ApiConstant.urlReceipt,
          data: json.encode({
            'serviceName': 'RECEIPT_GET_DETAILS',
            'txid': transId,
          }));
      _appController.hideLoading();
      if (response.result) {
        int transactionDate = response.data['transactionDate'];
        DateTime dateTime =
            DateTime.fromMillisecondsSinceEpoch((transactionDate ?? 0), isUtc: true).add(Duration(hours: 7));
        if (dateTime == null) {
          dateTime = DateTime.now();
        }
        _appController.showLoading();
        tempPrinterWidget.value = Container(
          child: TempPrinterBillWidget(
            printTID: nativeResponseModelTID?.data,
            printMID: nativeResponseModelMID?.data,
            printRef: response.data['refNo'],
            printInvoice: response.data['invoiceNo'],
            printBatch: response.data['batchNo'],
            printApprove: response.data['approvalCode'],
            printPan: response.data['pan'],
            printHolder: response.data['cardHolderName'],
            printType: response.data['issuerCode'],
            printAmount: response.data['amount'],
            printDate: DateFormat('dd/MM/yyyy').format(dateTime),
            printTime: DateFormat('HH:mm:ss').format(dateTime),
            printSign: response.data['targetBase64Receipt'] ?? response.data['base64PdfReceipt'],
            printMCName: _appController.userInfo?.businessName,
            printMCAddress: _appController.userInfo?.businessAddress,
            printTxid: transId,
            printDes: response.data['description'],
            printIsVoid: isCancel,
            screenshotController: screenshotController,
          ),
        );
        screenshotController
            .capture(
          delay: Duration(seconds: 2),
          pixelRatio: 1.0,
        )
            .then((Uint8List captureImage) async {
          String imageB64 = base64Encode(captureImage);
          await NativeBridge.getInstance().nativePrintBase64(imageB64);
          // await NativeBridge.getInstance().nativePrintPush(4);
          tempPrinterWidget.value = Container();
          _appController.hideLoading();
        }, onError: (e) {
          tempPrinterWidget.value = Container();
          _appController.hideLoading();
        });
      } else {
        if (isCancel) {
          AppUtils.showDialogError(context, '${response.code ?? ''}: ${response.message ?? ''}');
        } else {
          _printTransactionReceipt(transId, '');
        }
      }
    }
  }*/

  printTransactionReceiptLocalQR(Data? data) async {
    NativeResponseModel nativeResponseModelTID = await NativeBridge.getInstance().nativeGetTID();
    NativeResponseModel nativeResponseModelMID = await NativeBridge.getInstance().nativeGetMID();
    DateTime dateTime =
        DateTime.fromMillisecondsSinceEpoch((data?.createdDate ?? 0), isUtc: true).add(Duration(hours: 7));
    _appController.showLoading();
    tempPrinterWidget.value = Container(
      child: TempPrinterBillQrWidget(
        printTID: nativeResponseModelTID.data,
        printMID: nativeResponseModelMID.data,
        printRef: data?.rrn,
        printAmount: data?.amount,
        printDate: DateFormat('dd/MM/yyyy').format(dateTime),
        printTime: DateFormat('HH:mm:ss').format(dateTime),
        printMCName: _appController.userInfo?.businessName,
        printMCAddress: _appController.userInfo?.businessAddress,
        printTxid: data?.txid,
        printDes: data?.description,
        printQRName: data?.issuerName,
        printAuthCode: data?.authCode,
        screenshotController: screenshotController,
      ),
    );
    screenshotController
        .capture(
      delay: Duration(seconds: 2),
      pixelRatio: 1.0,
    )
        .then((Uint8List? captureImage) async {
      String imageB64 = base64Encode(captureImage!);
      await NativeBridge.getInstance().nativePrintBase64(imageB64);
      // await NativeBridge.getInstance().nativePrintPush(4);
      tempPrinterWidget.value = Container();
      _appController.hideLoading();
    }, onError: (e) {
      tempPrinterWidget.value = Container();
      _appController.hideLoading();
    });
  }
}
