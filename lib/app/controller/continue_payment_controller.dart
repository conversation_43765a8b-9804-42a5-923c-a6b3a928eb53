import 'dart:convert';
import 'dart:io';

import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mposxs/app/controller/payment_finish_controller.dart';
import 'package:mposxs/app/data/provider/local_storage.dart';
import 'package:mposxs/app/data/provider/native_bridge.dart';
import 'package:mposxs/app/route/app_pages.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/app_validation.dart';
import 'package:mposxs/app/util/mpos_constant.dart';

import 'app_controller.dart';

class ContinuePaymentController extends GetxController {
  BuildContext? context;
  Map? _paymentInfo;
  RxString amount = ''.obs;
  RxString cardNumber = ''.obs;
  RxString cardholderName = ''.obs;
  RxString transactionDate = ''.obs;
  RxString description = ''.obs;
  final MyAppController _appController = Get.find<MyAppController>();

  @override
  void onReady() {
    initData();
    super.onReady();
  }

  initData() {
    _paymentInfo = Get.arguments;
    if (_paymentInfo != null) {
      if (_paymentInfo!['amount'] != null) {
        amount.value = '${AppUtils.formatCurrency(_paymentInfo!['amount'])} VND';
      }

      if (_paymentInfo!['pan'] != null) {
        cardNumber.value = _paymentInfo!['pan'];
      }

      if (_paymentInfo!['cardholderName'] != null) {
        cardholderName.value = _paymentInfo!['cardholderName'];
      }
      if (_paymentInfo!['transactionDate'] != null) {
        transactionDate.value = DateFormat('HH:mm, dd/MM/yyyy', 'vi_VN').format(DateTime.fromMillisecondsSinceEpoch(
                Platform.isIOS || _appController.userInfo!.isMacqFlow == 1
                    ? _paymentInfo!['transactionDate']
                    : int.tryParse(_paymentInfo!['transactionDate'])!,
                isUtc: true)
            .add(Duration(hours: 7)));
      }
      if (!isNullEmpty(_paymentInfo!['itemDesc'])) {
        description.value = _paymentInfo!['itemDesc'];
      } else {
        if (!isNullEmpty(_paymentInfo!['paymentIdentify'])) {
          String paymentIdentify = _paymentInfo!['paymentIdentify'];
          if (paymentIdentify.contains('|')) {
            List params = paymentIdentify.split('|');
            if (params.length > 1) {
              String strDescription = params[0];
              strDescription.replaceAll(new RegExp(r'_'), ' ');
              strDescription.replaceAll(new RegExp(r'-'), ' ');
              strDescription.replaceAll(new RegExp(r'INSTALLMENT'), '');
              strDescription.replaceAll(new RegExp(r'CASHBACK'), '');
              description.value = strDescription;
            }
          }
        }
      }
    }
  }

  onPressConfirm() async {
    LocalStorage().setPaymentErrorUnsign(null);
    // Map callingAppMap = await LocalStorage().getDataCallingApp();

    String strDescription = description.value;
    String paymentIdentify = _paymentInfo!['paymentIdentify'];
    String strOrderId = '';
    if (paymentIdentify.contains('MERCHANT_INTEGRATED')) {
      List params = paymentIdentify.split('-');
      if (params.length > 1) {
        strOrderId = params[1];
      }

      // if (callingAppMap != null && callingAppMap['description'] != null) {
      //   strDescription = callingAppMap['description'];
      // } else {
        strDescription = strOrderId + '-Thanh_toan_hoa_don/_Pay_order';
      // }
    }

    Map nativeParams = {
      'paymentIdentify': _paymentInfo!['paymentIdentify'],
      'description_payment': strDescription,
    };
    NativeResponseModel nativeResponseModel =
        await NativeBridge.getInstance().nativeContinueTransactionUnsign(nativeParams);
    if (nativeResponseModel.isSuccess) {
      final Map responseMap = json.decode(nativeResponseModel.data);
      responseMap['description_payment'] = strDescription;
      Get.toNamed(AppRoute.payment_finish_screen,
          arguments: PaymentFinishArguments(MposConstant.UNSIGN_CARD_PAYMENT, responseMap, null));
    } else {
      Get.toNamed(AppRoute.payment_finish_screen,
          arguments: PaymentFinishArguments(
              MposConstant.UNSIGN_CARD_PAYMENT,
              null,
              AppUtils.getErrorByCode(context, nativeResponseModel?.error?.code,
                  defaultMessage: nativeResponseModel?.error?.message)));
    }
  }
}
