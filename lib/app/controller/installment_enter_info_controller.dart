import 'package:credit_card_type_detector/credit_card_type_detector.dart';
import 'package:get/get.dart';
import 'package:math_expressions/math_expressions.dart';
import 'package:mposxs/app/data/model/mp_data_login_model.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/route/app_pages.dart';
import 'package:mposxs/app/util/app_validation.dart';

import 'app_controller.dart';

class InstallmentEnterInfoController extends GetxController {
  final MyAppController _appController = Get.find<MyAppController>();
  List<Map> listDataCard = [
    {
      'id': 0,
      'type': 'VISA_LOCAL',
      'logo': AppImages.icCardVisa,
      'typeShort': CreditCardType.visa,
      'name': 'Visa',
      'code': 'VISA'
    },
    {
      'id': 1,
      'type': 'JCB_LOCAL',
      'logo': AppImages.icCardJCB,
      'typeShort': CreditCardType.jcb,
      'name': 'JCB',
      'code': 'JCB'
    },
    {
      'id': 2,
      'type': 'MASTER_LOCAL',
      'logo': AppImages.icCardMaster,
      'typeShort': CreditCardType.mastercard,
      'name': 'Mastercard',
      'code': 'MASTER'
    },
    {
      'id': 3,
      'type': 'CUP_LOCAL',
      'logo': AppImages.icCardUnionPay,
      'typeShort': CreditCardType.unionpay,
      'name': 'Unionpay',
      'code': 'UNIONPAY',
    }
  ];

  static const TYPE_FEE_PERCENT = 1;
  static const TYPE_FEE_SUBTRACTION = 2; // subtraction amountInput
  static const TYPE_FEE_SUBTRACTION_FEE = 3; // subtraction amountInput + fee

  RxMap selectedCardType = {}.obs;
  Rx<MPItemPeriod> selectedPeriod = MPItemPeriod().obs;

  RxBool enableFeeTrans = false.obs;
  RxBool enableFeeInstallment = false.obs;
  bool allowChangeFee = false;
  bool allowScanCard = false;
  bool allowCreateLink = false;
  RxBool isCheckPayLink = false.obs;
  RxDouble feeInstallment = 0.0.obs;
  RxDouble feeCard = 0.0.obs;
  RxInt finalAmountPay = 0.obs;
  int inputAmount = 0;

  @override
  void onInit() {
    allowChangeFee = _appController.userInfo?.checkFeeChange == 1;
    enableFeeTrans.value = _appController.userInfo?.checkFeeTrans == 1;
    enableFeeInstallment.value = _appController.userInfo?.checkFeeInstallment == 1;
    allowCreateLink = _appController.userInfo?.isPayLink == 1;
    allowScanCard = _appController.userInfo?.isPayCard == 1;
    isCheckPayLink.value = !allowScanCard && allowCreateLink;
    inputAmount = _appController.installmentInfoSession?.amount ?? 0;
    if (!isNullEmpty(_appController.installmentInfoSession?.selectedCardType)) {
      var index =
          listDataCard.indexWhere((i) => i['typeShort'] == _appController.installmentInfoSession!.selectedCardType);
      selectedCardType.value = listDataCard[index > -1 ? index : 0];
    } else {
      selectedCardType.value = listDataCard[0];
    }
    selectedPeriod.value = _appController.installmentInfoSession!.selectedBank!.listPeriod![0];
    calculatorAmount();
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  double? calculatorAmountPay(int amount, double varX, double varY, String formula) {
    Variable x = Variable('x'), y = Variable('y'), A = Variable('A');
    Parser parser = Parser();
    Expression expression = parser.parse(formula);
    ContextModel contextModel = ContextModel();
    contextModel.bindVariable(x, Number(varX));
    contextModel.bindVariable(y, Number(varY));
    contextModel.bindVariable(A, Number(amount));
    return expression.evaluate(EvaluationType.REAL, contextModel);
  }

  double? calculatorAmountFeeByPercent(int amount, double fee) {
    const formula = 'A * x / 100';
    Variable x = Variable('x'), A = Variable('A');
    Parser parser = Parser();
    Expression expression = parser.parse(formula);
    ContextModel contextModel = ContextModel();
    contextModel.bindVariable(x, Number(fee));
    contextModel.bindVariable(A, Number(amount));
    return expression.evaluate(EvaluationType.REAL, contextModel);
  }

  double? calculatorAmountFeeBySubtraction(double amountInput, int amount) {
    const formula = 'B - A';
    Variable B = Variable('B'), A = Variable('A');
    Parser parser = Parser();
    Expression expression = parser.parse(formula);
    ContextModel contextModel = ContextModel();
    contextModel.bindVariable(B, Number(amount));
    contextModel.bindVariable(A, Number(amountInput));
    return expression.evaluate(EvaluationType.REAL, contextModel);
  }

  /// Nếu chủ thẻ chịu cả 2 loai phí:  B = A*100/(100-x-y)
  /// <p>
  /// Nếu chủ thẻ chỉ chịu phí trả góp: B = A*(100-x)/(100-x-y)
  /// Nếu chủ thẻ chỉ chịu phí giao dịch: B = A*(100-y)/(100-x-y)
  /// <p>
  /// A: Số tiền MC nhận về sau khi trừ phí (tiền bán sản phẩm)
  /// B: Số tiền chủ thẻ quẹt thẻ
  /// x: % phí giao dịch
  /// y: % phí trả góp
  /// <p>
  /// Số tiền phí giao dịch hiển thị: N  (hiển thị ở “Thêm phí cà thẻ”)
  /// Số tiền phí trả góp hiển thị: M (Hiển thị ở “Thêm phí trả góp”)
  void calculatorAmount() {
    String formula = '';
    var typeFeeCard = 0;
    var typeFeeInstallment = 0;

    bool haveFeeInstallment = true;
    bool haveFeeCard = true;
    bool calculatorFee = true;

    if (enableFeeTrans.value && enableFeeInstallment.value) {
      formula = 'A*100/(100-x-y)';
      typeFeeCard = TYPE_FEE_PERCENT;
      typeFeeInstallment = TYPE_FEE_SUBTRACTION_FEE;
    } else if (enableFeeTrans.value) {
      formula = 'A*(100-y)/(100-x-y)';
      haveFeeInstallment = false;
      typeFeeCard = TYPE_FEE_SUBTRACTION;
      typeFeeInstallment = TYPE_FEE_PERCENT;
    } else if (enableFeeInstallment.value) {
      formula = _appController.userInfo?.utmSource == 'NGANLUONG' ? 'A + A * y / 100' : 'A*(100-x)/(100-x-y)';
      haveFeeCard = false;
      typeFeeCard = TYPE_FEE_PERCENT;
      typeFeeInstallment = _appController.userInfo?.utmSource == 'NGANLUONG' ? TYPE_FEE_PERCENT : TYPE_FEE_SUBTRACTION;
    } else {
      formula = 'B = A';
      calculatorFee = false;
      haveFeeCard = false;
      haveFeeInstallment = false;
      typeFeeCard = TYPE_FEE_PERCENT;
      typeFeeInstallment = TYPE_FEE_PERCENT;
    }

    double? feeSwipeCard = 0;
    double feeInstallment =
        !isCheckPayLink.value ? (selectedPeriod.value.rate?.toDouble()??0): (selectedPeriod.value.linkCardRate?.toDouble() ?? 0);
    // fee swipe card
    if (!isCheckPayLink.value) {
      for (MPExchangeInfo exchangeInfo in (_appController.userInfo?.exchangeInfo ?? [])) {
        if (exchangeInfo.issuerCode == selectedCardType.value['type']) {
          feeSwipeCard = exchangeInfo.fee?.toDouble();
          break;
        }
      }
    } else {
      for (MPExchangeLinkCardInfo exchangeLinkCardInfo in (_appController.userInfo?.exchangeLinkCardInfo ?? [])) {
        if (exchangeLinkCardInfo.issuerCode == selectedCardType.value['type']) {
          feeSwipeCard = exchangeLinkCardInfo.fee as double?;
          break;
        }
      }
    }

    // total amount
    int finalAmountPayTemp = 0;
    if (calculatorFee) {
      double amountCalculator = calculatorAmountPay(inputAmount, feeSwipeCard!, feeInstallment, formula)!;
      if (amountCalculator % 1000 != 0) {
        finalAmountPayTemp = ((amountCalculator / 1000).floor() + 1) * 1000;
      } else {
        finalAmountPayTemp = amountCalculator.round();
      }
    } else {
      finalAmountPayTemp = inputAmount;
    }
    this.finalAmountPay.value = finalAmountPayTemp;

    // fee card
    double? amountFeeCard = 0;
    if (haveFeeCard) {
      if (typeFeeCard == TYPE_FEE_PERCENT) {
        amountFeeCard = calculatorAmountFeeByPercent(finalAmountPayTemp, feeSwipeCard!);
      } else {
        amountFeeCard = calculatorAmountFeeBySubtraction(inputAmount.toDouble(), finalAmountPayTemp);
      }
    } else {
      amountFeeCard = 0;
    }
    this.feeCard.value = amountFeeCard!;

    // fee installment
    double? amountFeeInstallment = 0;
    if (haveFeeInstallment) {
      if (typeFeeInstallment == TYPE_FEE_PERCENT) {
        if (_appController.userInfo?.utmSource == 'NGANLUONG') {
          amountFeeInstallment = finalAmountPayTemp - inputAmount.toDouble();
        } else {
          amountFeeInstallment = this.calculatorAmountFeeByPercent(finalAmountPayTemp, feeInstallment);
        }
      } else if (typeFeeInstallment == TYPE_FEE_SUBTRACTION) {
        amountFeeInstallment = this.calculatorAmountFeeBySubtraction(inputAmount.toDouble(), finalAmountPayTemp);
      } else {
        amountFeeInstallment = calculatorAmountFeeBySubtraction(inputAmount + amountFeeCard, finalAmountPayTemp);
      }
    } else {
      amountFeeInstallment = 0;
    }
    this.feeInstallment.value = amountFeeInstallment!;
  }

  void onPressItemCardType(Map item) {
    selectedCardType.value = item;
    calculatorAmount();
  }

  void onPressItemPeriod(MPItemPeriod item) {
    selectedPeriod.value = item;
    calculatorAmount();
  }

  void onCheckedPayLinkChange() {
    isCheckPayLink.value = !isCheckPayLink.value;
    calculatorAmount();
  }

  void onCheckedFeeInstallment() {
    enableFeeInstallment.value = !enableFeeInstallment.value;
    calculatorAmount();
  }

  void onCheckedFeeTrans() {
    enableFeeTrans.value = !enableFeeTrans.value;
    calculatorAmount();
  }

  onPressContinue() {
    _appController.installmentInfoSession!.selectedPeriod = selectedPeriod.value;
    _appController.installmentInfoSession!.finalAmountPay = finalAmountPay.value;
    _appController.installmentInfoSession!.allowChangeFee = allowChangeFee;
    _appController.installmentInfoSession!.enableFeeTrans = enableFeeTrans.value;
    _appController.installmentInfoSession!.enableFeeInstallment = enableFeeInstallment.value;
    _appController.installmentInfoSession!.isPayLink = isCheckPayLink.value;
    _appController.installmentInfoSession!.feeCard = feeCard.value;
    _appController.installmentInfoSession!.feeInstallment = feeInstallment.value;
    _appController.installmentInfoSession!.selectedCardTypeMap = selectedCardType.value;
    Get.toNamed(AppRoute.installment_confirm_screen, arguments: Get.arguments);
  }
}
