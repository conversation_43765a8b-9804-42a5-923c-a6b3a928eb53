import 'dart:convert';

import 'package:credit_card_type_detector/credit_card_type_detector.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/data/model/base_response.dart';
import 'package:mposxs/app/data/model/data_session.dart';
import 'package:mposxs/app/data/model/mp_data_login_model.dart';
import 'package:mposxs/app/data/provider/api_client.dart';
import 'package:mposxs/app/route/app_pages.dart';
import 'package:mposxs/app/ui/screen/installment/widget/bottom_sheet_check_bank.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/app_validation.dart';
import 'package:mposxs/app/util/remove_accent.dart';

class InstallmentListBankController extends GetxController {
  late BuildContext context;
  TextEditingController textSearchController = TextEditingController();
  RxBool showClearSearch = false.obs;
  RxList<MPInstallmentInfo> listBankInstallment = RxList();
  final MyAppController _appController = Get.find<MyAppController>();

  @override
  void onInit() {
    listBankInstallment.value = _appController.userInfo?.installmentInfo ?? [];
    super.onInit();
  }

  @override
  void onReady() {
    super.onReady();
  }

  onChangedSearch(String text) {
    _onFilterBank(text);
    bool showCS = !isNullEmpty(text);
    if (showCS != showClearSearch.value) {
      showClearSearch.value = showCS;
    }
  }

  _onFilterBank(String textFilter) {
    if (isNullEmpty(textFilter)) {
      listBankInstallment.value = _appController.userInfo?.installmentInfo ?? [];
    } else {
      List<MPInstallmentInfo> listBI = (_appController.userInfo?.installmentInfo ?? [])
          .where(
              (item) => (removeAccents(item.bankName).toLowerCase().contains(removeAccents(textFilter).toLowerCase())))
          .toList();
      listBankInstallment.value = listBI;
    }
  }

  onPressClearSearch() {
    textSearchController.clear();
    onChangedSearch('');
  }

  _onPressSkipCheckBin(MPInstallmentInfo item) {
    _appController.installmentInfoSession = InstallmentInfoSession()..selectedBank = item;
    Get.toNamed(AppRoute.installment_enter_amount_screen);
  }

  _onPressContinueCheckBin(MPInstallmentInfo item, String binCode, CreditCardType typeCardCheck) async {
    Map params = {
      'serviceName': 'GET_INSTALLMENT_BIN_CODE',
      'bankShortname': item.bankName,
      'binCode': binCode,
    };
    _appController.showLoading();
    BaseResponse response = await ApiClient.instance.request(data: json.encode(params));
    _appController.hideLoading();
    if (response.result!) {
      _appController.installmentInfoSession = InstallmentInfoSession()
        ..selectedBank = item
        ..selectedCardType = typeCardCheck;
      Get.toNamed(AppRoute.installment_enter_amount_screen);
    } else {
      AppUtils.showDialogError(context, '${response.code ?? ''}: ${response.message ?? ''}');
    }
  }

  onPressItemBank(MPInstallmentInfo item) {
    AppUtils.hideKeyboard(context);

    if (_appController.userInfo?.optCheckInstallmentBin == 1) {
      showModalBottomSheet(
        context: context,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
        ),
        builder: (context) {
          return BottomSheetCheckBank(
            item,
            () => _onPressSkipCheckBin(item),
            (binCode, typeCardCheck) => _onPressContinueCheckBin(item, binCode, typeCardCheck),
          );
        },
        isScrollControlled: true,
        isDismissible: false,
      );
    } else {
      _appController.installmentInfoSession = InstallmentInfoSession()..selectedBank = item;
      Get.toNamed(AppRoute.installment_enter_amount_screen);
    }
  }
}
