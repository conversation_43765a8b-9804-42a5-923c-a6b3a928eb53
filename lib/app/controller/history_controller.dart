import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/data/model/settle_transaction_history_model.dart';
import 'package:mposxs/app/data/provider/native_bridge.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/route/app_pages.dart';
import 'package:mposxs/app/ui/widget/dialog_alert.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:screenshot/screenshot.dart';

class HistoryController extends GetxController {
  late BuildContext context;
  final MyAppController _appController = Get.find<MyAppController>();
  RxList<PaymentItems> listSettleTransaction = RxList();
  late RefreshController refreshController;
  bool? canVoid = false;
  bool? canSettle = false;
  RxInt lengthListCanSettle = 0.obs;
  RxBool enableBtnSettle = false.obs;
  Function? doLogout;
  Function? onPressQuickPayment;
  Rx<Widget> tempPrinterWidget = Container().obs;
  final ScreenshotController screenshotController = ScreenshotController();

  _onPressCloseDialog(NativeResponseModel nativeResponseModel) {
    Get.back();
    if ((nativeResponseModel.error?.code == '14004' ||
            nativeResponseModel.error?.code == '14003' ||
            nativeResponseModel.error?.code == '14002') &&
        doLogout != null) {
      doLogout!();
    }
  }

  getSettleTransactionHistory(bool isRefresh) async {
    listSettleTransaction.value = [];
    if (!isRefresh) {
      _appController.showLoading();
    }
    NativeResponseModel nativeResponseModel = await NativeBridge.getInstance().nativeGetListTransactionComplete();
    _appController.hideLoading();
    if (nativeResponseModel.isSuccess) {
      final String listTransaction = nativeResponseModel.data;
      if (isRefresh) refreshController.refreshCompleted();
      Map? responseMap = json.decode(listTransaction);
      if (responseMap != null && listTransaction.length > 0) {
        SettleTransactionHistoryModel itemObject = SettleTransactionHistoryModel.fromJson(responseMap as Map<String, dynamic>);
        listSettleTransaction.value = itemObject.paymentItems ?? [];
        canVoid = itemObject.canVoid ?? false;
        canSettle = itemObject.canSettle ?? false;
      }
      List<PaymentItems> listCanSettle = listSettleTransaction.value.where((item) => item.mStatus == 100).toList();
      int totalAmountSettle = 0;
      for (int i = 0; i < listCanSettle.length; i++) {
        int amountTemp = int.parse(listCanSettle[i].mAmount!.replaceAll(',', ''));
        totalAmountSettle = totalAmountSettle + amountTemp;
      }
      lengthListCanSettle.value = listCanSettle.length;
      enableBtnSettle.value = lengthListCanSettle.value > 0 && totalAmountSettle > 0 && canSettle!;
    } else {
      if (isRefresh) refreshController.refreshFailed();
      AppUtils.showDialogErrorNative(context!, nativeResponseModel.error!, onPressButton: () {
        _onPressCloseDialog(nativeResponseModel);
      });
    }
  }

  _onPressConfirmSettle() async {
    Get.back();
    _appController.showLoading();
    NativeResponseModel nativeResponseModel = await NativeBridge.getInstance().nativeSettleAllTransactionComplete();
    _appController.hideLoading();
    if (nativeResponseModel.isSuccess) {
      getSettleTransactionHistory(false);
    } else {
      AppUtils.showDialogErrorNative(context!, nativeResponseModel?.error!);
    }
  }

  onPressSettled() {
    showDialog(
      context: context!,
      builder: (BuildContext context) {
        return DialogAlert(
          title: AppStrings.getString(AppStrings.titleConfirmSettle)!,
          description: AppStrings.getString(AppStrings.desConfirmSettle)!.replaceFirst('{x}', lengthListCanSettle.value.toString()),
          image: AppImages.icNoticeBlue,
          isTwoButton: true,
          text1stButton: AppStrings.getString(AppStrings.skip)!,
          text2ndButton: AppStrings.getString(AppStrings.buttonConfirm)!,
          onPress2ndButton: _onPressConfirmSettle,
        );
      },
    );
  }

  onPressSeeAllTransaction() async {
    var dataBackVar = await Get.toNamed(AppRoute.history_all_screen);
    Map? dataBack = jsonDecode(dataBackVar.toString());
    if (dataBack != null) {
      if (dataBack['needReloadList'] == true) {
        getSettleTransactionHistory(false);
      }
      if (dataBack['isQuickPress'] == true && onPressQuickPayment != null) {
        onPressQuickPayment!(true);
      }
    }
  }

  onPressSettleItemTransaction(PaymentItems data) async {
    Map param = {"data": data, "isNormal": false, "isDomestic": false};
    var dataBackVar = await Get.toNamed(AppRoute.history_detail_screen, arguments: param);
    Map? dataBack = jsonDecode(dataBackVar.toString());
    if (dataBack != null) {
      if (dataBack['needReloadList'] == true) {
        getSettleTransactionHistory(false);
      }
      if (dataBack['isQuickPress'] == true && onPressQuickPayment != null) {
        onPressQuickPayment!(true);
      }
    }
  }

  /*_printTransactionReceipt(String transId, String transRqId) async {
    _appController.showLoading();
    NativeResponseModel nativeResponseModel =
        await NativeBridge.getInstance().nativeGetTransactionReceipt(transId, transRqId);
    _appController.hideLoading();
    if (nativeResponseModel.isSuccess) {
      _appController.showLoading();
      NativeResponseModel nativeResponseModelPrint =
          await NativeBridge.getInstance().nativePrintBase64(nativeResponseModel.data);
      _appController.hideLoading();
      if (!nativeResponseModelPrint.isSuccess) {
        AppUtils.showDialogErrorNative(context, nativeResponseModelPrint?.error);
      }
    } else {
      AppUtils.showDialogErrorNative(context, nativeResponseModel?.error);
    }
  }*/

  printTransactionReceiptLocal(String transId, bool isCancel) async {
    _appController.printTransactionReceipt(context, transId, isCancel, screenshotController, tempPrinterWidget);
  }
  /*printTransactionReceiptLocal(String transId, bool isCancel) async {
    _appController.showLoading();
    NativeResponseModel nativeResponseModelTID = await NativeBridge.getInstance().nativeGetTID();
    NativeResponseModel nativeResponseModelMID = await NativeBridge.getInstance().nativeGetMID();
    var dataPrint;
    try {
      final storage = FlutterSecureStorage();
      String value = await storage.read(key: transId) ?? '';
      if (!isNullEmpty(value)) {
        dataPrint = json.decode(value);
      }
    } on Exception catch (e) {}
    _appController.hideLoading();
    // print offline from cache
    if (dataPrint != null) {
      int transactionDate = int.tryParse(dataPrint['transactionDate']) ?? 0;
      DateTime dateTime =
          DateTime.fromMillisecondsSinceEpoch((transactionDate ?? 0), isUtc: true).add(Duration(hours: 7));
      if (dateTime == null) {
        dateTime = DateTime.now();
      }
      _appController.showLoading();
      tempPrinterWidget.value = Container(
        child: TempPrinterBillWidget(
          printTID: nativeResponseModelTID?.data,
          printMID: nativeResponseModelMID?.data,
          printRef: dataPrint['refNo'],
          printInvoice: dataPrint['invoiceNo'],
          printBatch: dataPrint['batchNo'],
          printApprove: dataPrint['approvalCode'],
          printPan: dataPrint['pan'],
          printHolder: dataPrint['cardHolderName'],
          printType: dataPrint['issuerCode'],
          printAmount: dataPrint['amount'],
          printDate: DateFormat('dd/MM/yyyy').format(dateTime),
          printTime: DateFormat('HH:mm:ss').format(dateTime),
          printSign: dataPrint['targetBase64Receipt'],
          printMCName: _appController.userInfo?.businessName,
          printMCAddress: _appController.userInfo?.businessAddress,
          printTxid: transId,
          printDes: dataPrint['description'],
          printIsVoid: isCancel,
          screenshotController: screenshotController,
        ),
      );
      screenshotController
          .capture(
        delay: Duration(seconds: 2),
        pixelRatio: 1.0,
      )
          .then((Uint8List captureImage) async {
        String imageB64 = base64Encode(captureImage);
        await NativeBridge.getInstance().nativePrintBase64(imageB64);
        // await NativeBridge.getInstance().nativePrintPush(4);
        tempPrinterWidget.value = Container();
        _appController.hideLoading();
      }, onError: (e) {
        tempPrinterWidget.value = Container();
        _appController.hideLoading();
      });
    } else {
      _appController.showLoading();
      BaseResponse response = await ApiClient.instance.request(
          url: ApiConstant.urlReceipt,
          data: json.encode({
            'serviceName': 'RECEIPT_GET_DETAILS',
            'txid': transId,
          }));
      _appController.hideLoading();
      if (response.result) {
        int transactionDate = response.data['transactionDate'];
        DateTime dateTime =
            DateTime.fromMillisecondsSinceEpoch((transactionDate ?? 0), isUtc: true).add(Duration(hours: 7));
        if (dateTime == null) {
          dateTime = DateTime.now();
        }
        _appController.showLoading();
        tempPrinterWidget.value = Container(
          child: TempPrinterBillWidget(
            printTID: nativeResponseModelTID?.data,
            printMID: nativeResponseModelMID?.data,
            printRef: response.data['refNo'],
            printInvoice: response.data['invoiceNo'],
            printBatch: response.data['batchNo'],
            printApprove: response.data['approvalCode'],
            printPan: response.data['pan'],
            printHolder: response.data['cardHolderName'],
            printType: response.data['issuerCode'],
            printAmount: response.data['amount'],
            printDate: DateFormat('dd/MM/yyyy').format(dateTime),
            printTime: DateFormat('HH:mm:ss').format(dateTime),
            printSign: response.data['targetBase64Receipt'] ?? response.data['base64PdfReceipt'],
            printMCName: _appController.userInfo?.businessName,
            printMCAddress: _appController.userInfo?.businessAddress,
            printTxid: transId,
            printDes: response.data['description'],
            printIsVoid: isCancel,
            screenshotController: screenshotController,
          ),
        );
        screenshotController
            .capture(
          delay: Duration(seconds: 2),
          pixelRatio: 1.0,
        )
            .then((Uint8List captureImage) async {
          String imageB64 = base64Encode(captureImage);
          await NativeBridge.getInstance().nativePrintBase64(imageB64);
          // await NativeBridge.getInstance().nativePrintPush(4);
          tempPrinterWidget.value = Container();
          _appController.hideLoading();
        }, onError: (e) {
          tempPrinterWidget.value = Container();
          _appController.hideLoading();
        });
      } else {
        if (isCancel) {
          AppUtils.showDialogError(context, '${response.code ?? ''}: ${response.message ?? ''}');
        } else {
          _printTransactionReceipt(transId, '');
        }
      }
    }
  }*/
}
