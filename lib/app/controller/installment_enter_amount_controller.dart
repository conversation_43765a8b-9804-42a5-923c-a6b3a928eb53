import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/route/app_pages.dart';
import 'package:mposxs/app/ui/screen/payment/widget/bottom_sheet_des.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/app_validation.dart';

import 'app_controller.dart';

class InstallmentEnterAmountController extends GetxController {
  late BuildContext context;
  RxString valueInput = '0'.obs;
  RxString textDes = ''.obs;
  RxList<int> listSuggest = [10000, 100000, 1000000].obs;
  final MyAppController _appController = Get.find<MyAppController>();

  @override
  void onReady() {
    super.onReady();
  }

  onPressClearValue() {
    valueInput.value = '0';
    listSuggest.value = [10000, 100000, 1000000];
  }

  onPressSuggest(int value) {
    valueInput.value = value.toString();
  }

  onPressDes() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
      ),
      builder: (context) => BottomSheetDes(
        value: textDes.value,
        onPressConfirm: (text) {
          textDes.value = text;
        },
      ),
      isScrollControlled: true,
      isDismissible: false,
    );
  }

  onPressClearDes() {
    textDes.value = '';
  }

  onPressChar(String char) {
    String tempString = valueInput.value;
    if (char == 'd') {
      if (tempString.length > 1) {
        tempString = tempString.substring(0, valueInput.value.length - 1);
      } else if (tempString.length == 1 && tempString != '0') {
        tempString = '0';
      }
    } else {
      if (tempString.length == 1 && tempString == '0') {
        if (char == '000') {
          tempString = '0';
        } else {
          tempString = char;
        }
      } else {
        tempString += char;
        if (tempString.length > 10) {
          tempString = tempString.substring(0, 10);
        }
      }
    }
    valueInput.value = tempString;
    listSuggest.value = tempString == '0'
        ? [10000, 100000, 1000000]
        : [
            int.parse(tempString) *
                (tempString.length < 3
                    ? 1000
                    : tempString.length < 4
                        ? 100
                        : tempString.length < 5
                            ? 10
                            : 1),
            int.parse(tempString) *
                (tempString.length < 3
                    ? 10000
                    : tempString.length < 4
                        ? 1000
                        : tempString.length < 5
                            ? 100
                            : tempString.length < 8
                                ? 10
                                : 1),
            int.parse(tempString) *
                (tempString.length < 3
                    ? 100000
                    : tempString.length < 4
                        ? 10000
                        : tempString.length < 5
                            ? 1000
                            : tempString.length < 8
                                ? 100
                                : 1)
          ];
  }

  String? validateInfo() {
    String? errorDes;
    if (!isNullEmpty(textDes.value) && textDes.value.length < 5) {
      errorDes = AppStrings.getString(AppStrings.errorInvalidDescriptionLength) ?? '';
    }
    return errorDes;
  }

  bool validate() {
    if (isNullEmptyFalseOrZero(valueInput.value)) {
      AppUtils.showDialogError(context, AppStrings.getString(AppStrings.installmentInfoErrorAmountEmpty) ?? '');
      return false;
    }
    if (int.parse(valueInput.value) < (_appController.installmentInfoSession?.selectedBank?.minAmount ?? 0)) {
      AppUtils.showDialogError(
          context,
          AppStrings.getString(AppStrings.installmentInfoErrorAmountMin)!
              .replaceFirst('%1s', _appController.installmentInfoSession?.selectedBank?.bankName ?? '')
              .replaceFirst(
                  '%2s',
                  AppUtils.formatCurrency(_appController.installmentInfoSession?.selectedBank?.minAmount ?? 0) +
                      'đ'));
      return false;
    }
    if (!isNullEmpty(_appController.installmentInfoSession?.selectedBank?.maxAmount) &&
        int.parse(valueInput.value) > (_appController.installmentInfoSession!.selectedBank!.maxAmount ?? 0)) {
      AppUtils.showDialogError(
          context,
          AppStrings.getString(AppStrings.installmentInfoErrorAmountMax)!
              .replaceFirst('%1s', _appController.installmentInfoSession!.selectedBank!.bankName!)
              .replaceFirst(
                  '%2s', AppUtils.formatCurrency(_appController.installmentInfoSession!.selectedBank!.minAmount) + 'đ'));
      return false;
    }
    String? validate = validateInfo();
    if (!isNullEmpty(validate)) {
      AppUtils.showDialogError(context, validate);
      return false;
    }
    return true;
  }

  onPressContinue() {
    if (validate()) {
      _appController.installmentInfoSession!.amount = int.parse(valueInput.value);
      Get.toNamed(AppRoute.installment_enter_info_screen, arguments: textDes.value);
    }
  }
}
