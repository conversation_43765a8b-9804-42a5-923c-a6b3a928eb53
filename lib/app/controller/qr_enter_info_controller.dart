import 'dart:convert';
import 'dart:math' as math;

import 'package:flutter/widgets.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/data/model/base_response.dart';
import 'package:mposxs/app/data/model/mp_data_login_model.dart';
import 'package:mposxs/app/data/provider/api_client.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/route/app_pages.dart';
import 'package:mposxs/app/util/api_constant.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/app_validation.dart';
import 'package:uuid/uuid.dart';

class QrEnterInfoController extends GetxController {
  BuildContext? context;
  MPQrChild? qrSource;
  MPBankQR? qrGroup;
  String? des;
  int amount = 0;
  double fee = 0;
  final MyAppController _appController = Get.find<MyAppController>();
  TextEditingController textDesController = TextEditingController();
  TextEditingController textPhoneController = TextEditingController();
  TextEditingController textEmailController = TextEditingController();
  RxString errorTextDes = ''.obs;
  RxString errorTextPhone = ''.obs;
  RxString errorTextEmail = ''.obs;
  RxBool keyboardVisible = false.obs;
  int? checkFeeChangeQr = 0;
  RxBool checkedFee = false.obs;

  @override
  void onInit() {
    var keyboardVisibilityController = KeyboardVisibilityController();
    keyboardVisibilityController.onChange.listen((bool visible) {
      keyboardVisible.value = visible;
    });
    qrGroup = Get.arguments['qrGroup'];
    qrSource = Get.arguments['qrSource'];
    des = Get.arguments['des'];
    checkFeeChangeQr = _appController.userInfo!.checkFeeChangeQr;
    checkedFee.value = _appController.userInfo!.checkFeeCustomerQr == 1;
    _initData();
    super.onInit();
  }

  double? _roundUp(num, precision) {
    precision = math.pow(10, precision);
    return (num * precision).ceil() / precision;
  }

  _initData() {
    amount = _appController.paymentInfoSession!.amount ?? 0;
    if (!isNullEmpty(des)) {
      textDesController.text = des!;
    }
    double x = qrSource?.fee ?? 0;
    double totalAmountTemp = _roundUp((amount * (100 / (100 - x))) / 1000, 0)! * 1000;
    fee = x == 0 ? 0 : totalAmountTemp - amount;
  }

  onChangedDes(text) {
    errorTextDes.value = '';
  }

  onChangedPhone(text) {
    String formattedText = AppUtils.formatPhoneNumber(text.replaceAll(' ', ''))!;
    textPhoneController.value =
        TextEditingValue(text: formattedText, selection: TextSelection.collapsed(offset: formattedText.length));
    errorTextPhone.value = '';
  }

  onChangedEmail(text) {
    errorTextEmail.value = '';
  }

  onPressChangeChecked() {
    checkedFee.value = !checkedFee.value;
  }

  onPressContinue() async {
    String? errorPhone;
    String? errorEmail;
    String? errorDes;
    String phone = textPhoneController.text;
    String email = textEmailController.text;
    String des = textDesController.text;
    if (!isNullEmpty(phone)) {
      errorPhone = checkValidPhone(context, phone);
    }
    if (!isNullEmpty(email)) {
      errorEmail = checkValidEmail(context, email);
    }
    // if (_appController.userInfo?.requiredInsDescription == true && isNullEmpty(des)) {
    //   errorDes = AppStrings.descriptionEmpty.tr;
    // }
    if (!isNullEmpty(des) && des.length < 5) {
      errorDes = AppStrings.getString(AppStrings.errorInvalidDescriptionLength) ?? '';
    }
    errorTextPhone.value = errorPhone ?? '';
    errorTextEmail.value = errorEmail ?? '';
    errorTextDes.value = errorDes ?? '';
    if (!isNullEmpty(errorPhone) || !isNullEmpty(errorEmail) || !isNullEmpty(errorDes)) {
      return;
    }
    String udid = '|${Uuid().v4()}';
    Map params = {
      'serviceName': 'PREPARE_TRANSACTION',
      'udid': udid,
      'deviceIdentifier': '',
      'muid': _appController.loginAccount,
      'merchantId': _appController.userInfo?.merchantId ?? 0,
      'paymentMethod': 'QR',
      'customerEmail': email,
      'customerMobile': phone.replaceAll(' ', ''),
      'description': des,
      'qrType': qrSource?.qrType,
      'amount': amount + (checkedFee.value ? fee : 0),
      'flatFee': fee,
      'amountBeforePayment': amount,
      'buyerBearFee': checkedFee.value,
      'shortNameChild': qrSource?.shortNameChild,
    };
    _appController.showLoading();
    BaseResponse response = await ApiClient.instance.request(url: ApiConstant.urlApi, data: json.encode(params));
    _appController.hideLoading();
    if (response.result!) {
      String? qrCode = response.data['qrCode'];
      if (isNullEmpty(qrCode)) {
        AppUtils.showDialogError(context!, AppStrings.getString(AppStrings.errorQrCodeEmpty)!);
      } else {
        _appController.paymentInfoSession!.selectedQrSource = qrSource;
        _appController.paymentInfoSession!.qrCode = qrCode;
        _appController.paymentInfoSession!.udid = udid;
        Get.toNamed(AppRoute.qr_code_screen, arguments: {'qrGroup': qrGroup});
      }
    } else {
      AppUtils.showDialogError(context!, '${response.code ?? ''}: ${response.message ?? ''}');
    }
  }
}
