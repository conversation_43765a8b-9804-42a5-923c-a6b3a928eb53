import 'dart:io';
import 'dart:ui';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/data/model/reader_mpos.dart';
import 'package:mposxs/app/data/provider/local_storage.dart';
import 'package:mposxs/app/data/provider/logger.dart';
import 'package:mposxs/app/data/provider/native_bridge.dart';
import 'package:mposxs/app/data/provider/session_data.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/route/app_pages.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/app_validation.dart';
import 'package:package_info/package_info.dart';

import 'app_controller.dart';

class SplashController extends GetxController {

  RxBool isDeviceConnected = false.obs;
  MyAppController _appController = Get.find<MyAppController>();

  @override
  void onReady() async {

    await Firebase.initializeApp();

    await FirebaseMessaging.instance.setAutoInitEnabled(true);

    _appController.fetchToken();
    // try {
    //   final fcmToken = await FirebaseMessaging.instance.getToken();
    //   _appController.deviceIdentifier = fcmToken;
    // } catch (e) {
    //   LoggerMp().writeLog('init deviceIdentifier err: ${e.toString()}');
    // }

    super.onReady();
    String langString = await LocalStorage().getLanguageApp();
    print('langString='+langString);
    if (isNullEmpty(langString)) {
      String currLangSys = Platform.localeName;
      print('currLangSys=$currLangSys');
      if(currLangSys!=AppStrings.localeCodeVi) {
        langString = 'en';
      } else {
        langString = 'vi';
      }
      LocalStorage().setLanguageApp(langString);
    }
    Get.updateLocale(langString == 'vi' ? Locale('vi', 'VN') : Locale('en', 'US'));
    // await Future.delayed(Duration(milliseconds: 1000));
    await _connectSmartPosDevice();
    getVersionApp();
    Get.offAndToNamed(AppRoute.login_screen);
  }

  _connectSmartPosDevice() async {
    NativeResponseModel<ReaderMpos?> nativeParams = await NativeBridge.getInstance().processGetSerialnumber();
    // NativeResponseModel nativeParams = await NativeBridge.getInstance().nativeCheckIsP20L();
    if (nativeParams.isSuccess) {
      AppUtils.log('connectSmartPosDevice: ' + nativeParams.data!.serialNumber!
          +" type=${nativeParams.data!.readerType}");
      _appController.serialNumber = nativeParams.data!.serialNumber;
      MyAppController.readerType = nativeParams.data!.readerType;
      isDeviceConnected.value = true;
      SessionData.serial = nativeParams.data!.serialNumber;
      LoggerMp().setUserAndDeviceInfo(deviceNo: _appController.serialNumber);
    } else {
      isDeviceConnected.value = false;
    }

    //TEST
    // await Future.delayed(Duration(milliseconds: 2000));
    // _appController.serialNumber = 'SP012007200006';
    // // _appController.serialNumber = 'SP012006150002';
    // _appController.readerType = MposConstant.READER_SP01;
    // isDeviceConnected.value = true;
    //
  }

  void getVersionApp() async {
    try {
      PackageInfo packageInfo = await PackageInfo.fromPlatform().timeout(Duration(seconds: 10));
      // _appController.appVersion = packageInfo.version;
      _appController.buildNumber = packageInfo.buildNumber;
      SessionData.version = packageInfo.version;
    }catch (e) {
      LoggerMp().writeLog('init getVersionApp err: ${e.toString()}');
    }
  }
}
