import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/data/provider/native_bridge.dart';
import 'package:mposxs/app/util/mpos_constant.dart';
import 'package:webview_flutter/webview_flutter.dart';

class WebViewInfoController extends GetxController {
  WebViewInfoArguments? webViewInfoArguments;
  late WebViewController webViewController;
  RxBool allowBack = false.obs;
  RxBool allowNext = false.obs;
  RxBool isLoadingWeb = true.obs;

  @override
  void onInit() {
    webViewInfoArguments = Get.arguments;
    super.onInit();
    _initWebViewController();
  }

  @override
  void onReady() {
    super.onReady();
  }

  _initWebViewController(){
    final PlatformWebViewControllerCreationParams params = const
    PlatformWebViewControllerCreationParams();


    webViewController = WebViewController.fromPlatformCreationParams(params);

    webViewController
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(const Color(0x00000000))
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {
            debugPrint('WebView is loading (progress : $progress%)');
          },
          onPageStarted: (String url) {
            debugPrint('Page started loading: $url');
            onPageStarted(url);
          },
          onPageFinished: (String url) {
            debugPrint('Page finished loading: $url');
            onPageFinished(url);
          },
          onWebResourceError: (WebResourceError error) {
            debugPrint('''
              Page resource error:
                code: ${error.errorCode}
                description: ${error.description}
                errorType: ${error.errorType}
                isForMainFrame: ${error.isForMainFrame}
                        ''');
          },
          onNavigationRequest: (NavigationRequest request) {
            if (request.url.startsWith('https://www.youtube.com/')) {
              debugPrint('blocking navigation to ${request.url}');
              return NavigationDecision.prevent;
            }
            debugPrint('allowing navigation to ${request.url}');
            return NavigationDecision.navigate;
          },
          onUrlChange: (UrlChange change) {
            debugPrint('url change to ${change.url}');
          },
        ),
      )
      ..loadRequest(Uri.parse(webViewInfoArguments?.url??MposConstant.URL_DEFAULT));
  }

  onPressWVBack() async {
    bool canGoBack = await webViewController.canGoBack();
    if (canGoBack) webViewController.goBack();
  }

  onPressWVReload() async {
    webViewController.reload();
  }

  onPressWVNext() async {
    bool canGoBack = await webViewController.canGoForward();
    if (canGoBack) webViewController.goForward();
  }

  onPageStarted(String url) {
    NativeBridge.getInstance().nativeHideKeyBoard();
    isLoadingWeb.value = true;
  }

  onPageFinished(String url) async {
    isLoadingWeb.value = false;
    bool canGoBack = await webViewController.canGoBack();
    bool canGoForward = await webViewController.canGoForward();
    allowBack.value = canGoBack;
    allowNext.value = canGoForward;
  }
}

class WebViewInfoArguments {
  final String url;
  final String? title;
  final bool showControl;

  WebViewInfoArguments(this.url, this.title, {this.showControl = true});
}
