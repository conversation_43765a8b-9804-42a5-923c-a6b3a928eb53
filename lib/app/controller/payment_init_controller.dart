import 'dart:convert';
import 'dart:developer';

import 'package:cashiermodule/BaseService/base_service.dart';
import 'package:cashiermodule/BaseService/mpos_request.dart';
import 'package:cashiermodule/Model/common_config.dart';
import 'package:cashiermodule/Model/device_mpos.dart';
import 'package:cashiermodule/Model/loginMacqModel.dart';
import 'package:cashiermodule/Pages/home_new/controller/home_new_controller.dart';
import 'package:cashiermodule/Pages/home_new/screen/installment_result_screen.dart';
import 'package:cashiermodule/Pages/settings/widget/base_bottom_sheet.dart';
import 'package:cashiermodule/Utilities/LocalizationCustom.dart';
import 'package:cashiermodule/Utilities/NativeBridge.dart' as nb;
import 'package:cashiermodule/constants/style.dart';
import 'package:cashiermodule/model_instance/app_configuration.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mpos_module_base/mpos_module_base_master.dart';
import 'package:mpos_module_base/mpos_module_base_model.dart' as md;
import 'package:mpos_module_base/src/util/mp_constant.dart';
import 'package:mpos_module_qr/mpos_module_qr.dart' as moduleQr;
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/controller/payment_finish_controller.dart';
import 'package:mposxs/app/controller/webview_info_controller.dart';
import 'package:mposxs/app/data/model/base_response.dart';
import 'package:mposxs/app/data/model/mp_data_login_model.dart';
import 'package:mposxs/app/data/model/qr_data_config.dart';
import 'package:mposxs/app/data/model/type_payment.dart';
import 'package:mposxs/app/data/provider/api_client.dart';
import 'package:mposxs/app/data/provider/local_storage.dart';
import 'package:mposxs/app/data/provider/logger.dart';
import 'package:mposxs/app/data/provider/native_bridge.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/route/app_pages.dart';
import 'package:mposxs/app/ui/screen/payment/payment_init_screen.dart';
import 'package:mposxs/app/ui/screen/payment/widget/bottom_sheet_des.dart';
import 'package:mposxs/app/ui/screen/payment/widget/bottom_sheet_select_type_pay.dart';
import 'package:mposxs/app/ui/screen/settings/bottomsheet_setting_order_payment.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/widget/qr_wallet_widget.dart';
import 'package:mposxs/app/util/api_constant.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/app_validation.dart';
import 'package:mposxs/app/util/constants.dart';
import 'package:mposxs/app/util/mpos_constant.dart';
import 'package:uuid/uuid.dart';

import '../data/model/data_session.dart';
import '../data/model/log_error_model.dart';
import '../ui/theme/app_dimens.dart';

class PaymentInitController extends GetxController {
  late BuildContext context;
  RxString valueInput = '0'.obs;
  RxString textDes = ''.obs;
  RxList<int> listSuggest = [10000, 100000, 1000000].obs;
  // bool showButtonSwipeCard = false;
  // bool showButtonQR = false;
  // bool showButtonInstallment = false;
  // bool showButtonMoto = false;
  // bool showButtonLinkCard = false;
  double _minAmount = MposConstant.MIN_AMOUNT_SCAN;
  final MyAppController _appController = Get.find<MyAppController>();

  bool permitScanCard = true;   // chỉ cho phép 1 giao dịch được thực hiện 1 thời điểm.
  // RxList<TypePayment> listBtn = RxList();
  // TypePayment btn1st, btn2nd;

  RxBool isShowSuggest = true.obs;
  MyCallbackAmount? callback;

  // var appCfPushPayment = AppConfiguration();

  void setCallback(MyCallbackAmount? cb) {
    callback = cb;
  }

  void cbAmountToScreen(String amount) {
    if (callback != null) {
      callback!(amount);
    }
  }

  @override
  Future<void> onInit() async {
    // appCfPushPayment.appType = AppType.PACKAGE;
    // if (MyAppController.isKozenP12()) {
    //   appCfPushPayment.setIsKozenP12(MyAppController.isKozenP12());
    // }
    // appCfPushPayment.setEnvironment(Environment.PROD);

    if (_appController.userInfo?.minAmount != null && _appController.userInfo!.minAmount! > 0) {
      _minAmount = _appController.userInfo!.minAmount!.toDouble();
    }


    // todo fake test
    // showButtonSwipeCard = true;
    // showButtonQR = true;
    // showButtonInstallment = true;

    _appController.listTypePayment.clear();
    String paymentsTypeCache = await _appController.localStorage.getData(LocalStorage.KEY_PAYMENT_HOME, '');
    AppUtils.log('paymentsTypeCache= $paymentsTypeCache');
    if (paymentsTypeCache=='') {
      if(_appController.paymentMethod!.swipeCardActive) {
        _appController.listTypePayment.add(TypePayment(
            sub_name: _appController.paymentMethod!.sub_cardPayment,
            sub_nameEn: _appController.paymentMethod!.sub_cardPaymentEn,
            title: AppStrings.getString(AppStrings.scanCard) ?? '',
            bgColor: AppColors.btn_scan_Card,
            typePay: TypePay.SWIPE_CARD,
            position: 0));
      }

      if (MyAppController.isKozenP12orN4()) {
        if (_appController.paymentMethod!.vietQrActive) {
          _appController.listTypePayment.add(TypePayment(
              sub_name: _appController.paymentMethod!.sub_qrCodePayment,
              sub_nameEn: _appController.paymentMethod!.sub_qrCodePaymentEn,
              title: AppStrings.getString(AppStrings.tv_title_vietQR),
              bgColor: AppColors.btn_vietQR,
              typePay: TypePay.QR_PAY,
              position: 1));
        }
      }else if (_appController.paymentMethod!.qrCodePaymentActive) {
        _appController.listTypePayment.add(TypePayment(
            sub_name: _appController.paymentMethod!.sub_qrCodePayment,
            sub_nameEn: _appController.paymentMethod!.sub_qrCodePaymentEn,
            title: AppStrings.getString(AppStrings.labelQRPay),
            bgColor: AppColors.bgButton,
            typePay: TypePay.QR_PAY,
            position: 1));
      }

      // if (AppController.isKozenP12()) {
      //   _appController.listTypePayment
      //       .add(TypePayment(title: AppController.isKozenP12() ? AppStrings.tv_title_vietQR.tr : AppStrings.labelQRPay.tr,
      //       bgColor: AppController.isKozenP12() ? AppColors.bg_btn_qr : AppColors.bgButton, typePay: TypePay.QR_PAY, position: 1));
      // }

      if(MyAppController.isKozenP12orN4() && _appController.paymentMethod!.installmentActive) {
        _appController.listTypePayment
            .add(TypePayment(title: AppStrings.getString(AppStrings.installment), bgColor: AppColors.btn_installment, typePay: TypePay.INSTALLMENT, position: 2));
      }
      if (_appController.paymentMethod!.walletQrActive) {
        _appController.listTypePayment
            .add(TypePayment(title: AppStrings.getString(AppStrings.title_qr_wallet), bgColor: AppColors.btn_wallet, typePay: TypePay.QR_WALLET, position: 3));
      }
      if(MyAppController.isKozenP12orN4() && _appController.paymentMethod!.motoActive) {
        _appController.listTypePayment.add(TypePayment(
            sub_name: _appController.paymentMethod!.sub_motoPayment,
            sub_nameEn: _appController.paymentMethod!.sub_motoPaymentEn,
            title: AppStrings.getString(AppStrings.moto),
            bgColor: AppColors.btn_moto,
            typePay: TypePay.MOTO,
            position: 4));
      }
      if (MyAppController.isKozenP12orN4() && _appController.paymentMethod!.linkCardActive) {
        _appController.listTypePayment
            .add(TypePayment(title: AppStrings.getString(AppStrings.labelCreateLink), bgColor: AppColors.btn_link, typePay: TypePay.CREATE_LINK, position: 5));
      }
      if (MyAppController.isKozenP12orN4() && _appController.paymentMethod!.bnplActive) {
        _appController.listTypePayment.add(TypePayment(
            sub_name: _appController.paymentMethod!.sub_bnplPayment,
            sub_nameEn: _appController.paymentMethod!.sub_bnplPaymentEn,
            title: AppStrings.getString(AppStrings.labelBnpl),
            bgColor: AppColors.btn_BNPL,
            typePay: TypePay.BNPL,
            position: 6));
      }
      if(MyAppController.isKozenP12orN4() && _appController.paymentMethod!.installmentActive) {
        _appController.listTypePayment
            .add(TypePayment(title: AppStrings.getString(AppStrings.check_card_installment), bgColor: AppColors.btn_check_card, typePay: TypePay.CHECK_INSTALLMENT_CARD, position: 7));
      }

      if (_appController.paymentMethod!.depositPayment) {
        _appController.listTypePayment.add(TypePayment(
            title: AppStrings.getString(AppStrings.title_deposit),
            bgColor: AppColors.btn_deposit,
            typePay: TypePay.DEPOSIT,
            position: 8,
            textStyle: TextStyle(
              fontSize: AppDimens.textSizeMedium,
              fontWeight: FontWeight.w500,
              fontFamily: kFontFamilyBeVietnamPro,
              color: AppColors.white,
            )));
      }
    } else {
      List<dynamic> parsedListJson = jsonDecode(paymentsTypeCache);
      List<TypePayment> listTypePaymentCache = List<TypePayment>.from(parsedListJson.map<TypePayment>((dynamic i) => TypePayment.fromJson(i)));
      bool cacheHasSwipeCard = false;
      listTypePaymentCache.forEach((element) {
        switch(element.typePay){
          case TypePay.SWIPE_CARD:
            cacheHasSwipeCard = true;
            if(_appController.paymentMethod!.swipeCardActive){
              _appController.listTypePayment.add(element);
            }
            break;
          case TypePay.QR_PAY:
            if(_appController.paymentMethod!.qrCodePaymentActive){
              _appController.listTypePayment.add(element);
            }
            break;
          case TypePay.INSTALLMENT:
            if(_appController.paymentMethod!.installmentActive){
              _appController.listTypePayment.add(element);
            }
            break;
          case TypePay.QR_WALLET:
            if(_appController.paymentMethod!.walletQrActive){
              _appController.listTypePayment.add(element);
            }
            break;
          case TypePay.MOTO:
            if(_appController.paymentMethod!.motoActive){
              _appController.listTypePayment.add(element);
            }
            break;
          case TypePay.CREATE_LINK:
            if(_appController.paymentMethod!.linkCardActive){
              _appController.listTypePayment.add(element);
            }
            break;
          case TypePay.BNPL:
            if (_appController.paymentMethod!.bnplActive) {
              _appController.listTypePayment.add(element);
            }
            break;
          case TypePay.CHECK_INSTALLMENT_CARD:
            if (_appController.paymentMethod!.installmentActive) {
              _appController.listTypePayment.add(element);
            }
            break;
          case TypePay.DEPOSIT:
            if (_appController.paymentMethod!.depositPayment) {
              _appController.listTypePayment.add(element);
            }
            break;
          default:
            break;
        }
      });
      if(_appController.paymentMethod!.numPayMethodActive != _appController.listTypePayment.length && !cacheHasSwipeCard && _appController.paymentMethod!.swipeCardActive) {
        _appController.listTypePayment
            .add(TypePayment(title: AppStrings.getString(AppStrings.scanCard), bgColor: AppColors.primary, typePay: TypePay.SWIPE_CARD, position: 0));
      }
    }

    _appController.listTypePayment.add(TypePayment(
        title: AppStrings.getString(AppStrings.menuGuide),
        bgColor: AppColors.white,
        typePay: TypePay.MANUAL,
        position: 9,
        textStyle: TextStyle(
          fontSize: AppDimens.textSizeMedium,
          fontWeight: FontWeight.w500,
          fontFamily: kFontFamilyBeVietnamPro,
          color: AppColors.black,
        )));
    super.onInit();
  }

  @override
  void onReady() async {
    permitScanCard = true;
    super.onReady();
  }

  getListBtn(){
    return _appController.listTypePayment;
  }

  getSizeListBtn(){
    return _appController.listTypePayment.length;
  }

  onPressClearValue() {
    valueInput.value = '0';
    listSuggest.value = [10000, 100000, 1000000];
  }

  onPressSuggest(int value) {
    valueInput.value = value.toString();
    cbAmountToScreen(valueInput.value);
  }

  onPressDes() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
      ),
      builder: (context) => BottomSheetDes(
        value: textDes.value,
        onPressConfirm: (text) {
          textDes.value = text;
        },
      ),
      isScrollControlled: true,
      isDismissible: false,
    );
  }

  onPressSelectTypePay() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
      ),
      builder: (context) => Container(
        height: (_appController.listTypePayment.length > 8) ? MediaQuery.of(context).size.height : null,
        child: SelectTypePay(
          onSelect: (typePay) {
            handleSelectTypePay(typePay);
          },
          onPressSetting: (){
            onSelectSettingOrderPayment();
          },
          listTypePayment: _appController.listTypePayment,
        ),
      ),
      isScrollControlled: true,
      isDismissible: false,
    );
  }

  onSelectSettingOrderPayment(){
    Get.back();

    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
      ),
      builder: (context) => SettingOrderPayment(
      ),
      isScrollControlled: true,
      isDismissible: false,
    );
  }

  onPressClearDes() {
    textDes.value = '';
  }

  onLongPressChar(String char) {
    if (char == 'd') {
      valueInput.value = '0';
      listSuggest.value = [10000, 100000, 1000000];
      cbAmountToScreen(valueInput.value);
    }
  }

  onPressChar(String char) {
    String tempString = valueInput.value;
    if (char == 'd') {
      if (tempString.length > 1) {
        tempString = tempString.substring(0, valueInput.value.length - 1);
      } else if (tempString.length == 1 && tempString != '0') {
        tempString = '0';
      }
    } else {
      if (tempString.length == 1 && tempString == '0') {
        if (char == '000') {
          tempString = '0';
        } else {
          tempString = char;
        }
      } else {
        tempString += char;
        if (tempString.length > 10) {
          tempString = tempString.substring(0, 10);
        }
      }
    }
    valueInput.value = tempString;
    listSuggest.value = tempString == '0'
        ? [10000, 100000, 1000000]
        : [
            int.parse(tempString) *
                (tempString.length < 3
                    ? 1000
                    : tempString.length < 4
                        ? 100
                        : tempString.length < 5
                            ? 10
                            : 1),
            int.parse(tempString) *
                (tempString.length < 3
                    ? 10000
                    : tempString.length < 4
                        ? 1000
                        : tempString.length < 5
                            ? 100
                            : tempString.length < 8
                                ? 10
                                : 1),
            int.parse(tempString) *
                (tempString.length < 3
                    ? 100000
                    : tempString.length < 4
                        ? 10000
                        : tempString.length < 5
                            ? 1000
                            : tempString.length < 8
                                ? 100
                                : 1)
          ];

    cbAmountToScreen(valueInput.value);
  }

  String? validateInfo() {
    String? errorDes;
    if (!isNullEmpty(textDes.value) && textDes.value.length < 5) {
      errorDes = AppStrings.getString(AppStrings.errorInvalidDescriptionLength);
    }
    return errorDes;
  }

  onPressCreateQR() async {
    if ((_appController.userInfo!.listBankQR ?? []).length == 0) {
      AppUtils.showDialogError(context, AppStrings.getString(AppStrings.messageInvalidService));
      return;
    }
    if (validateAmount(Get.find<PaymentInitController>().context, valueInput.value, _minAmount)) {
      String? validate = validateInfo();
      if (isNullEmpty(validate)) {
        _appController.paymentInfoSession = PaymentInfoSession()
          ..typePayment = MposConstant.QR_PAYMENT
          ..amount = int.parse(valueInput.value)
          ..description = textDes.value
          ..needResetAmount = false;
        await Get.toNamed(AppRoute.qr_list_source_screen, arguments: textDes.value);
        if (_appController.paymentInfoSession!.needResetAmount!) {
          _appController.paymentInfoSession = null;
          valueInput.value = '0';
          textDes.value = '';
        }
      } else {
        AppUtils.showDialogError(context, validate);
      }
    }
  }

  onPressScanCard() async {
    _appController.appendLog('scan card: ${int.parse(valueInput.value)}');
    if (!permitScanCard && (_appController.userInfo?.config?.connectType == 4)) {
      _appController.appendLog('busy, doing payment');
      return;
    }
    permitScanCard = false;

    if (context == null) {
      _appController.appendLog('context is null in onPressScanCard');
      permitScanCard = true;
      return;
    }

    if (validateAmount(context, valueInput.value, _minAmount)) {
      String? validate = validateInfo();
      if (isNullEmpty(validate)) {
        _appController.paymentInfoSession = PaymentInfoSession()
          ..typePayment = MposConstant.CARD_PAYMENT
          ..amount = int.parse(valueInput.value)
          ..description = textDes.value
          ..needResetAmount = false;
        String udid = '|${Uuid().v4()}';
        Map paramsPrepare = {
          'serviceName': 'PREPARE_TRANSACTION',
          'udid': udid,
          'deviceIdentifier': '',
          'muid': _appController.loginAccount,
          'customerEmail': '',
          'customerMobile': '',
          'merchantId': _appController.userInfo?.merchantId ?? 0,
          'paymentMethod': 'CARD',
          'description': textDes.value,
        };

        bool hasErrorCodePrepay = await _appController.localStorage.getData(LocalStorage.KEY_HAS_ERROR_CODE_PREPARE, false);

        _appController.localStorage.addLogError(LogErrorModel(type: 'ACT', errorMessage: 'scan card: udid=$udid'));

        if (hasErrorCodePrepay) {
          _appController.showLoading();
          BaseResponse response = await ApiClient.instance.request(url: ApiConstant.urlApi, data: json.encode(paramsPrepare));
          _appController.hideLoading();
          if (response.result!) {
            _saveHasErrorCodePrepare(false);
            await _callNativeMethodScanCard(udid, textDes.value);
          } else {
            if (response.code == ApiClient.ERROR_TIMEOUT || response.code == ApiClient.ERROR_SOCKET_EXCEPTION) {
              await _callNativeMethodScanCard(udid, textDes.value);
            } else {
              _saveHasErrorCodePrepare(true);
              AppUtils.showDialogError(context, '${response.code ?? ''}: ${response.message ?? ''}');
            }
          }
        } else {
          ApiClient.instance.request(url: ApiConstant.urlApi, data: json.encode(paramsPrepare))
              .then((response) => {
            if (response.result!) {
              _saveHasErrorCodePrepare(false)
            } else {
              _saveHasErrorCodePrepare(true)
            }
          });
          await _callNativeMethodScanCard(udid, textDes.value);
        }
      } else {
        AppUtils.showDialogError(context, validate);
      }
    }else {
      permitScanCard = true;
    }
  }

  gotoP12QrScreen(BuildContext context, String qrType, String qrChirdName, String logoQr, String amountMin, {String? amount, String? orderID, int? autoCloseFinishScreen, bool? isPaymentSocket}) async {
    double minAmount = _minAmount;
    if (amountMin.isNotEmpty) {
      minAmount = double.parse(amountMin);
    }
    if (validateAmount(context, amount ?? valueInput.value, minAmount)) {
      await SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
      Get.toNamed(AppRoute.qr_code_p12_screen, arguments: {
        'amount': amount ?? valueInput.value,
        'qrType': qrType,
        'qrChirdName': qrChirdName,
        'logoQr': logoQr,
        'typePay': TypePay.QR_PAY,
        'orderID': orderID ?? "",
        'autoCloseFinishScreen': autoCloseFinishScreen ?? 15,
        'isPaymentSocket': isPaymentSocket
      });
    }else {
      if (_appController.userInfo?.config?.connectType == 4) {
        NativeBridge.getInstance().nativeCallBackResultPaymentSocket('TRANS_ERROR', orderID, amount ?? "", qrType, "");
      }
    }
  }

  handleSelectTypePay(TypePay? typePay, {String? codeBNPLItem, String? amountMin}) async {
    AppUtils.log('handleSelectTypePay: $typePay');

    Get.back();
    switch (typePay){
      case TypePay.SWIPE_CARD:
        onPressScanCard();
        break;
      case TypePay.CREATE_LINK:
        onPressCreateLink();
        break;
      case TypePay.INSTALLMENT:
        if (_appController.userInfo?.mores?.tgVer == '1') {
          // new installment flow
          _onPressFeatureInstallment();
        }else {
          // old installment flow
          onSelectInstallment();
        }
        break;
      case TypePay.MOTO:
        onSelectMoto();
        break;
      case TypePay.QR_PAY:
        if(MyAppController.isKozenP12orN4()) {
          String configQr = await _appController.localStorage.getConfigQrP12();
          if(configQr.isEmpty) {
            fetchConfigQrP12(context, true);
          }else {
            QrChildren qrDataConfig = AppUtils.getQRChidWithMethodPayment(configQr, MposConstant.name_group_qr_VAQR);
            gotoP12QrScreen(Get.find<PaymentInitController>().context, qrDataConfig.qrType ?? MposConstant.name_group_qr_VAQR, qrDataConfig.shortNameChild ?? MposConstant.qr_name_VietQr, '', qrDataConfig.amountMin ?? "");
            // gotoP12QrScreen(Get.find<PaymentInitController>().context, MposConstant.name_group_qr_VAQR, MposConstant.qr_name_VietQr ,'', '');
          }
        } else {
          onPressCreateQR();
        }
        break;
      case TypePay.QR_WALLET:
        onPressQrWallet();
        break;
      case TypePay.BNPL:
        onPressPayBNPL();
        break;
      case TypePay.CHECK_INSTALLMENT_CARD:
        onPressCheckInstallmentCard();
        break;
      case TypePay.DEPOSIT:
        onPressDeposit();
        break;
      case TypePay.MANUAL:
        onPressManualUse();
        break;
      case TypePay.INSTALLMENT_CARD:
        _onPressFeatureInstallmentCard(false);
        break;
      case TypePay.INSTALLMENT_LINK:
        _onPressFeatureCreateLinkInstallment();
        break;
      case TypePay.INSTALLMENT_BNPL:
        onPressPayBNPL(codeBNPLItem: codeBNPLItem);
        // _onPressFeatureInstallmentBNPL(codeModule, codeBNPLItem, amountMin);
        break;
    }
  }

  onPressDeposit() async {
    LoggerMp().writeLog('goto deposit');
    // Get.back();
    // initConfigCashierModule();
    // await Get.find<MainController>().initConfigCashierModule().timeout(
    //     Duration(milliseconds: 1000));
    Get.toNamed(AppRoute.DEPOSIT_PAGE);
  }

  onSelectInstallment() async {
    LoggerMp().writeLog('old installment flow');
    if (_appController.userInfo?.installmentInfo == null || _appController.userInfo!.installmentInfo!.length == 0) {
      Get.toNamed(AppRoute.webview_info_screen,
          arguments: WebViewInfoArguments(MposConstant.URL_LANDING_INSTALLMENT, null));
    } else {
      Get.toNamed(AppRoute.installment_list_bank_screen);
    }
  }


  _onPressFeatureInstallmentCard(bool showPayLink) async {
    LoggerMp().writeLog('new installment flow');
    if (validateAmount(Get.find<PaymentInitController>().context, valueInput.value, _minAmount)) {
      LoggerMp().writeLog('onPressFeatureInstallmentCard');
      // await Get.find<MainController>().initConfigCashierModule().timeout(
      //     Duration(milliseconds: 1000));

      MPMasterAction.instance.init(
        baseUrl: BaseService().getApiBaseUrl(),
        // baseUrl: BaseService().getBaseUrlMPOS(),
        showLog: AppConfiguration().showLog,
        muid: _appController.loginAccount,
        serial: _appController.serialNumber,
        merchantId: int.parse(_appController.userInfo?.merchantId ?? '0'),
        rootNavigator: false,
        forceInit: true,
        mpDevice: AppConfiguration().isKozenP12 ? MPConstantDevice.n31 : null,
      );

      AppConfiguration().setLoginMacqModel(LoginMacqModel());
      AppConfiguration().loginMacqModel.deviceIdentifier =
          _appController.deviceIdentifier ?? '';
      Map argumentsInput = {
        'title': "Kiểm tra thẻ trả góp",
        'showPayLink': showPayLink,
        'amount': double.parse(valueInput.value),
        'allowOtherPaymentMethod': true,
        'onSwipeSuccess': (resultCard) async {
          if (resultCard['useOtherPaymentMethod'] == true) {
            LoggerMp().writeLog('useOtherPaymentMethod');
            String udid = '|${Uuid().v4()}';
            _callNativeMethodScanCard(udid, '');
          } else if (resultCard['data'] != null) {
            print('uffooo: ${resultCard['data']}');
            String mpDataLoginModel = await _appController.localStorage.getGatewayMcConfig();
            doFunctionInstallmentPublic(
                context: context,
                mpDataLoginModel:
                md.MPDataLoginModel.fromJson(json.decode(mpDataLoginModel)),
                amount: double.parse(valueInput.value),
                isPayLink: resultCard['isPayLink'] ?? false,
                dataInput: resultCard['data'],
                processHandleResultPay: _processHandleResultPayInstallment,
                currentDescription: textDes.value,
                onPressFeatureInstallmentCard: () =>
                    _onPressFeatureInstallmentCard(false),
                allowOtherPaymentMethod: false,
                callbackOtherPaymentMethod: (_) {
                  print('callbackOtherPaymentMethod');
                });
          }
        },
      };
      Get.toNamed(AppRoute.swipeCard, arguments: argumentsInput);
    }
  }

  RxString recentConnectedDeviceSerial = ''.obs;

    _processHandleResultPayInstallment(String data, Map dataInstallment) async {
    Map result = jsonDecode(data);
    debugPrint('///////////////////// _processHandleResultPayInstallment: $result');
    debugPrint('///////////////////// _processHandleInstallmentData: $dataInstallment');
    debugPrint('///////////////////// locale: ${Get.locale.toString()}');
    if (result['result']['status'] == 'APPROVED') {
      // remove curr data pay
      // _resetDataPay();
      bool checkPrint = await nb.NativeBridge().isSupportPrinter();
      Get.to(
        N31InstallmentResult(
          resultFromMposSDK: result,
          installmentResultEntity: InstallmentResultModel(
            success: true,
            errorMsg: '',
            customerName: result['userCard']?['cardHolderName'] ?? '',
            cardType: result['wfInfo']?['issuerCode'] ?? '',
            cardNumber: result['userCard']?['pan'] ?? '',
            time: DateFormat('HH:mm, dd/MM/yyyy').format(DateTime.fromMillisecondsSinceEpoch(
                (int.tryParse(result['userCard']['transactionDate'].toString()) ?? 0))),
            tranCode: result['result']?['transactionId'] ?? '',
            amount: (result['userCard']?['amountAuthorized'] ?? 0).toString(),
            originalAmount: (dataInstallment['originalAmount'] ?? 0).toString(),
            fee: (dataInstallment['customerInstallmentFee'] ?? 0).toString(),
            period: (dataInstallment['period'] ?? 0).toString(),
            monthly: ((double.tryParse(result['userCard']['amountAuthorized'].toString()) ?? 0.0) /
                (double.tryParse(dataInstallment['period'].toString()) ?? 1.0))
                .round()
                .toString(),
            userName: AppConfiguration().mobileUser,
            serial: AppConfiguration().deviceMpos.deviceType == DeviceType.READER_SP01.index ||
                AppConfiguration().deviceMpos.deviceType == DeviceType.READER_SP02.index
                ? AppConfiguration().deviceMpos.deviceSerial
                : recentConnectedDeviceSerial.value,
            isFlagNoSignature: result['wfInfo']?['isFlagNoSignature'] == true,
            enablePrint: checkPrint,
            ruleNote: dataInstallment['ruleNote'] ?? '',
            iconTypeCard: dataInstallment['iconTypeCard'] ?? '',
            followKey: dataInstallment['followKey'] ?? '',
            promotionCode: dataInstallment['payloadPrepareTransaction']?['installmentInfo']?['promotionCode'] ?? '',
          ),
        ),
        opaque: false,
      );
    } else {
      Get.to(
        N31InstallmentResult(
          resultFromMposSDK: result,
          installmentResultEntity: InstallmentResultModel(
            success: false,
            errorMsg: result['result']?['error']?['message'] ?? '',
            customerName: dataInstallment['cardholder'] ?? '',
            cardType: dataInstallment['payloadPrepareTransaction']?['installmentInfo']?['issuerCode'] ?? '',
            cardNumber: result['userCard']?['pan'] ?? '',
            time: DateFormat('HH:mm, dd/MM/yyyy').format(DateTime.now()),
            tranCode: '',
            amount: (result['userCard']?['amountAuthorized'] ?? 0).toString(),
            originalAmount: (dataInstallment['originalAmount'] ?? 0).toString(),
            fee: (dataInstallment['customerInstallmentFee'] ?? 0).toString(),
            period: (dataInstallment['period'] ?? 0).toString(),
            monthly: ((double.tryParse(result['userCard']['amountAuthorized'].toString()) ?? 0.0) /
                (double.tryParse(dataInstallment['period'].toString()) ?? 1.0))
                .round()
                .toString(),
            userName: AppConfiguration().mobileUser,
            serial: AppConfiguration().deviceMpos.deviceType == DeviceType.READER_SP01.index ||
                AppConfiguration().deviceMpos.deviceType == DeviceType.READER_SP02.index
                ? AppConfiguration().deviceMpos.deviceSerial
                : recentConnectedDeviceSerial.value,
            isFlagNoSignature: true,
            enablePrint: false,
            ruleNote: dataInstallment['ruleNote'] ?? '',
            iconTypeCard: dataInstallment['iconTypeCard'] ?? '',
            followKey: dataInstallment['followKey'] ?? '',
            promotionCode: dataInstallment['payloadPrepareTransaction']?['installmentInfo']?['promotionCode'] ?? '',
          ),
        ),
        opaque: false,
      );
    }
  }

  _saveHasErrorCodePrepare(bool hasError){
    _appController.localStorage.saveData(LocalStorage.KEY_HAS_ERROR_CODE_PREPARE, hasError);
  }

  _callNativeMethodScanCard(String udid, String des) async {
    Map params = {
      'amount': valueInput.value,
      'phone': '',
      'email': '',
      'description': des,
      'paymentID': udid //anhvt them vao de ca iOS va Android SDK su dung, khong de generate tu duoi sdk nua
    };
    // todo fake test
    // fail
    // String result = '{"needUpdateConfig": false,"numberSaleRemain": 0,"result": {  "error": {    "code": 0,    "httpCode": 500,    "message": "51 - Thẻ của bạn không có đủ tiền để giao dịch. Vui lòng liên hệ ngân hàng phát hành hoặc đổi thẻ khác để tiếp tục giao dịch"    },    "paymentIdentifier": "|bbadc9bb-8396-4108-9104-edac1f719217",    "status": "TRANS_ERROR",    "transStatus": 0  },  "userCard": {    "amountAuthorized": 2000000,    "pan": "97040799****1273"  }}';
    // timeout
    // String result = '{  "needUpdateConfig": false,  "numberSaleRemain": 0,  "result": {    "error": {      "details": [        {          "metadata": {            "wfId": "635a0a615c16936dd1cdb99e"          }        }      ],      "code": 0,      "httpCode": 500,      "message": "Mất kết nối. Vui lòng thử lại."    },    "paymentIdentifier": "|2535ee33-60eb-43aa-b975-a0fd8d05e107",    "status": "TRANS_ERROR",    "transStatus": 0  },  "userCard": {    "amountAuthorized": 5000,    "pan": "44509300****7837"  }}';
    // success
    // String result = '{  "needUpdateConfig": false,  "numberSaleRemain": 0,  "result": {    "amount": "1000",    "paymentIdentifier": "|86fb61ec-ec0a-4798-9f66-669181d80858",    "status": "APPROVED",    "trId": "",    "transactionId": "2022100716517635718",    "transStatus": 100  },  "userCard": {    "amountAuthorized": 1000,    "applicationLabel": "NAPAS",    "authCode": "151403",    "cardHolderName": "NGUYEN TUAN ANH",    "pan": "970407******1273",    "transactionDate": "1665136265521",    "userSignature": "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"}}';
    // NativeResponseModel nativeResponseModel = NativeResponseModel(true, result, null);
    // _appController.showLoading();
    NativeResponseModel nativeResponseModel = await NativeBridge.getInstance().nativePaymentScanCard(params);
    LoggerMp().writeLog('End payment Scan card ${DateTime.now()}');

    permitScanCard = true;
    // _appController.hideLoading();
    if (nativeResponseModel.isSuccess) {
      await _appController.playSoundPromotion(nativeResponseModel.data).timeout(Duration(seconds: 3);
      final Map responseMap = json.decode(nativeResponseModel.data);
      responseMap['emailSendReceipt'] = '';
      Map? result = responseMap['result'];
      if (result != null && result['status'] != 'CANCEL') {
        await Get.toNamed(AppRoute.payment_finish_screen,
            arguments: PaymentFinishArguments(MposConstant.CARD_PAYMENT, responseMap, ''));
        if (_appController.paymentInfoSession!.needResetAmount!) {
          _appController.paymentInfoSession = null;
          valueInput.value = '0';
          textDes.value = '';
        }
      }
    } else {
      await Get.toNamed(AppRoute.payment_finish_screen,
          arguments: PaymentFinishArguments(
              MposConstant.CARD_PAYMENT,
              null,
              AppUtils.getErrorByCode(context, nativeResponseModel.error?.code,
                  defaultMessage: nativeResponseModel.error?.message)));
      if (_appController.paymentInfoSession!.needResetAmount!) {
        _appController.paymentInfoSession = null;
        valueInput.value = '0';
        textDes.value = '';
      }
    }
  }

  void fetchConfigQrP12(BuildContext context, bool isGotoQrScreen, {String? amount, String? orderID, int? autoDismissDlgTimer, String? paymentMethod, bool? isPaymentSocket}) async {
    _appController.showLoading();
    NativeResponseModel nativeResponseModel = await NativeBridge.getInstance().nativeFetchConfigQr(_appController.loginAccount);
    _appController.hideLoading();

    if (nativeResponseModel.isSuccess && (nativeResponseModel.data != null)) {
      log('fetchConfigQr success: data=${nativeResponseModel.data}');
      // save QR, show QR,
      _appController.localStorage.setConfigQrP12(nativeResponseModel.data);
      if (isGotoQrScreen) {
        if (paymentMethod != null){
          QrChildren qrChildren = AppUtils.getQRChidWithMethodPayment(nativeResponseModel.data, paymentMethod);
          if (qrChildren.qrType != null) {
            gotoP12QrScreen(context, qrChildren.qrType!, qrChildren.shortNameChild!, qrChildren.logoChild ?? '', qrChildren.amountMin ?? '', amount: amount, orderID: orderID, autoCloseFinishScreen: autoDismissDlgTimer, isPaymentSocket: isPaymentSocket);
          }else if (_appController.userInfo?.config?.connectType == 4) {
            NativeBridge.getInstance().nativeCallBackResultPaymentSocket('TRANS_ERROR', orderID, amount ?? "", paymentMethod, "" ,errCode: MposConstant.ERROR_NOT_SUPPORT);
          }
          // gotoP12QrScreen(context, MposConstant.name_group_qr_VAQR, MposConstant.qr_name_VietQr, '', '', amount: amount, orderID: orderID, autoCloseFinishScreen: autoDismissDlgTimer);
        }else { //default paymentMethod == "" -> VAQR.
          QrChildren qrDataConfig = AppUtils.getQRChidWithMethodPayment(nativeResponseModel.data, MposConstant.name_group_qr_VAQR);
          gotoP12QrScreen(context, qrDataConfig.qrType ?? MposConstant.name_group_qr_VAQR, qrDataConfig.shortNameChild ?? MposConstant.qr_name_VietQr, '', qrDataConfig.amountMin ?? '', isPaymentSocket: isPaymentSocket);
          // gotoP12QrScreen(context, MposConstant.name_group_qr_VAQR, MposConstant.qr_name_VietQr, '', '', amount: amount, orderID: orderID, autoCloseFinishScreen: autoDismissDlgTimer);
        }
      }else {
        showWidgetQrWallet(nativeResponseModel.data);
      }
    }else {
      if (_appController.userInfo?.config?.connectType == 4) {
        NativeBridge.getInstance().nativeCallBackResultPaymentSocket('TRANS_ERROR', orderID, amount ?? "", MposConstant.name_group_qr_VAQR, "");
      }
      AppUtils.showDialogError(context, AppStrings.getString(AppStrings.msg_err_fetch_config_qr)!.tr);
    }
  }

  void onPressQrWallet() async {
    if (!validateAmount(Get.find<PaymentInitController>().context, valueInput.value, _minAmount)) {
      return;
    }

    String configQr = await _appController.localStorage.getConfigQrP12();
    if(configQr.isEmpty) {
      fetchConfigQrP12(context, false);
    }else {
      showWidgetQrWallet(configQr);
    }
  }

  void showWidgetQrWallet(String configQr) {
    List<QrChildren> listQr = [];
    final Map responseMap = json.decode(configQr);
    QrDataConfig _qrDataConfig = QrDataConfig.fromJson(responseMap as Map<String, dynamic>);
    _qrDataConfig.data?.forEach((element) {
      if (element.groupName == MposConstant.name_group_qr_QR_VI) {
        listQr.addAll(element.qrChildren!);
      }
    });

    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
      ),
      builder: (context) {
        return BaseBottomSheet(
            isCloseHeader: true,
            hideCloseButton: true,
            child: QrWalletWidget(
              onPressItem: (qrType, qrName, logo, amountMin) => gotoP12QrScreen(Get.find<PaymentInitController>().context, qrType, qrName, logo, amountMin),
              listQr: listQr,
              tileBoxQr: AppStrings.getString(AppStrings.tv_name_group_qr_wallet) ?? '',
            )
        );
      },
      isScrollControlled: true,
      isDismissible: false,
    );
  }

  onSelectMoto() async {
      LoggerMp().writeLog('onSelectMoto');
    // await Get.find<MainController>().initConfigCashierModule().timeout(
    //     Duration(milliseconds: 1000));
    var mapArgument = {};
    mapArgument["rnAmount"] = valueInput.value;
    mapArgument["rnDescription"] = textDes.value;
    // mapArgument["rnPhone"] = currentPhone.value;
    // mapArgument["rnEmail"] = currentEmail.value;
    // mapArgument["rnIdentity"] = currentIdentity.value;
    // mapArgument["rnServiceCode"] = code;

    _appController.showLoading();
    String response = await MposRequest.requestGetCommonConfig(
        _appController.loginAccount,
        _appController.serialNumber ?? '');
    _appController.hideLoading();
    if (response.isNotEmpty) {
      // print('response: $response');
      CommonConfig commonConfig = CommonConfig.fromJson(jsonDecode(response));
      NativeResponseModel nativeResponseModelTID = await NativeBridge.getInstance().nativeGetTID();
      Map map = {
        'mId':_appController.userInfo?.mid,
        'tId':nativeResponseModelTID.data,
        'username':_appController.loginAccount,
        'motoBinRanges':commonConfig.motoBinRanges,
        'motoPolicyUrl':commonConfig.motoPolicyUrl,
      };
      AppConfiguration().setMacqConfigByMap(map);
      Get.toNamed(AppRoute.moto_screen, arguments: mapArgument);
    } else {
      AppUtils.showDialogError(context, 'Không thành công');
    }
  }

  void onPressCreateLink() async {
    await SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    Get.toNamed(AppRoute.qr_code_p12_screen, arguments: {
      'amount': valueInput.value,
      'typePay': TypePay.CREATE_LINK
    });
  }

  void onPressPayBNPL({String? codeBNPLItem}) async {
    //todo check invalid amount
    print('onPressPayBNPL');
    // await Get.find<MainController>().initConfigCashierModule().timeout(
    //     Duration(milliseconds: 1000));
    MPMasterAction.instance.init(
      baseUrl: BaseService().getApiBaseUrl(),
      // baseUrl: BaseService().getBaseUrlMPOS(),
      showLog: AppConfiguration().showLog,
      muid: _appController.loginAccount,
      serial: _appController.serialNumber,
      merchantId: int.parse(_appController.userInfo?.merchantId ?? '0'),
      forceInit: true,
      mpDevice: AppConfiguration().isKozenP12 ? MPConstantDevice.n31 : null,
    );
    String mpDataLoginModel = await _appController.localStorage.getGatewayMcConfig();
    MPMasterAction.instance.startMposModule(
      getXBackupKey: Get.key,
      mposModule: moduleQr.MposModuleQR(),
      context: context,
      arguments: md.MPInputArgs()
        ..data = md.MPDataLoginModel.fromJson(json.decode(mpDataLoginModel))
        ..dataExtend = {
          "qrDetailParam": {
            "qrTypeCode": "MUA_TRUOC_TRA_SAU",
            "totalAmount": double.parse(valueInput.value),
            "feeAmount": 0.0, // calculate fee inside module
            "phone": "",
            "email": "",
            "description": "",
            "currencyCode": 'VND',
            "enablePrint": false,
            "codeBNPLItem": codeBNPLItem ?? "",
          }
        },
    );
  }

  void onPressCheckInstallmentCard() {
    if (validateAmount(Get.find<PaymentInitController>().context, valueInput.value, _minAmount)) {
      LoggerMp().writeLog('onPressCheckInstallmentCard');
      String params = 'merchantId=${_appController.userInfo?.merchantId ?? ''}&isMobile=1&lang=${LocalizationCustom.langCode == 'en' ? 'eng' : 'vie'}';
      params += '&amount=${valueInput.value}';

      Get.toNamed(AppRoute.webview_info_screen,
          arguments: WebViewInfoArguments(MposConstant.URL_CHECK_CARD_INSTALLMENT + base64.encode(utf8.encode(params)), 'Kiểm tra thẻ trả góp', showControl: false));
    }
  }

  void onPressManualUse() {
    Get.toNamed(AppRoute.webview_info_screen,
        arguments:
        WebViewInfoArguments(MposConstant.URL_MPOS_GUIDE, AppStrings.getString(AppStrings.menuGuide)));
  }

  void _onPressFeatureInstallment() {
    List<TypePayment> lstTypeInstallment = [];
    bool mcAllowCard = (_appController.userInfo?.isPayCard ?? 0) == 1;
    bool isPayLink = (_appController.userInfo?.isPayLink ?? 0) == 1;

    MPItemMenuHome? mpItemMenuHomeBNPLCheck = (_appController.userInfo?.buttonApps ?? [])
        .firstWhereOrNull((element) => element.code == "MUA_TRUOC_TRA_SAU" || element.code == "BNPL_PAYMENT");

    if (mcAllowCard) {
      lstTypeInstallment.add(TypePayment(
          title: AppStrings.getString('installmentSwipeCard'),
          bgColor: AppColors.white,
          logo: AppImages.ic_installment_card,
          typePay: TypePay.INSTALLMENT_CARD,
          textStyle: TextStyle(
            fontSize: AppDimens.textSizeLarge,
            fontWeight: FontWeight.w500,
            fontFamily: kFontFamilyBeVietnamPro,
            color: AppColors.black,
          )));
    }
    if (isPayLink) {
      lstTypeInstallment.add(TypePayment(
          title: AppStrings.getString('installmentCreateLink'),
          bgColor: AppColors.white,
          logo: AppImages.ic_installment_link,
          typePay: TypePay.INSTALLMENT_LINK,
          textStyle: TextStyle(
            fontSize: AppDimens.textSizeLarge,
            fontWeight: FontWeight.w500,
            fontFamily: kFontFamilyBeVietnamPro,
            color: AppColors.black,
          )));
    }

    if (mpItemMenuHomeBNPLCheck != null) {
      MPBankQR bankQRBNPL = (_appController.userInfo?.listBankQR ?? []).firstWhere((element) => element.type == 'BNPL');
      for (MPQrChild item in (bankQRBNPL.qrChildren ?? [])) {
        print('logo: ${item.faviconChild}');
        lstTypeInstallment.add(TypePayment(
            amountMin: item.amountMin,
            logo: item.faviconChild,
            codeBNPLItem: item.qrType,
            title: item.longNameChild,
            bgColor: AppColors.white,
            typePay: TypePay.INSTALLMENT_BNPL,
            sub_name: AppStrings.getString(AppStrings.labelBnpl),
            sub_nameEn: AppStrings.getString(AppStrings.labelBnpl),
            textStyle: TextStyle(
              fontSize: AppDimens.textSizeLarge,
              fontWeight: FontWeight.w500,
              fontFamily: kFontFamilyBeVietnamPro,
              color: AppColors.black,
            )));
      }
    }

    lstTypeInstallment.add(TypePayment(
      logo: AppImages.ic_checkcard_installment,
        title: AppStrings.getString(AppStrings.check_card_installment_short),
        bgColor: AppColors.white,
        typePay: TypePay.CHECK_INSTALLMENT_CARD,
        position: 5,
        textStyle: TextStyle(
          fontSize: AppDimens.textSizeLarge,
          fontWeight: FontWeight.w500,
          fontFamily: kFontFamilyBeVietnamPro,
          color: AppColors.black,
        )));

    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(6)),
      ),
      builder: (context) => Container(
        child: SelectTypePay(
          onSelectInstallment: (typePay, codeBNPLItem, amountMin) {
            handleSelectTypePay(typePay, codeBNPLItem: codeBNPLItem, amountMin: amountMin);
          },
          listTypePayment: lstTypeInstallment,
          isTypeInstallment: true,
        ),
      ),
      isScrollControlled: true,
      isDismissible: false,
    );
  }

  // void _onPressFeatureInstallmentBNPL(String? code, String? codeBNPLItem, String? amountMin) async {
  //     Logger().write('onPressFeatureInstallmentBNPL: code=${code}, codeBNPL=${codeBNPLItem}, amountMin=${amountMin}');
  //   if (validateAmount(Get.find<PaymentInitController>().context, valueInput.value, double.parse(amountMin ?? '1000'))) {
  //     await Get.find<MainController>().initConfigCashierModule().timeout(
  //         Duration(milliseconds: 1000));
  //
  //     MPMasterAction.instance.init(
  //       baseUrl: BaseService().getApiBaseUrl(),
  //       showLog: AppConfiguration().showLog,
  //       muid: _appController.loginAccount,
  //       serial: _appController.serialNumber,
  //       merchantId: int.parse(_appController.userInfo?.merchantId ?? '0'),
  //       rootNavigator: false,
  //       forceInit: true,
  //       mpDevice: AppConfiguration().isKozenP12 ? MPConstantDevice.n31 : null,
  //     );
  //
  //     String mpDataLoginModel = await _appController.localStorage.getGatewayMcConfig();
  //     await doFunctionWalletPublic(
  //         context: context,
  //         mpDataLoginModel: md.MPDataLoginModel.fromJson(json.decode(mpDataLoginModel)),
  //         code: code ?? '',
  //         amount: double.parse(valueInput.value),
  //         currentDescription: textDes.value,
  //         isBNPL: true,
  //         codeBNPLItem: codeBNPLItem
  //     );
  //   }
  // }

  _onPressFeatureCreateLinkInstallment() async {
    LoggerMp().writeLog('new installment flow');
    if (validateAmount(Get.find<PaymentInitController>().context, valueInput.value, _minAmount)) {
      LoggerMp().writeLog('onPressFeatureInstallmentCard');
      // await Get.find<MainController>().initConfigCashierModule().timeout(Duration(milliseconds: 1000));

      MPMasterAction.instance.init(
        baseUrl: BaseService().getApiBaseUrl(),
        // baseUrl: BaseService().getBaseUrlMPOS(),
        showLog: AppConfiguration().showLog,
        muid: _appController.loginAccount,
        serial: _appController.serialNumber,
        merchantId: int.parse(_appController.userInfo?.merchantId ?? '0'),
        rootNavigator: false,
        forceInit: true,
        mpDevice: MPConstantDevice.n31,
      );

      AppConfiguration().setLoginMacqModel(LoginMacqModel());
      AppConfiguration().loginMacqModel.deviceIdentifier = _appController.deviceIdentifier ?? '';
      String mpDataLoginModel = await _appController.localStorage.getGatewayMcConfig();
      doFunctionInstallmentPublic(
          context: context,
          mpDataLoginModel: md.MPDataLoginModel.fromJson(json.decode(mpDataLoginModel)),
          amount: double.parse(valueInput.value),
          isPayLink: true,
          dataInput: null,
          processHandleResultPay: null,
          currentDescription: textDes.value,
          onPressFeatureInstallmentCard: null,
          allowOtherPaymentMethod: false,
          callbackOtherPaymentMethod: null);
    }
  }

}
