import 'dart:async';
import 'dart:convert';
import 'dart:developer' as dev;

import 'package:cashiermodule/model_instance/app_configuration.dart';
import 'package:cashiermodule/model_instance/mqtt_client.dart';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:get/get.dart';
import 'package:just_audio/just_audio.dart';
import 'package:mposxs/app/data/model/mqtt_model.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/route/app_pages.dart';
import 'package:mposxs/app/util/TTSControl.dart';
import 'package:mposxs/app/util/np_tingting_speak_handler.dart';
import 'package:mqtt5_client/mqtt5_client.dart';

import 'package:shared_preferences/shared_preferences.dart';

import '../data/provider/local_storage.dart';

late var emqxConnect;
MqttModel? mqttModel;
const String notificationIdChanel = 'mpos_lite_foreground';
const String notificationTitle = 'Mpos Sound SERVICE';
bool isServiceRunning = false;
Map<String, dynamic> status = {'status': ''};

String lastNotificationContent = '';

@pragma('vm:entry-point')
void onStartServices(ServiceInstance service) async {
  dev.log('onstart service======>');
  // Only available for flutter 3.0.0 and later
  // DartPluginRegistrant.ensureInitialized();

  // For flutter prior to version 3.0.0
  // We have to register the plugin manually
  isServiceRunning = true;

  SharedPreferences preferences = await SharedPreferences.getInstance();
  String? cacheMqttModel = preferences.getString('mqttModel') ?? '';
  dev.log('service-cacheMqttModel=$cacheMqttModel');
  if (cacheMqttModel != '') {
    mqttModel = MqttModel.fromJson(jsonDecode(cacheMqttModel));
  }

  /// OPTIONAL when use custom notification
  // final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
  // FlutterLocalNotificationsPlugin();

  if (service is AndroidServiceInstance) {
    service.on('setAsForeground').listen((event) {
      service.setAsForegroundService();
    });

    service.on('setAsBackground').listen((event) {
      service.setAsBackgroundService();
    });
  }

  service.on('stopService').listen((event) {
    dev.log('======> stopping service');
    service.stopSelf();
    isServiceRunning = false;
  });

  // runTestMacq();
  initMqtt(service);
  dev.log('<==== end of start service');
}

updateStatusEmqxForeground(ServiceInstance service, String status) async {
  if (!isServiceRunning) {
    dev.log('timer periodic check service===>$isServiceRunning');
    return;
  }

  if (service is AndroidServiceInstance) {
    if (await service.isForegroundService()) {
      final newContent = "${getNameStatusMqtt(status)}";
      // So sánh với nội dung cũ, chỉ cập nhật nếu có thay đổi
      if (newContent != lastNotificationContent) {
        await service.setForegroundNotificationInfo(
          title: notificationTitle,
          content: newContent,
        );
        lastNotificationContent = newContent;
      }
      print('ufo status connect: ${status}');
    }
  }
}

runTestMacq() async {
  dev.log('---- run test mqtt5 ----');
  // TestMqtt5Server mqtt5server = new TestMqtt5Server();
  // mqtt5server.runTestConnect();
}

initMqtt(ServiceInstance service) async {
  print('init Mqtt: ${mqttModel?.toJson().toString()}');
  if (mqttModel != null) {
    emqxConnect = EmqxClient();
    AppConfiguration().setEmqxEnable(true);
    String sn = await LocalStorage().getSerialNumber();

    initEmqxConnect(service, sn, false);
  }
}

void listenerEmqx(ServiceInstance service, String sn) {
  emqxConnect.changeFeedConnection = (connectionElement) {
    dev.log('mqtt feedConnection=$connectionElement');
    callBackStatusEmqx(service, connectionElement);

    if (connectionElement == 'EMQX_RECONECT') {
      Future.delayed(Duration(seconds: 15)).then((value) {
        initEmqxConnect(service, sn, true);
      });
    }
  };
  emqxConnect.changeFeedQueueListener = (element) async {
    dev.log('-1-received msg: ${element.toString()}');
    _handlerMqttMessage(element, service);
  };
}

initEmqxConnect(ServiceInstance service, String sn, bool isRetry) async {
  var topicTemp = '/N31/$sn/data';
  // topicTemp = 'SP052308013006';
  print('topicTemp: $topicTemp');
  try {
    var client = await emqxConnect.connect(
      // server: 'mqtt.nextpay.vn',
        server: mqttModel!.host,
        clientIdentifier: sn, // test: SP0524B0200008. N31DEV01
        port: mqttModel!.port, // test: 8883
        username: mqttModel!.emqxUsername, // test: rltb01, n31_gDQNyBuji8A
        password: mqttModel!.emqxPass, // test: rltb01123456, hhDqz0TfoTfd8d7
        sslEnable: true,
        useQueueMsg: true,
        maxMsgInQueue: 5,
        mqttQosModeUse: 0);
    emqxConnect.setTopic(topicTemp); // test: mpos-soundbox
    dev.log('client.connectionStatus!.state=${client.connectionStatus!.state}');

    if (client.connectionStatus!.state == MqttConnectionState.connected) {
      await NPTingTingSpeakerHandler.soundConnectSuccess();
      emqxConnect.subscribeTopic();
      String statusEmqx = emqxConnect?.getEmqxCurrState();
      callBackStatusEmqx(service, statusEmqx);

      if(!isRetry) {
        listenerEmqx(service, sn);
      }
    } else {
      callBackStatusEmqx(service, EmqxClient.emqxDisconnected);
      Future.delayed(Duration(seconds: 15)).then((value) {
        initEmqxConnect(service, sn, false);
      });
    }
  } catch (e) {
    dev.log('initEmqxConnect err: $e');
  }
}

callBackStatusEmqx(ServiceInstance service, String emqxCurSt) {
  status['status'] = emqxCurSt;
  service.invoke('statusEmqx', status);
  updateStatusEmqxForeground(service, emqxCurSt);
}

closeMqtt() {
  if (emqxConnect != null) {
    emqxConnect.unSubscribeTopic();
    // emqxConnect.disconnect();
  }
}

_handlerMqttMessage(var map, ServiceInstance service) async {
  dev.log('------');
  // Map map = jsonDecode(msg);
  bool hasReadNotify = false;
  bool hasUpdateQR = false;
  // broadcast_type: 1 -> chỉ đọc noti
  // broadcast_type: 4 -> chỉ hiển thị QR
  // broadcast_type: 3 -> vừa hiển thị QR, vừa đọc noti
  String broadcastType = map['broadcast_type'] ?? '';
  switch (broadcastType) {
    case '1':
      hasReadNotify = true;
      break;
    case '3':
      hasReadNotify = true;
      hasUpdateQR = true;
      break;
    case '4':
      hasUpdateQR = true;
      break;
  }
  dev.log('broadcastType=$broadcastType');
  if (hasReadNotify && Get.currentRoute != AppRoute.qr_code_p12_screen) {
    String amount = map['amount'] ?? map['money'] ?? '';
    dev.log('amount=$amount');
    if (amount != '') {
      try {
        await NPTingTingSpeakerHandler.receivedCurrencyAmountSound(
            int.parse(amount),
            hasMerge: false);
        // if(_callbackReceiveMqttMsg!=null) {
        //   _callbackReceiveMqttMsg!(map);
        // }
      } catch (e) {
        dev.log('play sound error: $e');
        // testPlaySound();
        TtsControl().speak(
            AppStrings.getString(AppStrings.msg_noti_payment_success)!
                .replaceFirst('%s', amount));
      }
    }
  }

  print('start callback receive Mqtt msg');
  if (map != null) {
    service.invoke('callbackEmqx', map);
  }
}

testPlaySound() {
  var soundPath = '/data/user/0/com.nextpay.mposxs.test/cache/Received.mp3';
  var soundPath2 =
      '/data/user/0/com.nextpay.mposxs.test/cache/NUM_UNIT_VND.mp3';
  final _player = AudioPlayer();
  // _player.setAudioSource(AudioSource.file(soundPath));
  // _player.play();
  // _player.setAudioSource(AudioSource.file(soundPath2));
  // _player.play();
  final playlist = ConcatenatingAudioSource(
    // Start loading next item just before reaching it
    useLazyPreparation: true,
    // Customise the shuffle algorithm
    shuffleOrder: DefaultShuffleOrder(),
    // Specify the playlist items
    children: [
      AudioSource.file(soundPath),
      AudioSource.file(soundPath2),
      AudioSource.file(soundPath),
      AudioSource.file(soundPath2),
      // AudioSource.uri(Uri.parse('https://example.com/track2.mp3')),
      // AudioSource.uri(Uri.parse('https://example.com/track3.mp3')),
    ],
  );
  _player.setAudioSource(playlist,
      initialIndex: 0, initialPosition: Duration.zero);
  _player.play();
}

String getNameStatusMqtt(String status) {
  String name = "Mất kết nối.";
  if (status == EmqxClient.emqxAlive) {
    name = "Đã kết nối.";
  }

  // switch (status) {
  //   case EmqxClient.emqxAlive:
  //     name = "Đã kết nối.";
  //     break;
  // }
  return name;
}

class MqttController {}
