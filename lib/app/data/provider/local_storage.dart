import 'dart:convert';

import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:mposxs/app/data/model/log_error_model.dart';
import 'package:mposxs/app/util/np_tingting_speak_handler.dart';
import 'package:shared_preferences/shared_preferences.dart';

class LocalStorage {
  static const KEY_LANGUAGE_APP = 'KEY_LANGUAGE_APP';
  static const KEY_LANGUAGE_SOUND_TINGBOX = 'KEY_LANGUAGE_SOUND_TINGBOX';
  static const KEY_PAYMENT_ERROR_UNSIGN = 'KEY_PAYMENT_ERROR_UNSIGN';
  static const KEY_NOTIFY_UPDATE_APP = 'KEY_NOTIFY_UPDATE_APP';

  static const KEY_NEED_SAY_HELLO = 'KEY_NEED_SAY_HELLO';
  static const KEY_NEED_GUIDE_P12 = 'KEY_NEED_GUIDE_P12';

  static const KEY_PAYMENT_HOME = 'KEY_PAYMENT_HOME';

  static const KEY_AUTO_PRINT = 'KEY_AUTO_PRINT';

  static const KEY_HAS_ERROR_CODE_PREPARE = 'KEY_HAS_ERROR_CODE_PREPARE';

  static const KEY_LOGGER = 'KEY_LOGGER';

  static const KEY_SECURE_SYNC = 'KEY_SECURE_SYNC';
  static const CAN_AUTO_LOGIN = 'CAN_AUTO_LOGIN';
  static const PASSWORD = 'PASSWORD';
  static const USER_NAME = 'USER_NAME';
  static const READER_TYPE = 'READER_TYPE';
  static const SERIAL_NUMBER = 'SERIAL_NUMBER';

  static const STATIC_QR_P12 = 'STATIC_QR_P12';
  static const STATIC_APPLE_GG_QR_P12 = 'STATIC_APPLE_GG_QR_P12';
  static const CONFIG_QR_P12 = 'CONFIG_QR_P12';
  static const USER_CACHE_QR = 'USER_CACHE_QR';

  static const GATEWAY_MC_CONFIG = 'GATEWAY_MC_CONFIG';

  static const listOrderPaid = 'listOrderPaid';
  static const lastTimeCacheQrPaid = 'lastTimeCacheQrPaid';

  static const displayNavbar = 'displayNavbar';
  static const disableStatusBar = 'disableStatusBar';
  static const isShowWaitingTcpScreen = 'isShowWaitingTcpScreen';
  static const pwWaitingTcpScreen = 'pwWaitingTcpScreen';
  static const isShowDefaultVietQr = 'isShowDefaultVietQr';
  static const isHoldScreenQr = 'isHoldScreenQr';

  static const storage = FlutterSecureStorage();

  Future clear(String key) async {
    await storage.delete(key: key);
  }

  Future clearAll() async {
    await storage.deleteAll();
  }

  Future<void> convertDataToNewSecureStorage() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    convertField(prefs, KEY_LANGUAGE_APP);
    convertField(prefs, KEY_NEED_SAY_HELLO);
    convertField(prefs, KEY_NEED_GUIDE_P12);
    convertField(prefs, KEY_PAYMENT_HOME);
    convertField(prefs, KEY_AUTO_PRINT);
    convertField(prefs, KEY_HAS_ERROR_CODE_PREPARE);
  }

  void convertField(SharedPreferences prefs, String key) {
    String? result = prefs.getString(key);
    if(result!=null) {
      saveData(key, result);
    }
  }

  setStaticAppleGoogleQrP12(String? value){
    saveData(STATIC_APPLE_GG_QR_P12, value);
  }

  Future<String> getStaticAppleGoogleQrP12() async {
    return await getData(STATIC_APPLE_GG_QR_P12, '');
  }

  setStaticQrP12(String? value){
    saveData(STATIC_QR_P12, value);
  }

  Future<String> getStaticQrP12() async {
    return await getData(STATIC_QR_P12, '');
  }

  setConfigQrP12(String? value){
    saveData(CONFIG_QR_P12, value);
  }

  Future<String> getConfigQrP12() async {
    return await getData(CONFIG_QR_P12, '');
  }

  setLanguageApp(String? value){
    saveData(KEY_LANGUAGE_APP, value);
  }

  Future<String> getLanguageApp() async {
    return await getData(KEY_LANGUAGE_APP, '');
  }

  setLanguageSoundTingbox(String? value){
    saveData(KEY_LANGUAGE_SOUND_TINGBOX, value);
  }

  Future<String> getLanguageSoundTingbox() async {
    return await getData(KEY_LANGUAGE_SOUND_TINGBOX, NPTingTingSpeakerHandler.soundNorth);
  }

  Future setPaymentErrorUnsign(Map? value) async {
    if (value != null) {
      saveData(KEY_PAYMENT_ERROR_UNSIGN, json.encode(value));
    } else {
      clear(KEY_PAYMENT_ERROR_UNSIGN);
    }
  }

  Future<Map?> getPaymentErrorUnsign() async {
    String result = await getData(KEY_PAYMENT_ERROR_UNSIGN, '');
    if (result == null || result == '') {
      return null;
    }
    Map? value = json.decode(result);
    return value;
  }

  void setIsSaveLogin(String? canAutoLogin) async {
    await storage.write(key: CAN_AUTO_LOGIN, value: canAutoLogin);
  }

  Future<String> getIsSaveLogin() async {
    String? result = await storage.read(key: CAN_AUTO_LOGIN);
    return result != null ? result : '';
  }

  Future setPassword(String password) async {
    await storage.write(key: PASSWORD, value: password);
  }

  Future<String> getPassword() async {
    String? result = await storage.read(key: PASSWORD);
    return result != null ? result : '';
  }

  Future setUserName(String userName) async {
    await storage.write(key: USER_NAME, value: userName);
  }

  Future<String> getUserName() async {
    String? result = await storage.read(key: USER_NAME);
    return result != null ? result : '';
  }

  Future setReaderType(String readerType) async {
    await storage.write(key: READER_TYPE, value: readerType);
  }

  Future<String?> getReaderType() async {
    String? result = await storage.read(key: READER_TYPE);
    return result;
  }

  Future setSerialNumber(String? serialNumber) async {
    await storage.write(key: SERIAL_NUMBER, value: serialNumber);
  }

  Future<String> getSerialNumber() async {
    String? result = await storage.read(key: SERIAL_NUMBER);
    return result != null ? result : '';
  }

  void setGatewayMcConfig(String value) async {
    await storage.write(key: GATEWAY_MC_CONFIG, value: value);
  }

  Future<String> getGatewayMcConfig() async {
    String? result = await storage.read(key: GATEWAY_MC_CONFIG);
    return result??'';
  }

  setUserCacheQR(String? value){
    saveData(USER_CACHE_QR, value);
  }

  Future<String> getUserCacheQR() async {
    return await getData(USER_CACHE_QR, '');
  }

  Future<void> addLogError(LogErrorModel logErrorModel) async {
    if (logErrorModel==null) {
      saveDataToLast(LocalStorage.KEY_LOGGER, logErrorModel.toString());
    }
  }

  Future<void> saveData(String key, dynamic value) async {
    // storage.write(key: key, value: value.toString());
    try {
      await storage.write(key: key, value: value.toString());
    } catch (e) {
      print('Error saving data: $e');
      // Xử lý lỗi - có thể thử lại với cách khác hoặc thông báo cho người dùng
    }
  }

  dynamic getData(String key, [dynamic defaultData]) async {
    if (await storage.containsKey(key: key)) {
      String? value = await storage.read(key: key);
      if (defaultData == null) {
        return value;
      } else {
        try {
          if (defaultData is bool) {
            if (value!.toLowerCase() == 'true') {
              return true;
            } else if (value.toLowerCase() == 'false') {
              return false;
            } else {
              return defaultData;
            }
          }
          else if (defaultData is int) {
            return int.parse(value!);
          } else if (defaultData is double) {
            return double.parse(value!);
          } else {
            return value;
          }
        } catch (e) {
          print(e);
          return defaultData;
        }
      }
    }
    return defaultData;
  }

  Future<void> saveDataToLast(String key, dynamic value) async {
    String? currentData;
    if (await storage.containsKey(key: key)) {
      currentData = await storage.read(key: key);
    }
    storage.write(key: key, value: '${currentData != null ? ('$currentData\n') : ''}${value.toString()}');
  }

  Future<List<String>> getListOrderPaid() async {
    // String dataOrderPaid = await getData(listOrderPaid, '');
    // if (dataOrderPaid.isNotEmpty) {
    //   return dataOrderPaid.split(',');
    // }
    // return [];

    try {
      String dataOrderPaid = '';
      try {
        dataOrderPaid = await getData(listOrderPaid, '');
      } catch (e) {
        print('Error reading from secure storage: $e');
      }

      if (dataOrderPaid.isNotEmpty) {
        return dataOrderPaid.split(',');
      }
      return [];
    } catch (e) {
      print('Error in getListOrderPaid: $e');
      return [];
    }
  }

  Future<void> setListOrderPaid(List<String> data) async {
    // String dataPaid = data.join(',');
    // await storage.write(key: listOrderPaid, value: dataPaid);
    try {
      // Kiểm tra null trước khi xử lý
      if (data == null) {
        data = [];
      }
      String dataPaid = data.join(',');
      // Sử dụng try-catch để xử lý lỗi từ FlutterSecureStorage
      try {
        await storage.write(key: listOrderPaid, value: dataPaid);
      } catch (e) {
        print('Error in secure storage, using SharedPreferences as fallback: $e');
      }
    } catch (e) {
      print('Error in setListOrderPaid: $e');
    }

  }

  Future setLastTimeCacheQrPaid(String? lastTimeCache) async {
    try {
      await storage.write(key: lastTimeCacheQrPaid, value: lastTimeCache);
    } catch (e) {
      print('Error saving data: $e');
    }
  }

  Future<String> getLastTimeCacheQrPaid() async {
    String? result = await storage.read(key: lastTimeCacheQrPaid);
    return result != null ? result : '';
  }
}

