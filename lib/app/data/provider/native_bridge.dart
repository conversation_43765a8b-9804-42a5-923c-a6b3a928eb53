import 'dart:convert';
import 'dart:io';

import 'package:flutter/services.dart';
import 'package:mposxs/app/data/model/log_error_model.dart';
import 'package:mposxs/app/data/model/reader_mpos.dart';
import 'package:mposxs/app/data/provider/logger.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/app_validation.dart';
import 'package:mposxs/build_constants.dart';

import 'local_storage.dart';

class NativeBridge {
  static final _instance = NativeBridge._internal();

  NativeBridge._internal();

  static NativeBridge getInstance() {
    return _instance;
  }

  static final platformFramework = MethodChannel('com.mpos/framework');
  static final String tag = '--NativeBridge ' + Platform.operatingSystem + '--: ';

  static final String _actionLogin = 'Action_LOGIN';
  static final String _actionCallApiMacq = 'Action_CALL_API_MACQ';

  void instanceListenerNative(Function(MethodCall call) handler) {
    print("Instance listener callback native: ${handler.toString()}");
    platformFramework.setMethodCallHandler((methodCall) async {
      handler(methodCall);
    });
  }

  Future<NativeResponseModel<String?>> nativeInitBluetoothAndDevice(int readerType, String readerIdTest) async {
    try {
      // final String result = _invokeMethod(
      //     'initBluetoothAndDevice', <String, dynamic>{'readerType': readerType, 'readerTest': readerIdTest});

      final String result = await _invokeMethod('initBluetoothAndDevice', <String, dynamic>{'readerType': readerType, 'readerTest': readerIdTest}, setTimeOut: true) ?? '';
      AppUtils.log('initBluetoothAndDevice:' + result);
      if (!isNullEmpty(result)) {
        printMessage(result);
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'nativeInitBluetoothAndDevice');
        return NativeResponseModel(false, result, null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeInitBluetoothAndDevice');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<String?>> nativeLoginMacqByDevice(String userName, String? serialNumber, int? readerType, String? deviceIdentifier, String? merchantId) async {
    try {
      Map params = {
        'userName': userName,
        'serialNumber': serialNumber,
        'readerType': readerType,
        'deviceIdentifier': deviceIdentifier,
        'merchantId': merchantId,
      };
      // final String? result = _invokeMethod('callLoginMacqByDevice', params);
      final String? result = await _invokeMethod('callLoginMacqByDevice', params) ?? '';
      printMessage(result);
      return NativeResponseModel(true, result, null);
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeLoginMacqByDevice');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<String?>> nativeFetchStaticQr(String muid) async {
    try {
      Map params = {
          'muid': muid,
      };
      final String? result = await _invokeMethod('fetchStaticQr', params) ?? '';
      printMessage(result);
      if (!isNullEmpty(result)) {
        return NativeResponseModel(true, result, null);
      }else {
        return NativeResponseModel(false, null, null);
      }
      // return NativeResponseModel(true, result, null);
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'fetchStaticQr');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<String?>> callNativeMotoPayRequest(String stringJson) async {
    try {
      final String? result = await _invokeMethod('Push_Pay_Moto_Info', stringJson) ?? '';
      printMessage(result);
      if (!isNullEmpty(result)) {
        printMessage(result);
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'Push_Pay_Moto_Info');
        return NativeResponseModel(false, result, null);
      }

      // return NativeResponseModel(true, result, null);
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'Push_Pay_Moto_Info');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<String?>> nativeFetchConfigQr(String muid) async {
    try {
      Map params = {
        'muid': muid,
      };
      final String? result = await _invokeMethod('fetchQrConfig', params) ?? '';
      printMessage(result);
      if (!isNullEmpty(result)) {
        return NativeResponseModel(true, result, null);
      }else {
        return NativeResponseModel(false, null, null);
      }
      // return NativeResponseModel(true, result, null);
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'fetchQrConfig');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<String?>> nativeVerifySerialNumber(String? serialNumber, String password) async {
    try {
      Map params = {
        'password': password,
        'serialNumber': serialNumber,
      };
      final String? result = await _invokeMethod('verifySerialNumber', params, setTimeOut: true);
      printMessage(result);
      return NativeResponseModel(true, result, null);
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'verifySerialNumber');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<void> nativeInitAuthenMA(String? user, String? serialNumber, String password) async {
    try {
      Map params = {
        'user': user,
        'password': password,
        'serialNumber': serialNumber,
      };
      await _invokeMethod('initAuthenMA', params);
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeHideKeyBoard');
    }
  }


  Future<NativeResponseModel<String?>> nativeLoginLevel1(String accountName, String accountPass, String? rawData,
      String? serialNumber, int? readerType, int isMacqFlow) async {
    if (Platform.isIOS) {
      try {
        Map params = Map();
        params['account_name'] = accountName;
        params['account_pass'] = accountPass;
        params['response_login'] = rawData;
        params['reader_type'] = readerType;
        params['serialNumber'] = serialNumber; //anhvt: chi dung cho test simulator
        final String result = await _invokeMethod('callLoginLevel1', params) ?? '';
        AppUtils.log('callLoginLevel1 result:' + result);
        if (result != null) {
          return NativeResponseModel(true, result, null);
        } else {
          printMessage('null', isError: true, positionLog: 'nativeLoginLevel1');
          return NativeResponseModel(false, result, null);
        }
      } on PlatformException catch (e) {
        printMessage(e.message, isError: true, positionLog: 'nativeLoginLevel1');
        return NativeResponseModel(false, null, e);
      }
    } else {
      Map params = {
        'userName': accountName,
        'password': accountPass,
        'serialNumber': serialNumber,
        'isMacqFlow': isMacqFlow,
        'readerType': readerType,
        'dataFromLoginSdk': rawData,
      };
      try {
        final String? result = await _invokeMethod('callLoginLevel1', params) ?? '';
        if (!isNullEmpty(result)) {
          await _invokeMethod('initMposSdk', <String, dynamic>{
            'userName': accountName,
            'password': accountPass,
            'appType': BuildConstants.appTYPE,
          });
          printMessage(result);
          return NativeResponseModel(true, result, null);
        }else {
          return NativeResponseModel(false, null, null);
        }
      } on PlatformException catch (e) {
        printMessage(e.message, isError: true, positionLog: 'nativeLoginLevel1');
        return NativeResponseModel(false, null, e);
      }
    }
  }

  Future<NativeResponseModel<String?>> nativeLoginLevel2(String accountName, String accountPass) async {
    Map params = {
      'userName': accountName,
      'password': accountPass,
    };
    try {
      final String? result = await _invokeMethod('callLoginLevel2', params) ?? '';
      printMessage(result);
      return NativeResponseModel(true, result, null);
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeLoginLevel2');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<String?>> nativeCheckLoginLevel2(String accountName, String accountPass) async {
    Map params = {
      'userName': accountName,
      'password': accountPass,
    };
    try {
      final bool? result = await _invokeMethod('callCheckLoginLevel2', params) ?? false;
      if (result == true) {
        printMessage(result.toString());
        return NativeResponseModel(true, result.toString(), null);
      } else {
        printMessage(result.toString(), isError: true, positionLog: 'nativeLoginLevel2');
        return NativeResponseModel(false, null, null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeLoginLevel2');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<String?>> nativeCallChangePass(String oldPass, String newPass) async {
    Map params = {
      'oldPass': oldPass,
      'newPass': newPass,
    };
    try {
      final String? result = await _invokeMethod('callChangePass', params) ?? '';
      if (!isNullEmpty(result)) {
        printMessage(result);
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'nativeCallChangePass');
        return NativeResponseModel(false, result, null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeCallChangePass');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<String?>> nativePaymentScanCard(Map nativeParams) async {
    try {
      final String? result = await _invokeMethod('callPaymentScanCard', nativeParams) ?? '';
      if (!isNullEmpty(result)) {
        printMessage(result);
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'nativePaymentScanCard');
        return NativeResponseModel(false, result, null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativePaymentScanCard');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<String?>> nativePaymentScanCardDeposit(String nativeParams) async {
    try {
      final String? result = await _invokeMethod('Action_Pay_Deposit', nativeParams) ?? '';
      if (!isNullEmpty(result)) {
        printMessage(result);
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'nativePaymentScanCardDeposit');
        return NativeResponseModel(false, result, null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativePaymentScanCard');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<String?>> nativeContinueTransactionUnsign(Map nativeParams) async {
    try {
      final String? result = await _invokeMethod('continueTransactionUnsign', nativeParams) ?? '';
      if (!isNullEmpty(result)) {
        printMessage(result);
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'nativeContinueTransactionUnsign');
        return NativeResponseModel(false, result, null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeContinueTransactionUnsign');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<String?>> nativeGetListTransactionComplete() async {
    try {
      final String? result = await _invokeMethod('getListTransactionComplete', '') ?? '';
      if (!isNullEmpty(result)) {
        printMessage(result);
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'nativeGetListTransactionComplete');
        return NativeResponseModel(false, result, null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeGetListTransactionComplete');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<bool?>> nativeSettleAllTransactionComplete() async {
    try {
      final bool? result = await _invokeMethod('settleAllTransactionComplete', '') ?? false;
      if (result == true) {
        printMessage(result.toString());
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'settleAllTransactionComplete');
        return NativeResponseModel(false, result, null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeSettleAllTransactionComplete');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<String?>> nativeGetTransactionDetail(Map params) async {
    try {
      final String? result = await _invokeMethod('getTransactionDetail', params) ?? '';
      if (!isNullEmpty(result)) {
        printMessage(result);
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'getTransactionDetail');
        return NativeResponseModel(false, result, null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeGetTransactionDetail');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<String?>> nativeGetTransactionStatus(Map params) async {
    try {
      final String? result = await _invokeMethod('getTransactionStatus', params) ?? '';
      if (!isNullEmpty(result)) {
        printMessage(result);
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'nativeGetTransactionStatus');
        return NativeResponseModel(false, result, null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeGetTransactionStatus');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<bool?>> nativeSendReceiptWithTransaction(Map params) async {
    try {
      final bool? result = await _invokeMethod('sendReceiptWithTransaction', params) ?? false;
      if (result == true) {
        printMessage(result.toString());
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'nativeSendReceiptWithTransaction');
        return NativeResponseModel(false, result, null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeSendReceiptWithTransaction');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<bool?>> nativeVoidTransaction(Map params) async {
    try {
      final bool? result = await _invokeMethod('voidTransaction', params) ?? false;
      if (result == true) {
        printMessage(result.toString());
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'nativeVoidTransaction');
        return NativeResponseModel(false, result, null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeVoidTransaction');
      return NativeResponseModel(false, null, e);
    }
  }
  
  void nativeLogout(){
    try {
      _invokeMethod('logout', '', setTimeOut: true);
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true);
    }
  }

  void nativeHideKeyBoard() {
    if (Platform.isAndroid) {
      try {
        _invokeMethod('hideKeyBoard', '', setTimeOut: true);
      } on PlatformException catch (e) {
        printMessage(e.message, isError: true, positionLog: 'nativeHideKeyBoard');
      }
    }
  }

  void nativeSetLanguage(String? languageCode) {
    Map params = {'languageCode': languageCode};
    try {
      _invokeMethod('setLanguage', params, setTimeOut: true);
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeSetLanguage');
    }
  }

  void nativeSetPrintMoreReceipt(String? value) {
    try {
      _invokeMethod('SET_PRINT_MORE_RECEIPT', value);
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeSetLanguage');
    }
  }

  Future<NativeResponseModel<String?>> nativeEncryptData(String data, String secretKey) async {
    Map params = {'data': data, 'secretKey': secretKey};
    try {
      final String? result = await _invokeMethod('encryptData', params) ?? '';
      if (!isNullEmpty(result)) {
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'nativeEncryptData');
        return NativeResponseModel(false, result, null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeEncryptData');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<String?>> nativeDecryptData(String data, String secretKey) async {
    Map params = {'data': data, 'secretKey': secretKey};
    try {
      final String? result = await _invokeMethod('decryptData', params) ?? '';
      if (!isNullEmpty(result)) {
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'nativeDecryptData');
        return NativeResponseModel(false, result, null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeDecryptData');
      return NativeResponseModel(false, null, e);
    }
  }

  /*Future<NativeResponseModel<String>> nativeCheckIsP20L() async {
    try {
      final String result = _invokeMethod('checkIsP20L');
      if (!isNullEmpty(result)) {
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'nativeCheckIsP20L');
        return NativeResponseModel(false, '', null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeCheckIsP20L');
      return NativeResponseModel(false, null, e);
    }
  }*/

  Future<NativeResponseModel<ReaderMpos?>> processGetSerialnumber() async {
    try {
      final String? result = await _invokeMethod('getSerialNumber', '', setTimeOut: true) ?? '';
      if (!isNullEmpty(result)) {
        return NativeResponseModel(true, ReaderMpos.fromJson(json.decode(result!)), null);
      } else {
        printMessage('null', isError: true, positionLog: 'nativeGetSerialNumber');
        return NativeResponseModel(false, null, null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeGetSerialNumber');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<String?>> putSuccessQrNotification(String messageData) async {
    try {
      final String? result = await _invokeMethod('putSuccessQrNotification', messageData, setTimeOut: true) ?? '';
      printMessage(result);
      return NativeResponseModel(true, result, null);
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'putSuccessQrNotification');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<bool?>> nativeInitPrinter() async {
    try {
      final bool? result = await _invokeMethod('initPrinter', '', setTimeOut: true) ?? false;
      if (result == true) {
        printMessage(result.toString());
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'nativeInitPrinter');
        return NativeResponseModel(false, result, null);
      }

      // if (!isNullEmpty(result)) {
      //   return NativeResponseModel(true, result, null);
      // } else {
      //   printMessage('null', isError: true, positionLog: 'nativeInitPrinter');
      //   return NativeResponseModel(false, result, null);
      // }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeInitPrinter');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<String?>> nativeGetTransactionReceipt(String transId, String transRqId) async {
    Map params = {
      'transId': transId,
      'transRqId': transRqId,
    };
    try {
      final String? result = await _invokeMethod('getTransactionReceipt', params) ?? '';
      if (!isNullEmpty(result)) {
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'nativeGetTransactionReceipt');
        return NativeResponseModel(false, result, null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeGetTransactionReceipt');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<String?>> nativeGetSettlementReceipt() async {
    try {
      final String? result = await _invokeMethod('getSettlementReceipt', '') ?? '';
      if (!isNullEmpty(result)) {
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'nativeGetSettlementReceipt');
        return NativeResponseModel(false, result, null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeGetSettlementReceipt');
      return NativeResponseModel(false, null, e);
    }
  }

  /*Future<NativeResponseModel<bool>> nativePrintData(String data) async {
    Map params = {
      'data': data,
    };
    try {
      final bool result = _invokeMethod('printData', params);
      if (!isNullEmpty(result)) {
//        printMessage(result.toString());
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'nativePrintData');
        return NativeResponseModel(false, result, null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativePrintData');
      return NativeResponseModel(false, null, e);
    }
  }*/

  Future<NativeResponseModel<String?>> nativePrintReceiptOffline(String? wfID) async{
    try {
      String result = await await _invokeMethod('Action_getDetail_And_Print_Receipt', wfID) ?? "";
      if (!isNullEmpty(result)) {
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'Action_getDetail_And_Print_Receipt');
        return NativeResponseModel(false, result, null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'Action_getDetail_And_Print_Receipt');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<bool?>> nativePrintBase64(String? data) async {
    Map params = {
      'data': data,
    };
    printMessage("nativePrintBase64-> $data <--end data print.");
    try {
      final bool? result = await _invokeMethod('printBase64', params) ?? false;
      if (result == true) {
        printMessage(result.toString());
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'nativePrintBase64');
        return NativeResponseModel(false, result, null);
      }
      // if (!isNullEmpty(result)) {
      //   return NativeResponseModel(true, result, null);
      // } else {
      //   printMessage('null', isError: true, positionLog: 'nativePrintBase64');
      //   return NativeResponseModel(false, result, null);
      // }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativePrintBase64');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<bool?>> callNativeGetAutoGoScreenCashier() async {
    try {
      final bool? result = await _invokeMethod('Action_Check_Is_AutoGoScreenCashier', '', setTimeOut: true) ?? false;
      if (result == true) {
        printMessage(result.toString());
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'callNativeGetAutoGoScreenCashier');
        return NativeResponseModel(false, result, null);
      }

      // if (!isNullEmpty(result) && (result == true)) {
      //   return NativeResponseModel(true, result, null);
      // } else {
      //   printMessage('null', isError: true, positionLog: 'callNativeGetAutoGoScreenCashier');
      //   return NativeResponseModel(false, result, null);
      // }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'callNativeGetAutoGoScreenCashier');
      return NativeResponseModel(false, null, e);
    }
  }

//   Future<NativeResponseModel<bool>> nativePrintText(String data) async {
//     Map params = {
//       'data': data,
//     };
//     printMessage("print-->"+data);
//
//     try {
//       final bool result = _invokeMethod('printText', params);
//       if (!isNullEmpty(result)) {
// //        printMessage(result.toString());
//         return NativeResponseModel(true, result, null);
//       } else {
//         printMessage('null', isError: true, positionLog: 'nativePrintText');
//         return NativeResponseModel(false, result, null);
//       }
//     } on PlatformException catch (e) {
//       printMessage(e.message, isError: true, positionLog: 'nativePrintText');
//       return NativeResponseModel(false, null, e);
//     }
//   }

//   Future<NativeResponseModel<bool>> nativePrintPush(int numberPush) async {
//     Map params = {
//       'numberPush': numberPush,
//     };
//     try {
//       final bool result = _invokeMethod('printPush', params);
//       if (!isNullEmpty(result)) {
// //        printMessage(result.toString());
//         return NativeResponseModel(true, result, null);
//       } else {
//         printMessage('null', isError: true, positionLog: 'nativePrintPush');
//         return NativeResponseModel(false, result, null);
//       }
//     } on PlatformException catch (e) {
//       printMessage(e.message, isError: true, positionLog: 'nativePrintPush');
//       return NativeResponseModel(false, null, e);
//     }
//   }

//   Future<NativeResponseModel<bool>> nativeSetTextFormat(int size, int align, bool isBold) async {
//     Map params = {
//       'size': size,
//       'align': align,
//       'isBold': isBold,
//     };
//     try {
//       final bool result = _invokeMethod('setTextFormat', params);
//       if (!isNullEmpty(result)) {
// //        printMessage(result.toString());
//         return NativeResponseModel(true, result, null);
//       } else {
//         printMessage('null', isError: true, positionLog: 'nativeSetTextFormat');
//         return NativeResponseModel(false, result, null);
//       }
//     } on PlatformException catch (e) {
//       printMessage(e.message, isError: true, positionLog: 'nativeSetTextFormat');
//       return NativeResponseModel(false, null, e);
//     }
//   }

  Future<NativeResponseModel<bool?>> nativeSetSoftInputMode(String type) async {
    Map params = {
      'type': type,
    };
    try {
      final bool? result = await _invokeMethod('setSoftInputMode', params, setTimeOut: true) ?? false;
      if (result == true) {
        printMessage(result.toString());
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'nativeSetSoftInputMode');
        return NativeResponseModel(false, result, null);
      }
//       if (!isNullEmpty(result)) {
// //        printMessage(result.toString());
//         return NativeResponseModel(true, result, null);
//       } else {
//         printMessage('null', isError: true, positionLog: 'nativeSetSoftInputMode');
//         return NativeResponseModel(false, result, null);
//       }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeSetSoftInputMode');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<String?>> nativeGetTidAndMid() async {
    try {
      final String? result = await _invokeMethod('getTidAndMid', '', setTimeOut: true) ?? '';
      if (!isNullEmpty(result)) {
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'nativeGetTID');
        return NativeResponseModel(false, result, null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeGetTID');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<String?>> nativeGetTID() async {
    try {
      final String? result = await _invokeMethod('getTID', '', setTimeOut: true) ?? '';
      if (!isNullEmpty(result)) {
//        printMessage(result.toString());
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'nativeGetTID');
        return NativeResponseModel(false, result, null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeGetTID');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<String?>> nativeGetMID() async {
    try {
      final String? result = await _invokeMethod('getMID', '', setTimeOut: true) ?? '';
      if (!isNullEmpty(result)) {
//        printMessage(result.toString());
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'nativeGetMID');
        return NativeResponseModel(false, result, null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeGetMID');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<NativeResponseModel<void>> nativeShowDialogWebView(String url, String title, String textClose) async {
    Map params = {
      'url': url,
      'title': title,
      'textClose': textClose,
    };
    try {
      final String? result = await _invokeMethod('showDialogWebView', params, setTimeOut: true) ?? '';
      if (!isNullEmpty(result)) {
//        printMessage(result.toString());
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'nativeShowDialogWebView');
        return NativeResponseModel(false, result, null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeShowDialogWebView');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<void> nativeClearDataAuto() async {
    try {
      // final bool? result = _invokeMethod('clearDataAuto');
      // if (result == true) {
      //   printMessage(result.toString());
      //   return NativeResponseModel(true, result, null);
      // } else {
      //   printMessage(result.toString(), isError: true, positionLog: 'nativeClearDataAuto');
      //   return NativeResponseModel(false, null, null);
      // }

      await _invokeMethod('clearDataAuto', '', setTimeOut: true);
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeClearDataAuto');
    }
  }


  Future<NativeResponseModel<String?>> callNativeLogin(String acc, String pass, String serialNumber, int? readerType, String? deviceIdentifier) async {
    Map params = {
      'username':acc,
      'password': pass,
      'serialNumber': serialNumber,
      'readerType': readerType,
      'deviceIdentifier': deviceIdentifier ?? '',
    };
    try {
      final String? result = await _invokeMethod(_actionLogin, params);
      if (!isNullEmpty(result)) {
        printMessage(result.toString());
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'callNativeLogin');
        return NativeResponseModel(false, result, null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'callNativeLogin');
      return NativeResponseModel(false, null, e);
    }

    // NativeResponseModel<String?> model = NativeResponseModel(true, '', null);
    // model.parseFromResponse(result);
    // return model;
  }

  Future<NativeResponseModel<String?>> callNativeRequest(String path, String content) async {
    Map params = {
      'path':path,
      'content': content
    };

    try {
      final String? result = await _invokeMethod(_actionCallApiMacq, params);
      if (!isNullEmpty(result)) {
        printMessage(result.toString());
        return NativeResponseModel(true, result, null);
      } else {
        printMessage('null', isError: true, positionLog: 'callNativeRequest');
        return NativeResponseModel(false, result, null);
      }
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'callNativeRequest');
      return NativeResponseModel(false, null, e);
    }
  }

  Future<void> nativeInitSocket() async {
    try {
      // final bool? result = _invokeMethod('nativeInitSocket');
      // if (!isNullEmpty(result)) {
      //   return NativeResponseModel(true, result, null);
      // } else {
      //   printMessage('null', isError: true, positionLog: 'nativeInitSocket');
      //   return NativeResponseModel(false, result, null);
      // }

      _invokeMethod('nativeInitSocket', '', setTimeOut: true);
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'nativeSetLanguage');
    }
  }

  Future<void> closeAndResetSocket() async {
    try {
      // final bool? result = _invokeMethod('closeAndResetSocket');
      // if (!isNullEmpty(result)) {
      //   return NativeResponseModel(true, result, null);
      // } else {
      //   printMessage('null', isError: true, positionLog: 'closeAndResetSocket');
      //   return NativeResponseModel(false, result, null);
      // }

      await _invokeMethod('closeAndResetSocket', '', setTimeOut: true);
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true, positionLog: 'closeAndResetSocket');
    }
  }

  // Future<bool> nativeIsPermitSocket() async {
  //   try {
  //     final bool result = _invokeMethod('isPermitSocket');
  //     return result;
  //   } on PlatformException catch (e) {
  //     printMessage(e.message, isError: true, positionLog: 'isPermitSocket');
  //     return false;
  //   }
  // }
  //
  // Future<bool> nativeIsPermitVoidSocket() async {
  //   try {
  //     final bool result = _invokeMethod('isPermitVoidSocket');
  //     return result;
  //   } on PlatformException catch (e) {
  //     printMessage(e.message, isError: true, positionLog: 'isPermitVoidSocket');
  //     return false;
  //   }
  // }

  void nativeCallBackResultSetFinalDepositSocket(String responseCode, String? responseMess, String depositId, String finalAmount, {String? lstTrxOrder}) async {
    try {
      Map params = {
        'serviceName': 'SET_FINAL_AMOUNT_DEPOSIT',
        'responseCode': responseCode,
        'depositId': depositId,
        'finalAmount': finalAmount,
        'responseMess': responseMess ?? '',
        'lstTrxOrder': lstTrxOrder ?? '',
      };
      await _invokeMethod('callbackSocketData', jsonEncode(params), setTimeOut: true);
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true);
    }
  }


  void nativeCallBackResultFinishDepositSocket(String responseCode, String? responseMess, String depositId, String finalAmount, {String? data}) async {
    try {
      Map params = {
        'serviceName': 'FINISH_DEPOSIT',
        'responseCode': responseCode,
        'depositId': depositId,
        'finalAmount': finalAmount,
        'responseMess': responseMess ?? '',
        'lstTrsOrder': data ?? '',
      };
      await _invokeMethod('callbackSocketData', jsonEncode(params), setTimeOut: true);
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true);
    }
  }


  void nativeCallBackResultPaymentSocket(String? status, String? orderID, String? totalAmount, String? label, String? orderCode, {int? errCode, String? transId}) async {
    try {
      Map params = {
        'status': status ?? '',
        'orderID': orderID ?? '',
        'totalAmount': totalAmount ?? '',
        'label': label ?? '',
        'orderCode': orderCode ?? '',
        'transId': transId ?? '',
        'errCode': errCode ?? -3
      };
      print('ufo call callback result payment');
      await _invokeMethod('callbackResultPayment', params, setTimeOut: true);
      print('ufo call callback result payment success');
    } on PlatformException catch (e) {
      print('ufo call callback result payment err');
      printMessage(e.message, isError: true);
    }
  }

  void nativeCallBackCancelOrderSocket(String? status, String? orderID, String? totalAmount, String? label, String? orderCode) async {
    try {
      Map params = {
        'status': status ?? '',
        'orderID': orderID ?? '',
        'totalAmount': totalAmount ?? '',
        'label': label ?? '',
        'orderCode': orderCode ?? ''
      };
      await _invokeMethod('callbackCancelPayment', params, setTimeOut: true);
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true);
    }
  }

  void nativeCallBackResultVoidSocket(Map params) async {
    try {
      // Map params = {
      //   'responseCode': responseCode ?? '',
      //   'orderId': orderID ?? '',
      //   'transCode': transCode ?? '',
      //   'confirmVoid': confirmVoid ?? '',
      //   'permitPrintReceipt': permitPrintReceipt ?? ''
      // };
      await _invokeMethod('callbackResultVoid', params, setTimeOut: true);
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true);
    }
  }

  void setDisplayNavbar(bool value) {
    try {
      Map params = {
        'isDisplayNavbar': value,
      };
      _invokeMethod('ActionDisplayNavbar', params, setTimeOut: true);
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true);
    }
  }

  void setDisableStatusBar(bool value) {
    try {
      Map params = {
        'isDisableStatusBar': value,
      };
      _invokeMethod('ActionDisableStatusBar', params, setTimeOut: true);
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true);
    }
  }

  Future<bool> getIsAutoDismissDlg() async {
    try {
      Map params = {
        'key': 'isAutoCloseDialog',
        'type': 3,
      };
      // bool result = _invokeMethod('getCacheDataFormKey', params);
      bool result = await _invokeMethod('getCacheDataFormKey', params, setTimeOut: true) ?? false;
      return result;
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true);
    }
    return false;
  }

  void setIsAutoDismissDlg(bool data) {
    try {
      Map params = {
        'key': 'isAutoCloseDialog',
        'data': data,
      };
      _invokeMethod('setCacheDataFormKey', params, setTimeOut: true);
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true);
    }
  }

  void initActionOpenApp() {
    try {
      _invokeMethod('actionOpenApp', '', setTimeOut: true);
    } on PlatformException catch (e) {
      printMessage(e.message, isError: true);
    }
  }

  Future<dynamic> _invokeMethod(String methodName, dynamic params, {bool? setTimeOut}) async {
    LoggerMp().writeLog('invokeMethod $methodName');
    print('methodName: $methodName, timeOut: $setTimeOut');
    try {
      var result;
      if ((setTimeOut != null) && setTimeOut == true) {
        result = await platformFramework.invokeMethod(methodName, params).timeout(Duration(seconds: 15));
      }else {
        result = await platformFramework.invokeMethod(methodName, params);
      }
      return result;
    } catch (error) {
      // Xử lý lỗi timeout hoặc lỗi khi gọi phương thức
      print('methodName: $methodName, Error: ${error != null ? error.toString() : ''}');
      LoggerMp().writeLogErrFunc('invokeMethod $methodName err: ', error);
      throw error;
    }
  }

  printMessage(String? message, {bool isError = false, String positionLog = ''}) {
    if (isError) {
      LocalStorage()
          .addLogError(LogErrorModel(type: 'SDK', errorCode: '', errorMessage: message, position: positionLog));
      AppUtils.log('ERROR' + tag + message.toString());
    } else {
      AppUtils.log(tag + message.toString());
    }
  }
}

class NativeResponseModel<T> {
  bool isSuccess = false;
  T? data;
  PlatformException? error;

  NativeResponseModel(this.isSuccess, this.data, this.error);

  parseFromResponse(String dataRes) {
    Map<String, dynamic> map = jsonDecode(dataRes);
    print('parseFromResponse: httpCode=${map['httpCode']}');
    if (map['httpCode'] == null) {
      isSuccess = true;
      data = dataRes as T?;
    } else {
      isSuccess = false;
      error = PlatformException(code: map['code'].toString(), message: map['message']);
    }
    print('isSuccess=$isSuccess');
  }
}
