import 'dart:convert';
import 'dart:io';

import 'package:connectivity/connectivity.dart';
// import 'package:dio/adapter.dart';
import 'package:dio/dio.dart';
import 'package:mposxs/app/data/model/base_response.dart';
import 'package:mposxs/app/data/model/log_error_model.dart';
import 'package:mposxs/app/data/provider/local_storage.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/util/api_constant.dart';
import 'package:mposxs/app/util/app_validation.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';

import '../../../build_constants.dart';

class ApiClient {
  static const String GET = 'GET';
  static const String POST = 'POST';
  static const String DELETE = 'DELETE';
  static const String PATCH = 'PATCH';
  static const String PUT = 'PUT';

  static const CONTENT_TYPE = 'Content-Type';
  static const CONTENT_TYPE_JSON = 'application/json; charset=utf-8';

  static const int ERROR_NO_NETWORK         = -3000;
  static const int ERROR_EMPTY_URL          = -3001;
  static const int ERROR_SOCKET_EXCEPTION   = -3002;
  static const int ERROR_TIMEOUT            = -3003;


  static final BaseOptions defaultOptions = BaseOptions(
    baseUrl: BuildConstants.serverAPI,
    connectTimeout: Duration(milliseconds: ApiConstant.connectTimeout),
    receiveTimeout: Duration(milliseconds: ApiConstant.receiveTimeout),
    responseType: ResponseType.plain,
    headers: {'Cache-Control': 'no-cache'},
  );

  late Dio _dio;

  static final Map<BaseOptions, ApiClient> _instanceMap = {};

  factory ApiClient({BaseOptions? options}) {
    if (options == null) options = defaultOptions;
    if (_instanceMap.containsKey(options)) {
      return _instanceMap[options]!;
    }
    final ApiClient apiClient = ApiClient._create(options: options);
    _instanceMap[options] = apiClient;
    return apiClient;
  }

  ApiClient._create({BaseOptions? options}) {
    if (options == null) options = defaultOptions;
    _dio = Dio(options);
    if (BuildConstants.currentEnvironment != Environment.PROD) {
      _dio.interceptors.add(PrettyDioLogger(
          requestHeader: false,
          requestBody: true,
          responseBody: true,
          responseHeader: false,
          error: true,
          compact: true,
          maxWidth: 90));
    }
  }

  static ApiClient get instance => ApiClient();

  Future<BaseResponse> request<T extends BaseResponseData>(
      {String url = ApiConstant.urlGateway,
      String method = POST,
      String? data,
      Function? fromJsonModel,
      Map<String, dynamic>? formData,
      Map<String, dynamic>? queryParameters,
      bool getFullResponse = false}) async {
    var connectivityResult = await Connectivity().checkConnectivity();

    String? positionLog = '';
    if (data != null && data.isNotEmpty) {
      Map dataMap = json.decode(data);
      positionLog = dataMap['serviceName'];
    }
    if (connectivityResult == ConnectivityResult.none) {
      LocalStorage().addLogError(
          LogErrorModel(type: 'API', errorCode: ERROR_NO_NETWORK.toString(), errorMessage: 'Không có kết nối mạng.', position: positionLog));
      return BaseResponse(
        result: false,
        data: null,
        message: 'Không có kết nối mạng.',
        code: ERROR_NO_NETWORK,
      );
    }
    if (isNullEmpty(url)) {
      LocalStorage().addLogError(
          LogErrorModel(type: 'API', errorCode: ERROR_EMPTY_URL.toString(), errorMessage: 'Empty url.', position: positionLog));
      return BaseResponse(
        result: false,
        data: null,
        message: 'Empty url.',
        code: ERROR_EMPTY_URL,
      );
    }
    try {
      // (_dio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate = (HttpClient client) {
      //   client.badCertificateCallback = (X509Certificate cert, String host, int port) => true;
      //   return client;
      // };
      final response = await _dio.request(url,
          data: formData != null ? FormData.fromMap(formData) : data ?? jsonEncode({}),
          options: Options(method: method, contentType: formData != null ? 'multipart/form-data' : null),
          queryParameters: queryParameters);
      if (_isSuccessful(response.statusCode!)) {
        Map responseMap = json.decode(response.data);
        int? errorCode = responseMap.containsKey('error') ? responseMap['error']['code'] : 1000;
        String? errorMsg = responseMap.containsKey('error') ? responseMap['error']['message'] : '';
        String? errorMsgEn = responseMap.containsKey('error') ? responseMap['error']['messageEn'] : '';
        if (errorCode == 1000) {
          Map<String, dynamic> dataOut = {
            'code': errorCode,
            'data': responseMap,
            'rawData': response.data,
            'message': isNullEmpty(errorMsg) ? errorMsgEn : errorMsg,
            'result': true,
          };
          var apiResponse = BaseResponse<T>.fromJson(dataOut, fromJsonModel);
          if (getFullResponse) apiResponse.dioResponse = response;
          return apiResponse;
        } else {
          LocalStorage().addLogError(LogErrorModel(
              type: 'API',
              errorCode: '$errorCode',
              errorMessage: isNullEmpty(errorMsg) ? errorMsgEn : errorMsg,
              position: positionLog));
          return BaseResponse(
            result: false,
            data: responseMap,
            message: isNullEmpty(errorMsg) ? errorMsgEn : errorMsg,
            code: errorCode,
          );
        }
      }
    } on DioError catch (e) {
      if (e.response != null) {
        // e.response.data có thể trả về _InternalLinkedHashMap hoặc 1 kiểu nào đó (String), tạm thời check thủ công theo runtimeType
        String? errorMessage = e?.response?.data != null &&
                e.response!.data.runtimeType.toString().contains('Map') &&
                !isNullEmpty(e.response!.data['message'])
            ? e.response!.data['message']
            : !isNullEmpty(e?.response?.statusMessage)
                ? e.response!.statusMessage
                : e.message;

        LocalStorage().addLogError(LogErrorModel(
            type: 'API',
            errorCode: '${e.response!.statusCode}',
            errorMessage: errorMessage,
            errorLogException: '$e',
            position: positionLog));

        return BaseResponse(
          result: false,
          data: null,
          message: errorMessage,
          code: e.response!.statusCode,
        );
      }
      if (e.error is SocketException) {
        SocketException? socketException = e.error as SocketException?;

        LocalStorage().addLogError(LogErrorModel(
            type: 'API',
            errorCode: '${socketException?.osError?.errorCode ?? ERROR_SOCKET_EXCEPTION}',
            errorMessage: socketException?.osError?.message ?? '',
            errorLogException: '$e',
            position: positionLog));

        return BaseResponse(
          result: false,
          data: null,
          message: socketException?.osError?.message ?? '',
          code: socketException?.osError?.errorCode ?? ERROR_SOCKET_EXCEPTION,
        );
      }

      if (e.type == DioErrorType.connectionTimeout) {
        LocalStorage().addLogError(LogErrorModel(
            type: 'API',
            errorCode: ERROR_TIMEOUT.toString(),
            errorMessage: AppStrings.getString(AppStrings.timeoutError),
            position: positionLog));

        return BaseResponse(
          result: false,
          data: null,
          message: AppStrings.getString(AppStrings.timeoutError),
          code: ERROR_TIMEOUT,
        );
        
      }

      LocalStorage().addLogError(LogErrorModel(
          type: 'API',
          errorCode: '-9999',
          errorMessage: e.error != null ? e.error.toString() : '',
          position: positionLog));

      return BaseResponse(
        result: false,
        data: null,
        message: e.error != null ? e.error.toString() : '',
        code: -9999,
      );
    }

    LocalStorage().addLogError(LogErrorModel(
        type: 'API',
        errorCode: '-8888',
        errorMessage: AppStrings.getString(AppStrings.errorUnknown),
        position: positionLog));

    return BaseResponse(
      result: false,
      data: null,
      message: AppStrings.getString(AppStrings.errorUnknown),
      code: -8888,
    );
  }

  bool _isSuccessful(int i) {
    return i >= 200 && i <= 299;
  }
}
