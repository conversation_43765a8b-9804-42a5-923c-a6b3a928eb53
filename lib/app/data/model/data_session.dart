
import 'package:credit_card_type_detector/credit_card_type_detector.dart';

import 'mp_data_login_model.dart';

class PaymentInfoSession {
  String? typePayment;
  int? amount;
  String? description;
  bool? needResetAmount;
  String? udid;
  MPQrChild? selectedQrSource;
  String? qrCode;
}

class InstallmentInfoSession {
  MPInstallmentInfo? selectedBank;
  CreditCardType? selectedCardType;
  int? amount;
  MPItemPeriod? selectedPeriod;
  int? finalAmountPay;
  bool? allowChangeFee;
  bool? enableFeeTrans;
  bool? enableFeeInstallment;
  bool? isPayLink;
  double? feeCard;
  double? feeInstallment;
  late Map selectedCardTypeMap;
  String? linkCheckout;
  int? minuteExpired;
}