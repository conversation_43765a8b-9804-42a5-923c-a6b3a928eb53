

class ReaderMpos {
  String? serialNumber;
  int? readerType;

  ReaderMpos({this.serialNumber, this.readerType});

  ReaderMpos.fromJson(Map<String, dynamic> json) {
    serialNumber = json['serialNumber'];
    readerType = json['readerType'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['serialNumber'] = this.serialNumber;
    data['readerType'] = this.readerType;
    return data;
  }
}