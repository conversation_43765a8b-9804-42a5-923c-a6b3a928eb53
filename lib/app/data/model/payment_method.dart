import '../../controller/app_controller.dart';
import 'mp_data_login_model.dart';

class PaymentMethod {
  bool _swipeCardActive = false;
  bool _qrCodePaymentActive = false;
  bool _vietQrActive = false;
  bool _installmentActive = false;
  bool _walletQrActive = false;
  bool _motoActive = false;
  bool _linkCardActive = false;
  bool _bnplPayment = false;
  bool _depositPayment = false;

  String? sub_cardPayment;
  String? sub_cardPaymentEn;
  String? sub_motoPayment;
  String? sub_motoPaymentEn;
  String? sub_qrCodePayment;
  String? sub_qrCodePaymentEn;
  String? sub_eWalletPayment;
  String? sub_eWalletPaymentEn;
  String? sub_bnplPayment;
  String? sub_bnplPaymentEn;


  int _numPayMethodActive = 0;

  PaymentMethod.initPaymentMethod(MPDataLoginModel userInfo) {
    List<MPItemMenuHome> listMenuHome = userInfo.menuHome ?? [];
    List<MPItemMenuHome> listButtonApps = userInfo.buttonApps ?? [];

    for (MPItemMenuHome buttonApp in listButtonApps) {
      if (buttonApp.code == 'QUET_THE' && (userInfo.enableNormalPayment == 1)) {
        // showButtonSwipeCard = true;
        _swipeCardActive = true;
        _numPayMethodActive++;
        sub_cardPayment = buttonApp.subName ?? '';
        sub_cardPaymentEn = buttonApp.subNameEn ?? '';
      }
      else if (buttonApp.code == 'TAO_VIET_QR') {
        _qrCodePaymentActive = true;
        _numPayMethodActive++;
      }
      else if (buttonApp.code == 'TRA_GOP') {
        _installmentActive = true;
        _numPayMethodActive++;
      } else if (buttonApp.code == 'TT_MOTO') {
        _motoActive = true;
        _numPayMethodActive++;
        sub_motoPayment = buttonApp.subName ?? '';
        sub_motoPaymentEn = buttonApp.subNameEn ?? '';
      } else if (buttonApp.code == 'TAO_LINK_TU_XA') {
        _linkCardActive = true;
        _numPayMethodActive++;
      } else if ((buttonApp.code == 'MUA_TRUOC_TRA_SAU')) {
        _bnplPayment = true;
        _numPayMethodActive++;
        sub_bnplPayment = buttonApp.subName ?? '';
        sub_bnplPaymentEn = buttonApp.subNameEn ?? '';
      } else if ((buttonApp.code == 'TAO_VIET_QR')) {
        sub_qrCodePayment = buttonApp.subName ?? '';
        sub_qrCodePaymentEn = buttonApp.subNameEn ?? '';
      } else if ((buttonApp.code == 'QR_E_WALLET')) {
        sub_eWalletPayment = buttonApp.subName ?? '';
        sub_eWalletPaymentEn = buttonApp.subNameEn ?? '';
      }
    }

    if ((userInfo.allowDeposit != '') && (userInfo.allowDeposit != '0')) {
      _depositPayment = true;
    }

    if (MyAppController.isKozenP12orN4()) {
      _walletQrActive = true;
    }

    if (userInfo.listBankQR != null) {
      for (var qrObj in userInfo.listBankQR!) {
        var qrShortName = qrObj.shortName ?? '';
        var qrLongName = qrObj.longName ?? '';
        if (qrShortName == "VAQR" || qrLongName == "VIETQR") {
          _vietQrActive = true;
          break;
        }
      }
    }

    // for(MPItemMenuHome button in listButtonApps) {
    //   switch (button.code) {
    //     case 'QUET_THE':
    //       sub_cardPayment = button.subName ?? '';
    //       sub_cardPaymentEn = button.subNameEn ?? '';
    //       break;
    //     case 'TAO_VIET_QR':
    //       sub_qrCodePayment = button.subName ?? '';
    //       sub_qrCodePaymentEn = button.subNameEn ?? '';
    //       break;
    //     case 'QR_E_WALLET':
    //       sub_eWalletPayment = button.subName ?? '';
    //       sub_eWalletPaymentEn = button.subNameEn ?? '';
    //       break;
    //     case 'TT_MOTO':
    //       sub_motoPayment = button.subName ?? '';
    //       sub_motoPaymentEn = button.subNameEn ?? '';
    //       break;
    //     case 'MUA_TRUOC_TRA_SAU':
    //       sub_bnplPayment = button.subName ?? '';
    //       sub_bnplPaymentEn = button.subNameEn ?? '';
    //       break;
    //   }
    // }

  }


  bool get qrCodePaymentActive => _qrCodePaymentActive;

  set qrCodePaymentActive(bool value) {
    _qrCodePaymentActive = value;
  }

  int get numPayMethodActive => _numPayMethodActive;

  set numPayMethodActive(int value) {
    _numPayMethodActive = value;
  }

  bool get swipeCardActive => _swipeCardActive;

  set swipeCardActive(bool value) {
    _swipeCardActive = value;
  }

  bool get vietQrActive => _vietQrActive;

  set vietQrActive(bool value) {
    _vietQrActive = value;
  }

  bool get installmentActive => _installmentActive;

  set installmentActive(bool value) {
    _installmentActive = value;
  }

  bool get walletQrActive => _walletQrActive;

  set walletQrActive(bool value) {
    _walletQrActive = value;
  }

  bool get motoActive => _motoActive;

  set motoActive(bool value) {
    _motoActive = value;
  }

  bool get linkCardActive => _linkCardActive;

  bool get depositPayment => _depositPayment;

  set depositPayment(bool value) {
    _depositPayment = value;
  }

  bool get bnplActive => _bnplPayment;

  set bnplActive(bool value) {
    _bnplPayment = value;
  }

  set linkCardActive(bool value) {
    _linkCardActive = value;
  }
}