import 'package:mposxs/app/data/model/base_response.dart';

class DetailTransactionHistoryModel extends BaseResponseData {
  int? createdDate;
  String? createdBy;
  int? status;
  String? txid;
  String? cardholderName;
  String? pan;
  String? mid;
  String? tid;
  String? authCode;
  String? rrn;
  String? trace;
  String? accquirer;
  int? amount;
  String? issuerCode;
  String? voidReason;
  Merchant? merchant;
  String? description;
  String? udid;
  TransactionInstallment? transactionInstallment;
  TransactionPush? transactionPush;
  int? sendLoyalty;
  int? sendCRM;
  String? applicationUsageControl;
  TransactionRequest? transactionRequest;
  TransactionFeedback? transactionFeedback;
  String? transactionType;
  GwTransaction? gwTransaction;
  String? transactionPushType;
  String? issuerName;
  String? issuerNameEn;
  String? issuerBank;
  int? rownum;
  int? id;
  int? version;
  int? count;

  DetailTransactionHistoryModel(
      {this.createdDate,
      this.createdBy,
      this.status,
      this.txid,
      this.cardholderName,
      this.pan,
      this.mid,
      this.tid,
      this.authCode,
      this.rrn,
      this.trace,
      this.accquirer,
      this.amount,
      this.issuerCode,
      this.merchant,
      this.description,
      this.udid,
      this.transactionInstallment,
      this.transactionPush,
      this.sendLoyalty,
      this.sendCRM,
      this.applicationUsageControl,
      this.transactionRequest,
      this.transactionFeedback,
      this.transactionType,
      this.gwTransaction,
      this.transactionPushType,
      this.issuerName,
      this.issuerNameEn,
      this.issuerBank,
      this.voidReason,
      this.rownum,
      this.id,
      this.version,
      this.count});

  DetailTransactionHistoryModel.fromJson(Map<String, dynamic> json) {
    createdDate = json['createdDate'];
    createdBy = json['createdBy'];
    status = json['status'];
    txid = json['txid'];
    cardholderName = json['cardholderName'];
    pan = json['pan'];
    mid = json['mid'];
    tid = json['tid'];
    authCode = json['authCode'];
    rrn = json['rrn'];
    trace = json['trace'].toString();
    accquirer = json['accquirer'];
    amount = json['amount'];
    issuerCode = json['issuerCode'];
    merchant = json['merchant'] != null ? new Merchant.fromJson(json['merchant']) : null;
    description = json['description'];
    udid = json['udid'];
    transactionInstallment = json['transactionInstallment'] != null
        ? new TransactionInstallment.fromJson(json['transactionInstallment'])
        : null;
    transactionPush = json['transactionPush'] != null ? new TransactionPush.fromJson(json['transactionPush']) : null;
    sendLoyalty = json['sendLoyalty'];
    sendCRM = json['sendCRM'];
    applicationUsageControl = json['applicationUsageControl'];
    transactionRequest =
        json['transactionRequest'] != null ? new TransactionRequest.fromJson(json['transactionRequest']) : null;
    transactionFeedback =
        json['transactionFeedback'] != null ? new TransactionFeedback.fromJson(json['transactionFeedback']) : null;
    transactionType = json['transactionType'];
    gwTransaction = json['gwTransaction'] != null ? new GwTransaction.fromJson(json['gwTransaction']) : null;
    transactionPushType = json['transactionPushType'];
    issuerName = json['issuerName'];
    issuerNameEn = json['issuerNameEn'];
    issuerBank = json['issuerBank'];
    voidReason = json['voidReason'];
    rownum = json['rownum'];
    id = json['id'];
    version = json['version'];
    count = json['count'];
  }

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['createdDate'] = this.createdDate;
    data['createdBy'] = this.createdBy;
    data['status'] = this.status;
    data['txid'] = this.txid;
    data['cardholderName'] = this.cardholderName;
    data['pan'] = this.pan;
    data['mid'] = this.mid;
    data['tid'] = this.tid;
    data['authCode'] = this.authCode;
    data['rrn'] = this.rrn;
    data['trace'] = this.trace;
    data['accquirer'] = this.accquirer;
    data['amount'] = this.amount;
    data['issuerCode'] = this.issuerCode;
    if (this.merchant != null) {
      data['merchant'] = this.merchant!.toJson();
    }
    data['description'] = this.description;
    data['udid'] = this.udid;
    if (this.transactionInstallment != null) {
      data['transactionInstallment'] = this.transactionInstallment!.toJson();
    }
    if (this.transactionPush != null) {
      data['transactionPush'] = this.transactionPush!.toJson();
    }
    data['sendLoyalty'] = this.sendLoyalty;
    data['sendCRM'] = this.sendCRM;
    data['applicationUsageControl'] = this.applicationUsageControl;
    if (this.transactionRequest != null) {
      data['transactionRequest'] = this.transactionRequest!.toJson();
    }
    if (this.transactionFeedback != null) {
      data['transactionFeedback'] = this.transactionFeedback!.toJson();
    }
    data['transactionType'] = this.transactionType;
    if (this.gwTransaction != null) {
      data['gwTransaction'] = this.gwTransaction!.toJson();
    }
    data['transactionPushType'] = this.transactionPushType;
    data['issuerName'] = this.issuerName;
    data['issuerNameEn'] = this.issuerNameEn;
    data['issuerBank'] = this.issuerBank;
    data['voidReason'] = this.voidReason;
    data['rownum'] = this.rownum;
    data['id'] = this.id;
    data['version'] = this.version;
    data['count'] = this.count;
    return data;
  }
}

class Merchant {
  String? businessName;
  String? businessShortName;
  String? businessAddress1;
  int? maxAmountPerTransaction;
  int? maxAmountPerMonth;
  int? numReader;
  int? feeFk;
  int? monthyAmount;
  int? dailyAmount;
  int? tmpAmount;
  int? maxAmountPerDay;
  int? withdrawDay;
  int? feedback;
  int? feedbackFrom;
  int? rownum;
  int? id;
  int? version;
  int? count;

  Merchant(
      {this.businessName,
      this.businessShortName,
      this.businessAddress1,
      this.maxAmountPerTransaction,
      this.maxAmountPerMonth,
      this.numReader,
      this.feeFk,
      this.monthyAmount,
      this.dailyAmount,
      this.tmpAmount,
      this.maxAmountPerDay,
      this.withdrawDay,
      this.feedback,
      this.feedbackFrom,
      this.rownum,
      this.id,
      this.version,
      this.count});

  Merchant.fromJson(Map<String, dynamic> json) {
    businessName = json['businessName'];
    businessShortName = json['businessShortName'];
    businessAddress1 = json['businessAddress1'];
    maxAmountPerTransaction = json['maxAmountPerTransaction'];
    maxAmountPerMonth = json['maxAmountPerMonth'];
    numReader = json['numReader'];
    feeFk = json['feeFk'];
    monthyAmount = json['monthyAmount'];
    dailyAmount = json['dailyAmount'];
    tmpAmount = json['tmpAmount'];
    maxAmountPerDay = json['maxAmountPerDay'];
    withdrawDay = json['withdrawDay'];
    feedback = json['feedback'];
    feedbackFrom = json['feedbackFrom'];
    rownum = json['rownum'];
    id = json['id'];
    version = json['version'];
    count = json['count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['businessName'] = this.businessName;
    data['businessShortName'] = this.businessShortName;
    data['businessAddress1'] = this.businessAddress1;
    data['maxAmountPerTransaction'] = this.maxAmountPerTransaction;
    data['maxAmountPerMonth'] = this.maxAmountPerMonth;
    data['numReader'] = this.numReader;
    data['feeFk'] = this.feeFk;
    data['monthyAmount'] = this.monthyAmount;
    data['dailyAmount'] = this.dailyAmount;
    data['tmpAmount'] = this.tmpAmount;
    data['maxAmountPerDay'] = this.maxAmountPerDay;
    data['withdrawDay'] = this.withdrawDay;
    data['feedback'] = this.feedback;
    data['feedbackFrom'] = this.feedbackFrom;
    data['rownum'] = this.rownum;
    data['id'] = this.id;
    data['version'] = this.version;
    data['count'] = this.count;
    return data;
  }
}

class TransactionInstallment {
  InstallmentSaleOutRate? installmentSaleOutRate;
  int? period;
  String? periodType;
  String? customerMobile;
  int? rownum;
  int? id;
  int? version;
  int? count;

  TransactionInstallment(
      {this.installmentSaleOutRate,
      this.period,
      this.periodType,
      this.customerMobile,
      this.rownum,
      this.id,
      this.version,
      this.count});

  TransactionInstallment.fromJson(Map<String, dynamic> json) {
    installmentSaleOutRate = json['installmentSaleOutRate'] != null
        ? new InstallmentSaleOutRate.fromJson(json['installmentSaleOutRate'])
        : null;
    period = json['period'];
    periodType = json['periodType'];
    customerMobile = json['customerMobile'];
    rownum = json['rownum'];
    id = json['id'];
    version = json['version'];
    count = json['count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.installmentSaleOutRate != null) {
      data['installmentSaleOutRate'] = this.installmentSaleOutRate!.toJson();
    }
    data['period'] = this.period;
    data['periodType'] = this.periodType;
    data['customerMobile'] = this.customerMobile;
    data['rownum'] = this.rownum;
    data['id'] = this.id;
    data['version'] = this.version;
    data['count'] = this.count;
    return data;
  }
}

class InstallmentSaleOutRate {
  int? period;
  InstallmentBank? installmentBank;
  String? periodType;
  int? rownum;
  int? version;
  int? count;

  InstallmentSaleOutRate({this.period, this.installmentBank, this.periodType, this.rownum, this.version, this.count});

  InstallmentSaleOutRate.fromJson(Map<String, dynamic> json) {
    period = json['period'];
    installmentBank = json['installmentBank'] != null ? new InstallmentBank.fromJson(json['installmentBank']) : null;
    periodType = json['periodType'];
    rownum = json['rownum'];
    version = json['version'];
    count = json['count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['period'] = this.period;
    if (this.installmentBank != null) {
      data['installmentBank'] = this.installmentBank!.toJson();
    }
    data['periodType'] = this.periodType;
    data['rownum'] = this.rownum;
    data['version'] = this.version;
    data['count'] = this.count;
    return data;
  }
}

class InstallmentBank {
  String? shortName;
  int? rownum;
  int? version;
  int? count;

  InstallmentBank({this.shortName, this.rownum, this.version, this.count});

  InstallmentBank.fromJson(Map<String, dynamic> json) {
    shortName = json['shortName'];
    rownum = json['rownum'];
    version = json['version'];
    count = json['count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['shortName'] = this.shortName;
    data['rownum'] = this.rownum;
    data['version'] = this.version;
    data['count'] = this.count;
    return data;
  }
}

class TransactionPush {
  String? orderCode;
  String? errorCode;
  String? errorMessage;
  String? referenceId;
  String? type;
  int? rownum;
  int? id;
  int? version;
  int? count;

  TransactionPush(
      {this.orderCode,
      this.errorCode,
      this.errorMessage,
      this.referenceId,
      this.type,
      this.rownum,
      this.id,
      this.version,
      this.count});

  TransactionPush.fromJson(Map<String, dynamic> json) {
    orderCode = json['orderCode'];
    errorCode = json['errorCode'];
    errorMessage = json['errorMessage'];
    referenceId = json['referenceId'];
    type = json['type'];
    rownum = json['rownum'];
    id = json['id'];
    version = json['version'];
    count = json['count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['orderCode'] = this.orderCode;
    data['errorCode'] = this.errorCode;
    data['errorMessage'] = this.errorMessage;
    data['referenceId'] = this.referenceId;
    data['type'] = this.type;
    data['rownum'] = this.rownum;
    data['id'] = this.id;
    data['version'] = this.version;
    data['count'] = this.count;
    return data;
  }
}

class TransactionRequest {
  int? rownum;
  int? id;
  int? version;
  int? count;

  TransactionRequest({this.rownum, this.id, this.version, this.count});

  TransactionRequest.fromJson(Map<String, dynamic> json) {
    rownum = json['rownum'];
    id = json['id'];
    version = json['version'];
    count = json['count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['rownum'] = this.rownum;
    data['id'] = this.id;
    data['version'] = this.version;
    data['count'] = this.count;
    return data;
  }
}

class TransactionFeedback {
  int? rownum;
  int? version;
  int? count;

  TransactionFeedback({this.rownum, this.version, this.count});

  TransactionFeedback.fromJson(Map<String, dynamic> json) {
    rownum = json['rownum'];
    version = json['version'];
    count = json['count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['rownum'] = this.rownum;
    data['version'] = this.version;
    data['count'] = this.count;
    return data;
  }
}

class GwTransaction {
  VasProduct? vasProduct;
  int? rownum;
  int? version;
  int? count;

  GwTransaction({this.vasProduct, this.rownum, this.version, this.count});

  GwTransaction.fromJson(Map<String, dynamic> json) {
    vasProduct = json['vasProduct'] != null ? new VasProduct.fromJson(json['vasProduct']) : null;
    rownum = json['rownum'];
    version = json['version'];
    count = json['count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.vasProduct != null) {
      data['vasProduct'] = this.vasProduct!.toJson();
    }
    data['rownum'] = this.rownum;
    data['version'] = this.version;
    data['count'] = this.count;
    return data;
  }
}

class VasProduct {
  ProductBilling? productBilling;
  ProductTelco? productTelco;
  int? rownum;
  int? version;
  int? count;

  VasProduct({this.productBilling, this.productTelco, this.rownum, this.version, this.count});

  VasProduct.fromJson(Map<String, dynamic> json) {
    productBilling = json['productBilling'] != null ? new ProductBilling.fromJson(json['productBilling']) : null;
    productTelco = json['productTelco'] != null ? new ProductTelco.fromJson(json['productTelco']) : null;
    rownum = json['rownum'];
    version = json['version'];
    count = json['count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.productBilling != null) {
      data['productBilling'] = this.productBilling!.toJson();
    }
    if (this.productTelco != null) {
      data['productTelco'] = this.productTelco!.toJson();
    }
    data['rownum'] = this.rownum;
    data['version'] = this.version;
    data['count'] = this.count;
    return data;
  }
}

class ProductBilling {
  TransactionFeedback? gwService;
  TransactionFeedback? vasIssuer;
  int? rownum;
  int? version;
  int? count;

  ProductBilling({this.gwService, this.vasIssuer, this.rownum, this.version, this.count});

  ProductBilling.fromJson(Map<String, dynamic> json) {
    gwService = json['gwService'] != null ? new TransactionFeedback.fromJson(json['gwService']) : null;
    vasIssuer = json['vasIssuer'] != null ? new TransactionFeedback.fromJson(json['vasIssuer']) : null;
    rownum = json['rownum'];
    version = json['version'];
    count = json['count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.gwService != null) {
      data['gwService'] = this.gwService!.toJson();
    }
    if (this.vasIssuer != null) {
      data['vasIssuer'] = this.vasIssuer!.toJson();
    }
    data['rownum'] = this.rownum;
    data['version'] = this.version;
    data['count'] = this.count;
    return data;
  }
}

class ProductTelco {
  TransactionFeedback? telCogwService;
  TransactionFeedback? telCoIssuer;
  int? rownum;
  int? version;
  int? count;

  ProductTelco({this.telCogwService, this.telCoIssuer, this.rownum, this.version, this.count});

  ProductTelco.fromJson(Map<String, dynamic> json) {
    telCogwService = json['telCogwService'] != null ? new TransactionFeedback.fromJson(json['telCogwService']) : null;
    telCoIssuer = json['telCoIssuer'] != null ? new TransactionFeedback.fromJson(json['telCoIssuer']) : null;
    rownum = json['rownum'];
    version = json['version'];
    count = json['count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.telCogwService != null) {
      data['telCogwService'] = this.telCogwService!.toJson();
    }
    if (this.telCoIssuer != null) {
      data['telCoIssuer'] = this.telCoIssuer!.toJson();
    }
    data['rownum'] = this.rownum;
    data['version'] = this.version;
    data['count'] = this.count;
    return data;
  }
}
