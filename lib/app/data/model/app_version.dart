import 'base_response.dart';

class AppVersion extends BaseResponseData{
  MposVersionUpdate? android = new MposVersionUpdate();
  MposVersionUpdate? sP01 = new MposVersionUpdate();
  MposVersionUpdate? sP02 = new MposVersionUpdate();
  MposVersionUpdate? n31 = new MposVersionUpdate();

  AppVersion({this.android, this.sP01, this.sP02});

  AppVersion.fromJson(Map<String, dynamic> json) {
    android = json['android'] != null
        ? new MposVersionUpdate.fromJson(json['android'])
        : null;
    sP01 = json['SP01'] != null ? new MposVersionUpdate.fromJson(json['SP01']) : null;
    sP02 = json['SP02'] != null ? new MposVersionUpdate.fromJson(json['SP02']) : null;
    n31 = json['N31'] != null ? new MposVersionUpdate.fromJson(json['N31']) : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.android != null) {
      data['android'] = this.android!.toJson();
    }
    if (this.sP01 != null) {
      data['SP01'] = this.sP01!.toJson();
    }
    if (this.sP02 != null) {
      data['SP02'] = this.sP02!.toJson();
    }
    if (this.sP02 != null) {
      data['N31'] = this.n31!.toJson();
    }
    return data;
  }
}

class MposVersionUpdate {
  int? type = -1;
  int? vc = -1;
  int? ntf = -1;
  String? contentVi = '';
  String? contentEn = '';
  String? link = '';

  MposVersionUpdate(
      {this.type,
        this.vc,
        this.ntf,
        this.contentVi,
        this.contentEn,
        this.link});

  MposVersionUpdate.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    vc = json['vc'];
    ntf = json['ntf'];
    contentVi = json['content_vi'];
    contentEn = json['content_en'];
    link = json['link'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['type'] = this.type;
    data['vc'] = this.vc;
    data['ntf'] = this.ntf;
    data['content_vi'] = this.contentVi;
    data['content_en'] = this.contentEn;
    data['link'] = this.link;
    return data;
  }
}
