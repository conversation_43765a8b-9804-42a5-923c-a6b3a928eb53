
import 'package:intl/intl.dart';
import 'package:mposxs/app/data/provider/session_data.dart';
import 'package:mposxs/build_constants.dart';

class LogErrorModel {
  String? date;
  String? account;
  String? serial;
  String? device;
  String? type;
  String? errorCode;
  String? errorMessage;
  String? errorLogException;
  String? position;
  String? appVersion;
  String? appType;

  LogErrorModel({
    date,
    account,
    serial,
    device,
    type,
    errorCode,
    errorMessage,
    errorLogException,
    position,
    appVersion,
    appType,
  }) {
    this.date = date ?? DateFormat('dd/MM/yyyy HH:mm:ss').format(DateTime.now());
    this.account = account ?? SessionData.account;
    this.serial = serial ?? SessionData.serial;
    this.device = device ?? SessionData.device;
    this.type = type ?? '';
    this.errorCode = errorCode ?? '';
    this.errorMessage = errorMessage ?? '';
    this.errorLogException = errorLogException ?? '';
    this.position = position ?? '';
    this.appVersion = appVersion ?? SessionData.version;
    this.appType = appType ?? BuildConstants.appTYPE;
  }

  Map<String, dynamic> toJson(){
    Map<String, dynamic> result = Map();

    if(date!.isNotEmpty) {result['date'] = date;}
    if(account!.isNotEmpty) {result['account'] = account;}
    if(serial!.isNotEmpty) {result['serial'] = serial;}
    if(device!.isNotEmpty) {result['device'] = device;}
    if(type!.isNotEmpty) {result['type'] = type;}
    if(errorCode!.isNotEmpty) {result['errorCode'] = errorCode;}
    if(errorMessage!.isNotEmpty) {result['errorMessage'] = errorMessage;}
    if(errorLogException!.isNotEmpty) {result['errorLogException'] = errorLogException;}
    if(position!.isNotEmpty) {result['position'] = position;}
    if(appVersion!.isNotEmpty) {result['appVersion'] = appVersion;}
    if(appType!.isNotEmpty) {result['appType'] = appType;}

    return result;
  }

  String toString(){
    String result = '';

    if(date!.isNotEmpty) {result += ' date:' + date!;}
    if(account!.isNotEmpty) {result +=' account:' + account!;}
    if(serial!.isNotEmpty) {result +=' serial:' + serial!;}
    if(device!.isNotEmpty) {result +=' device:' + device!;}
    if(type!.isNotEmpty) {result +=' type:' + type!;}
    if(errorCode!.isNotEmpty) {result +=' errorCode:' + errorCode!;}
    if(errorMessage!.isNotEmpty) {result +=' errorMsg:' + errorMessage!;}
    if(errorLogException!.isNotEmpty) {result +=' LogExp:' + errorLogException!;}
    if(position!.isNotEmpty) {result +=' pos:' + position!;}
    if(appVersion!.isNotEmpty) {result +=' appVer:' + appVersion!;}
    if(appType!.isNotEmpty) {result +=' appType:' + appType!;}

    return result;
  }

}