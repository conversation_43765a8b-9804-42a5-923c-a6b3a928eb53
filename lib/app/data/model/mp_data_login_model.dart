import 'package:mposxs/app/data/model/base_response.dart';

import '../../util/app_validation.dart';

class MPDataLoginModel  implements BaseResponseData{
  final MPConfig? config;
  final String? emailMerchant;
  final String? bankName;
  final String? merchantName;
  final List<MPInstallmentInfo>? installmentInfo;
  final List<MPInstallmentVaymuonInfo>? installmentVaymuonInfo;
  final List<MPExchangeInfo>? exchangeInfo;
  final List<MPExchangeQrInfo>? exchangeQrInfo;
  final List<MPExchangeLinkCardInfo>? exchangeLinkCardInfo;
  final List<MPExchangeVaymuonInfo>? exchangeVaymuonInfo;
  final MPQuickWithdrawInfo? quickWithdrawInfo;
  final int? isAllowRooted;
  final int? restrictInternationalCard;
  final int? emvRestrictInternationalCard;
  final int? isSaveLogin;
  final int? isShowNormalPayment;
  final int? isShowInstallmentPayment;
  final int? isShowNew;
  final int? isShowChangePassword;
  final int? isShowTransactionHistory;
  final int? checkFeeTrans;
  final int? checkFeeInstallment;
  final int? checkFeeChange;
  final int? sendTrxReceipt;
  final int? isShowVimoQr;
  final int? isFeedback;
  final String? feedbackAmountMin;
  final String? paymentAmountMin;
  final int? optCheckInstallmentBin;
  final int? optConvertVimo;
  final String? optConvertVimoAmount;
  final bool? upgradeEMVConfig;
  final String? kData;
  final bool? outage;
  final String? outageMessage;
  final int? enableNormalPayment;
  final int? isPayLink;
  final int? isNormalPayLink;
  final int? oTAPayout;
  final int? isPayCard;
  final int? optCheckAffiliate;
  final int? showMenuLending;
  final String? menuLendingLink;
  final int? mandatoryCheckLimit;
  final int? permitSettlement;
  final int? permitVoid;
  final String? businessName;
  final String? mcShortName;
  final String? authoriserContactNumber;
  final String? authoriserName;
  final int? paybyVasBalanceFlag;
  final int? rechargeVasFlatFee;
  final double? rechargeVasPercentageFee;
  final int? createLinkPaymentFlag;
  final bool? hasPasswordLv2;
  final String? utmSource;
  final bool? enableRethinkdb;
  final String? rethinkHostName;
  final String? rethinkPort;
  final String? rethinkDbName;
  final String? rethinkUsername;
  final String? rethinkUserPassword;
  final String? blockPinRange;
  final int? rechargeVpbankFlg;
  final String? taptophoneSdkUrl;
  final MPBanner? banners;
  final List<MPConfigParamRequires>? configParamRequires;
  final String? mid;
  final List<MPItemBanner>? listBanner;
  final String? businessAddress;
  final int? ekycVpbankFlg;
  final int? isSupplierPromotion;
  final int? checkFeeCustomerQr;
  final int? checkFeeChangeQr;
  final int? checkFeeCustomerNormal;
  final int? checkFeeChangeNormal;
  final bool? installmentDiscount;
  final String? mobileUserToken;
  final String? baseUrlPortal;
  final List<MPExchangeQrFeeCustomer>? exchangeQrFeeCustomer;
  final String? optPushTransaction;
  final int? isUnivestLink;
  final List<MPBankQR>? listBankQR;
  final List<MPInternationalQR>? listInternationalQR;
  final String? username;
  final List<MPItemMenuHome>? menuHome;
  final List<MPItemMenuHome>? buttonApps;
  final String? versionBinLocal;
  final String? allowDeposit;
  final int? skipSignature;
  final int? isAutoPay;
  final int? isConfirmPassword;
  final int? isMacqFlow;
  final MPMores? mores;
  final bool? isFirmwareUpdate;
  final String? userType;
  final int? minAmount;
  final String? merchantId;
  final String? isSaleService;
  final String? isDisableCheckGps;
  final String? mvisaTid;
  final String? mvisaMid;
  final String? mvisaMidMaster;
  final String? mcc;
  final List<MPItemQr>? listQr;
  final bool? isEnableVetcService;
  final String? mobileUserPhone;
  final bool? check;
  final MPError? error;


  int? lastTimeCache;
  String? pass;
  String? muid;

  MPDataLoginModel({
    this.config,
    this.emailMerchant,
    this.bankName,
    this.merchantName,
    this.installmentInfo,
    this.installmentVaymuonInfo,
    this.exchangeInfo,
    this.exchangeQrInfo,
    this.exchangeLinkCardInfo,
    this.exchangeVaymuonInfo,
    this.quickWithdrawInfo,
    this.isAllowRooted,
    this.restrictInternationalCard,
    this.emvRestrictInternationalCard,
    this.isSaveLogin,
    this.isShowNormalPayment,
    this.isShowInstallmentPayment,
    this.isShowNew,
    this.isShowChangePassword,
    this.isShowTransactionHistory,
    this.checkFeeTrans,
    this.checkFeeInstallment,
    this.checkFeeChange,
    this.sendTrxReceipt,
    this.isShowVimoQr,
    this.isFeedback,
    this.feedbackAmountMin,
    this.paymentAmountMin,
    this.optCheckInstallmentBin,
    this.optConvertVimo,
    this.optConvertVimoAmount,
    this.upgradeEMVConfig,
    this.kData,
    this.outage,
    this.outageMessage,
    this.enableNormalPayment,
    this.isPayLink,
    this.isNormalPayLink,
    this.oTAPayout,
    this.isPayCard,
    this.optCheckAffiliate,
    this.showMenuLending,
    this.menuLendingLink,
    this.mandatoryCheckLimit,
    this.permitSettlement,
    this.permitVoid,
    this.businessName,
    this.mcShortName,
    this.authoriserContactNumber,
    this.authoriserName,
    this.paybyVasBalanceFlag,
    this.rechargeVasFlatFee,
    this.rechargeVasPercentageFee,
    this.createLinkPaymentFlag,
    this.hasPasswordLv2,
    this.utmSource,
    this.enableRethinkdb,
    this.rethinkHostName,
    this.rethinkPort,
    this.rethinkDbName,
    this.rethinkUsername,
    this.rethinkUserPassword,
    this.blockPinRange,
    this.rechargeVpbankFlg,
    this.taptophoneSdkUrl,
    this.banners,
    this.configParamRequires,
    this.mid,
    this.listBanner,
    this.businessAddress,
    this.ekycVpbankFlg,
    this.isSupplierPromotion,
    this.checkFeeCustomerQr,
    this.checkFeeChangeQr,
    this.checkFeeCustomerNormal,
    this.checkFeeChangeNormal,
    this.installmentDiscount,
    this.mobileUserToken,
    this.baseUrlPortal,
    this.exchangeQrFeeCustomer,
    this.optPushTransaction,
    this.isUnivestLink,
    this.listBankQR,
    this.listInternationalQR,
    this.username,
    this.menuHome,
    this.buttonApps,
    this.versionBinLocal,
    this.allowDeposit,
    this.skipSignature,
    this.isAutoPay,
    this.isConfirmPassword,
    this.isMacqFlow,
    this.mores,
    this.isFirmwareUpdate,
    this.userType,
    this.minAmount,
    this.merchantId,
    this.isSaleService,
    this.isDisableCheckGps,
    this.mvisaTid,
    this.mvisaMid,
    this.mvisaMidMaster,
    this.mcc,
    this.listQr,
    this.isEnableVetcService,
    this.mobileUserPhone,
    this.check,
    this.error,
  });

  MPDataLoginModel.fromJson(Map<String, dynamic> json)
      : config = ((json['config'] != null) && (json['config'] as Map<String, dynamic>?) != null)
            ? MPConfig.fromJson(json['config'] as Map<String, dynamic>)
            : null,
        emailMerchant = json['emailMerchant'] as String?,
        bankName = json['bankName'] as String?,
        merchantName = json['merchantName'] as String?,
        installmentInfo = (json['installmentInfo'] as List?)
            ?.map((dynamic e) => MPInstallmentInfo.fromJson(e as Map<String, dynamic>))
            .toList(),
        installmentVaymuonInfo = (json['installmentVaymuonInfo'] as List?)
            ?.map((dynamic e) => MPInstallmentVaymuonInfo.fromJson(e as Map<String, dynamic>))
            .toList(),
        exchangeInfo = (json['exchangeInfo'] as List?)
            ?.map((dynamic e) => MPExchangeInfo.fromJson(e as Map<String, dynamic>))
            .toList(),
        exchangeQrInfo = (json['exchangeQrInfo'] as List?)
            ?.map((dynamic e) => MPExchangeQrInfo.fromJson(e as Map<String, dynamic>))
            .toList(),
        exchangeLinkCardInfo = (json['exchangeLinkCardInfo'] as List?)
            ?.map((dynamic e) => MPExchangeLinkCardInfo.fromJson(e as Map<String, dynamic>))
            .toList(),
        exchangeVaymuonInfo = (json['exchangeVaymuonInfo'] as List?)
            ?.map((dynamic e) => MPExchangeVaymuonInfo.fromJson(e as Map<String, dynamic>))
            .toList(),
        quickWithdrawInfo = (json['quickWithdrawInfo'] as Map<String, dynamic>?) != null
            ? MPQuickWithdrawInfo.fromJson(json['quickWithdrawInfo'] as Map<String, dynamic>)
            : null,
        isAllowRooted = json['isAllowRooted'] as int?,
        restrictInternationalCard = json['restrictInternationalCard'] as int?,
        emvRestrictInternationalCard = json['emvRestrictInternationalCard'] as int?,
        isSaveLogin = json['isSaveLogin'] as int?,
        isShowNormalPayment = json['isShowNormalPayment'] as int?,
        isShowInstallmentPayment = json['isShowInstallmentPayment'] as int?,
        isShowNew = json['isShowNew'] as int?,
        isShowChangePassword = json['isShowChangePassword'] as int?,
        isShowTransactionHistory = json['isShowTransactionHistory'] as int?,
        checkFeeTrans = json['checkFeeTrans'] as int?,
        checkFeeInstallment = json['checkFeeInstallment'] as int?,
        checkFeeChange = json['checkFeeChange'] as int?,
        sendTrxReceipt = json['sendTrxReceipt'] as int?,
        isShowVimoQr = json['isShowVimoQr'] as int?,
        isFeedback = json['isFeedback'] as int?,
        feedbackAmountMin = json['feedbackAmountMin'].toString(),
        paymentAmountMin = json['paymentAmountMin'].toString(),
        optCheckInstallmentBin = json['optCheckInstallmentBin'] as int?,
        optConvertVimo = json['optConvertVimo'] as int?,
        optConvertVimoAmount = json['optConvertVimoAmount'].toString(),
        upgradeEMVConfig = json['upgradeEMVConfig'] as bool?,
        kData = json['kData'] as String?,
        outage = json['outage'] as bool?,
        outageMessage = json['outageMessage'] as String?,
        enableNormalPayment = json['enableNormalPayment'] as int?,
        isPayLink = json['isPayLink'] as int?,
        isNormalPayLink = json['isNormalPayLink'] as int?,
        oTAPayout = json['OTAPayout'] as int?,
        isPayCard = json['isPayCard'] as int?,
        optCheckAffiliate = json['optCheckAffiliate'] as int?,
        showMenuLending = json['showMenuLending'] as int?,
        menuLendingLink = json['menuLendingLink'] as String?,
        mandatoryCheckLimit = json['mandatoryCheckLimit'] as int?,
        permitSettlement = json['permitSettlement'] as int?,
        permitVoid = json['permitVoid'] as int?,
        businessName = json['businessName'] as String?,
        mcShortName = json['mcShortName'] as String?,
        authoriserContactNumber = json['authoriserContactNumber'] as String?,
        authoriserName = json['authoriserName'] as String?,
        paybyVasBalanceFlag = json['paybyVasBalanceFlag'] as int?,
        rechargeVasFlatFee = json['rechargeVasFlatFee'] as int?,
        rechargeVasPercentageFee = json['rechargeVasPercentageFee'] as double?,
        createLinkPaymentFlag = json['createLinkPaymentFlag'] as int?,
        hasPasswordLv2 = json['hasPasswordLv2'] as bool?,
        utmSource = json['utmSource'] as String?,
        enableRethinkdb = (json['enableRethinkdb'] as bool?) ?? false,
        rethinkHostName = json['rethinkHostName'] as String?,
        rethinkPort = json['rethinkPort'].toString(),
        rethinkDbName = json['rethinkDbName'] as String?,
        rethinkUsername = json['rethinkUsername'] as String?,
        rethinkUserPassword = json['rethinkUserPassword'] as String?,
        blockPinRange = json['blockPinRange'] as String?,
        rechargeVpbankFlg = json['rechargeVpbankFlg'] as int?,
        taptophoneSdkUrl = json['taptophoneSdkUrl'] as String?,
        banners = (json['banners'] as Map<String, dynamic>?) != null
            ? MPBanner.fromJson(json['banners'] as Map<String, dynamic>)
            : null,
        configParamRequires = (json['configParamRequires'] as List?)
            ?.map((dynamic e) => MPConfigParamRequires.fromJson(e as Map<String, dynamic>))
            .toList(),
        mid = json['mid'] as String?,
        listBanner = (json['listBanner'] as List?)
            ?.map((dynamic e) => MPItemBanner.fromJson(e as Map<String, dynamic>))
            .toList(),
        businessAddress = json['businessAddress'] as String?,
        ekycVpbankFlg = json['ekycVpbankFlg'] as int?,
        isSupplierPromotion = json['isSupplierPromotion'] as int?,
        checkFeeCustomerQr = json['checkFeeCustomerQr'] as int?,
        checkFeeChangeQr = json['checkFeeChangeQr'] as int?,
        checkFeeCustomerNormal = json['checkFeeCustomerNormal'] as int?,
        checkFeeChangeNormal = json['checkFeeChangeNormal'] as int?,
        installmentDiscount = json['installmentDiscount'] as bool?,
        mobileUserToken = json['mobileUserToken'] as String?,
        baseUrlPortal = json['baseUrlPortal'] as String?,
        exchangeQrFeeCustomer = (json['exchangeQrFeeCustomer'] as List?)
            ?.map((dynamic e) => MPExchangeQrFeeCustomer.fromJson(e as Map<String, dynamic>))
            .toList(),
        optPushTransaction = json['optPushTransaction'] as String?,
        isUnivestLink = json['isUnivestLink'] as int?,
        listBankQR =
            (json['listBankQR'] as List?)?.map((dynamic e) => MPBankQR.fromJson(e as Map<String, dynamic>)).toList(),
        listInternationalQR = (json['listInternationalQR'] as List?)
            ?.map((dynamic e) => MPInternationalQR.fromJson(e as Map<String, dynamic>))
            .toList(),
        username = json['username'] as String?,
        menuHome = (json['menuHome'] as List?)
            ?.map((dynamic e) => MPItemMenuHome.fromJson(e as Map<String, dynamic>))
            .toList(),
        buttonApps = (json['buttonApps'] as List?)
            ?.map((dynamic e) => MPItemMenuHome.fromJson(e as Map<String, dynamic>))
            .toList(),
        versionBinLocal = json['versionBinLocal'] as String?,
        allowDeposit = (json['allowDeposit'] ?? '').toString(),
        skipSignature = json['skipSignature'] as int?,
        isAutoPay = json['isAutoPay'] as int?,
        isConfirmPassword = json['isConfirmPassword'] as int?,
        isMacqFlow = json['isMacqFlow'] as int?,
        mores = (json['mores'] as Map<String, dynamic>?) != null
            ? MPMores.fromJson(json['mores'] as Map<String, dynamic>)
            : null,
        isFirmwareUpdate = json['isFirmwareUpdate'] as bool?,
        userType = json['userType'] as String?,
        minAmount = json['minAmount'] as int?,
        merchantId = json['merchantId'].toString(),
        isSaleService = json['isSaleService'] as String?,
        isDisableCheckGps = json['isDisableCheckGps'] as String?,
        mvisaTid = json['mvisaTid'] as String?,
        mvisaMid = json['mvisaMid'] as String?,
        mvisaMidMaster = json['mvisaMidMaster'] as String?,
        mcc = json['mcc'] as String?,
        listQr = (json['listQr'] as List?)?.map((dynamic e) => MPItemQr.fromJson(e as Map<String, dynamic>)).toList(),
        isEnableVetcService = json['isEnableVetcService'] as bool?,
        mobileUserPhone = json['mobileUserPhone'] as String?,
        check = json['check'] as bool?,
        error = (json['error'] as Map<String, dynamic>?) != null
            ? MPError.fromJson(json['error'] as Map<String, dynamic>)
            : null,
        lastTimeCache = json['lastTimeCache']!=null?json['lastTimeCache']:0,
        pass = json['pass']!=null?json['pass']:'',
        muid = json['muid']!=null?json['muid']:'';

  Map<String, dynamic> toJson() => {
        'config': config?.toJson(),
        'emailMerchant': emailMerchant,
        'bankName': bankName,
        'merchantName': merchantName,
        'installmentInfo': installmentInfo?.map((e) => e.toJson()).toList(),
        'installmentVaymuonInfo': installmentVaymuonInfo?.map((e) => e.toJson()).toList(),
        'exchangeInfo': exchangeInfo?.map((e) => e.toJson()).toList(),
        'exchangeQrInfo': exchangeQrInfo?.map((e) => e.toJson()).toList(),
        'exchangeLinkCardInfo': exchangeLinkCardInfo?.map((e) => e.toJson()).toList(),
        'exchangeVaymuonInfo': exchangeVaymuonInfo?.map((e) => e.toJson()).toList(),
        'quickWithdrawInfo': quickWithdrawInfo?.toJson(),
        'isAllowRooted': isAllowRooted,
        'restrictInternationalCard': restrictInternationalCard,
        'emvRestrictInternationalCard': emvRestrictInternationalCard,
        'isSaveLogin': isSaveLogin,
        'isShowNormalPayment': isShowNormalPayment,
        'isShowInstallmentPayment': isShowInstallmentPayment,
        'isShowNew': isShowNew,
        'isShowChangePassword': isShowChangePassword,
        'isShowTransactionHistory': isShowTransactionHistory,
        'checkFeeTrans': checkFeeTrans,
        'checkFeeInstallment': checkFeeInstallment,
        'checkFeeChange': checkFeeChange,
        'sendTrxReceipt': sendTrxReceipt,
        'isShowVimoQr': isShowVimoQr,
        'isFeedback': isFeedback,
        'feedbackAmountMin': feedbackAmountMin,
        'paymentAmountMin': paymentAmountMin,
        'optCheckInstallmentBin': optCheckInstallmentBin,
        'optConvertVimo': optConvertVimo,
        'optConvertVimoAmount': optConvertVimoAmount,
        'upgradeEMVConfig': upgradeEMVConfig,
        'kData': kData,
        'outage': outage,
        'outageMessage': outageMessage,
        'enableNormalPayment': enableNormalPayment,
        'isPayLink': isPayLink,
        'isNormalPayLink': isNormalPayLink,
        'OTAPayout': oTAPayout,
        'isPayCard': isPayCard,
        'optCheckAffiliate': optCheckAffiliate,
        'showMenuLending': showMenuLending,
        'menuLendingLink': menuLendingLink,
        'mandatoryCheckLimit': mandatoryCheckLimit,
        'permitSettlement': permitSettlement,
        'permitVoid': permitVoid,
        'businessName': businessName,
        'mcShortName': mcShortName,
        'authoriserContactNumber': authoriserContactNumber,
        'authoriserName': authoriserName,
        'paybyVasBalanceFlag': paybyVasBalanceFlag,
        'rechargeVasFlatFee': rechargeVasFlatFee,
        'rechargeVasPercentageFee': rechargeVasPercentageFee,
        'createLinkPaymentFlag': createLinkPaymentFlag,
        'hasPasswordLv2': hasPasswordLv2,
        'utmSource': utmSource,
        'enableRethinkdb': enableRethinkdb,
        'rethinkHostName': rethinkHostName,
        'rethinkPort': rethinkPort,
        'rethinkDbName': rethinkDbName,
        'rethinkUsername': rethinkUsername,
        'rethinkUserPassword': rethinkUserPassword,
        'blockPinRange': blockPinRange,
        'rechargeVpbankFlg': rechargeVpbankFlg,
        'taptophoneSdkUrl': taptophoneSdkUrl,
        'banners': banners?.toJson(),
        'configParamRequires': configParamRequires?.map((e) => e.toJson()).toList(),
        'mid': mid,
        'listBanner': listBanner?.map((e) => e.toJson()).toList(),
        'businessAddress': businessAddress,
        'ekycVpbankFlg': ekycVpbankFlg,
        'isSupplierPromotion': isSupplierPromotion,
        'checkFeeCustomerQr': checkFeeCustomerQr,
        'checkFeeChangeQr': checkFeeChangeQr,
        'checkFeeCustomerNormal': checkFeeCustomerNormal,
        'checkFeeChangeNormal': checkFeeChangeNormal,
        'installmentDiscount': installmentDiscount,
        'mobileUserToken': mobileUserToken,
        'baseUrlPortal': baseUrlPortal,
        'exchangeQrFeeCustomer': exchangeQrFeeCustomer?.map((e) => e.toJson()).toList(),
        'optPushTransaction': optPushTransaction,
        'isUnivestLink': isUnivestLink,
        'listBankQR': listBankQR?.map((e) => e.toJson()).toList(),
        'listInternationalQR': listInternationalQR?.map((e) => e.toJson()).toList(),
        'username': username,
        'menuHome': menuHome?.map((e) => e.toJson()).toList(),
        'buttonApps': buttonApps?.map((e) => e.toJson()).toList(),
        'versionBinLocal': versionBinLocal,
        'allowDeposit': allowDeposit,
        'skipSignature': skipSignature,
        'isAutoPay': isAutoPay,
        'isConfirmPassword': isConfirmPassword,
        'isMacqFlow': isMacqFlow,
        'mores': mores?.toJson(),
        'isFirmwareUpdate': isFirmwareUpdate,
        'userType': userType,
        'minAmount': minAmount,
        'merchantId': merchantId,
        'isSaleService': isSaleService,
        'isDisableCheckGps': isDisableCheckGps,
        'mvisaTid': mvisaTid,
        'mvisaMid': mvisaMid,
        'mvisaMidMaster': mvisaMidMaster,
        'mcc': mcc,
        'listQr': listQr?.map((e) => e.toJson()).toList(),
        'isEnableVetcService': isEnableVetcService,
        'mobileUserPhone': mobileUserPhone,
        'check': check,
        'error': error?.toJson(),
        'lastTimeCache': this.lastTimeCache,
        'pass': this.pass,
        'muid': this.muid,
      };

  bool isRunMacq(){
    return isMacqFlow == 1;
  }
}

class MPConfig {
  final String? secretKey;
  final String? merchantName;
  final String? logoUrl;
  final int? connectType;
  final int? isShowNormalPayment;
  final int? isShowInstallmentPayment;
  final int? isShowNew;
  final int? permitVoidSocket;
  final int? permitPayGiftCard;
  final int? appPresaleCache;
  final int? isShowChangePassword;
  final int? isShowTransactionHistory;
  final int? permitQrNl;

  MPConfig({
    this.secretKey,
    this.merchantName,
    this.logoUrl,
    this.connectType,
    this.isShowNormalPayment,
    this.isShowInstallmentPayment,
    this.isShowNew,
    this.permitVoidSocket,
    this.permitPayGiftCard,
    this.appPresaleCache,
    this.isShowChangePassword,
    this.isShowTransactionHistory,
    this.permitQrNl,
  });

  MPConfig.fromJson(Map<String, dynamic> json)
      : secretKey = json['secretKey'] as String?,
        merchantName = json['merchantName'] as String?,
        logoUrl = json['logoUrl'] as String?,
        connectType = json['connectType'] as int?,
        isShowNormalPayment = json['isShowNormalPayment'] as int?,
        isShowInstallmentPayment = json['isShowInstallmentPayment'] as int?,
        isShowNew = json['isShowNew'] as int?,
        permitVoidSocket = json['permitVoidSocket'] as int?,
        permitPayGiftCard = json['permitPayGiftCard'] as int?,
        appPresaleCache = json['appPresaleCache'] as int?,
        isShowChangePassword = json['isShowChangePassword'] as int?,
        isShowTransactionHistory = json['isShowTransactionHistory'] as int?,
        permitQrNl = json['permitQrNl'] as int?;

  Map<String, dynamic> toJson() => {
        'secretKey': secretKey,
        'merchantName': merchantName,
        'logoUrl': logoUrl,
        'connectType': connectType,
        'isShowNormalPayment': isShowNormalPayment,
        'isShowInstallmentPayment': isShowInstallmentPayment,
        'isShowNew': isShowNew,
        'permitVoidSocket': permitVoidSocket,
        'permitPayGiftCard': permitPayGiftCard,
        'appPresaleCache': appPresaleCache,
        'isShowChangePassword': isShowChangePassword,
        'isShowTransactionHistory': isShowTransactionHistory,
        'permitQrNl': permitQrNl
      };
}

class MPInstallmentInfo {
  final String? bankId;
  final String? bankName;
  final String? bankLongName;
  final String? logo;
  final String? idCardNumber;
  final int? minAmount;
  final int? maxAmount;
  final String? policy;
  final List<MPItemPeriod>? listPeriod;
  final List<String>? binList;
  final int? binNumberDigit;
  final String? compulsoryCheck;

  MPInstallmentInfo({
    this.bankId,
    this.bankName,
    this.bankLongName,
    this.logo,
    this.idCardNumber,
    this.minAmount,
    this.maxAmount,
    this.policy,
    this.listPeriod,
    this.binList,
    this.binNumberDigit,
    this.compulsoryCheck,
  });

  MPInstallmentInfo.fromJson(Map<String, dynamic> json)
      : bankId = json['bankId'].toString(),
        bankName = json['bankName'] as String?,
        bankLongName = json['bankLongName'] as String?,
        logo = json['logo'] as String?,
        idCardNumber = json['idCardNumber'] as String?,
        minAmount = int.tryParse(json['minAmount'].toString()),
        maxAmount = int.tryParse(json['maxAmount'].toString()),
        policy = json['policy'] as String?,
        listPeriod = (json['listPeriod'] as List?)
            ?.map((dynamic e) => MPItemPeriod.fromJson(e as Map<String, dynamic>))
            .toList(),
        binList = (json['binList'] as List?)?.map((dynamic e) => e as String).toList(),
        binNumberDigit = json['binNumberDigit'] as int?,
        compulsoryCheck = json['compulsoryCheck'] as String?;

  Map<String, dynamic> toJson() => {
        'bankId': bankId,
        'bankName': bankName,
        'bankLongName': bankLongName,
        'logo': logo,
        'idCardNumber': idCardNumber,
        'minAmount': minAmount,
        'maxAmount': maxAmount,
        'policy': policy,
        'listPeriod': listPeriod?.map((e) => e.toJson()).toList(),
        'binList': binList,
        'binNumberDigit': binNumberDigit,
        'compulsoryCheck': compulsoryCheck
      };
}

class MPItemPeriod {
  final String? installmentOutId;
  final String? period;
  final String? periodType;
  final num? rate;
  final num? linkCardRate;

  MPItemPeriod({
    this.installmentOutId,
    this.period,
    this.periodType,
    this.rate,
    this.linkCardRate,
  });

  MPItemPeriod.fromJson(Map<String, dynamic> json)
      : installmentOutId = json['installmentOutId'].toString(),
        period = json['period'] as String?,
        periodType = json['periodType'] as String?,
        rate = json['rate'] as num?,
        linkCardRate = json['linkCardRate'] as num?;

  Map<String, dynamic> toJson() => {
        'installmentOutId': installmentOutId,
        'period': period,
        'periodType': periodType,
        'rate': rate,
        'linkCardRate': linkCardRate
      };
}

class MPInstallmentVaymuonInfo {
  final String? bankId;
  final String? bankName;
  final String? bankLongName;
  final String? logo;
  final String? idCardNumber;
  final int? minAmount;
  final int? maxAmount;
  final List<MPItemPeriod>? listPeriod;
  final String? compulsoryCheck;

  MPInstallmentVaymuonInfo({
    this.bankId,
    this.bankName,
    this.bankLongName,
    this.logo,
    this.idCardNumber,
    this.minAmount,
    this.maxAmount,
    this.listPeriod,
    this.compulsoryCheck,
  });

  MPInstallmentVaymuonInfo.fromJson(Map<String, dynamic> json)
      : bankId = json['bankId'].toString(),
        bankName = json['bankName'] as String?,
        bankLongName = json['bankLongName'] as String?,
        logo = json['logo'] as String?,
        idCardNumber = json['idCardNumber'] as String?,
        minAmount = int.tryParse(json['minAmount'].toString()),
        maxAmount = int.tryParse(json['maxAmount'].toString()),
        listPeriod = (json['listPeriod'] as List?)
            ?.map((dynamic e) => MPItemPeriod.fromJson(e as Map<String, dynamic>))
            .toList(),
        compulsoryCheck = json['compulsoryCheck'] as String?;

  Map<String, dynamic> toJson() => {
        'bankId': bankId,
        'bankName': bankName,
        'bankLongName': bankLongName,
        'logo': logo,
        'idCardNumber': idCardNumber,
        'minAmount': minAmount,
        'maxAmount': maxAmount,
        'listPeriod': listPeriod?.map((e) => e.toJson()).toList(),
        'compulsoryCheck': compulsoryCheck
      };
}

class MPExchangeInfo {
  final String? issuerCode;
  final String? issuerName;
  final num? fee;
  final String? paymentMethod;

  MPExchangeInfo({
    this.issuerCode,
    this.issuerName,
    this.fee,
    this.paymentMethod,
  });

  MPExchangeInfo.fromJson(Map<String, dynamic> json)
      : issuerCode = json['issuerCode'] as String?,
        issuerName = json['issuerName'] as String?,
        fee = json['fee'] as num?,
        paymentMethod = json['paymentMethod'] as String?;

  Map<String, dynamic> toJson() =>
      {'issuerCode': issuerCode, 'issuerName': issuerName, 'fee': fee, 'paymentMethod': paymentMethod};
}

class MPExchangeQrInfo {
  final String? issuerCode;
  final String? issuerName;
  final num? fee;
  final String? paymentMethod;

  MPExchangeQrInfo({
    this.issuerCode,
    this.issuerName,
    this.fee,
    this.paymentMethod,
  });

  MPExchangeQrInfo.fromJson(Map<String, dynamic> json)
      : issuerCode = json['issuerCode'] as String?,
        issuerName = json['issuerName'] as String?,
        fee = json['fee'] as num?,
        paymentMethod = json['paymentMethod'] as String?;

  Map<String, dynamic> toJson() =>
      {'issuerCode': issuerCode, 'issuerName': issuerName, 'fee': fee, 'paymentMethod': paymentMethod};
}

class MPExchangeLinkCardInfo {
  final String? issuerCode;
  final String? issuerName;
  final num? fee;
  final String? paymentMethod;

  MPExchangeLinkCardInfo({
    this.issuerCode,
    this.issuerName,
    this.fee,
    this.paymentMethod,
  });

  MPExchangeLinkCardInfo.fromJson(Map<String, dynamic> json)
      : issuerCode = json['issuerCode'] as String?,
        issuerName = json['issuerName'] as String?,
        fee = json['fee'] as num?,
        paymentMethod = json['paymentMethod'] as String?;

  Map<String, dynamic> toJson() =>
      {'issuerCode': issuerCode, 'issuerName': issuerName, 'fee': fee, 'paymentMethod': paymentMethod};
}

class MPExchangeVaymuonInfo {
  String? issuerCode;
  String? issuerName;
  double? fee;
  String? paymentMethod;

  MPExchangeVaymuonInfo({this.issuerCode, this.issuerName, this.fee, this.paymentMethod});

  MPExchangeVaymuonInfo.fromJson(Map<String, dynamic> json) {
    issuerCode = json['issuerCode'];
    issuerName = json['issuerName'];
    fee = double.parse(json['fee'].toString());
    paymentMethod = json['paymentMethod'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};
    data['issuerCode'] = issuerCode;
    data['issuerName'] = issuerName;
    data['fee'] = fee;
    data['paymentMethod'] = paymentMethod;
    return data;
  }
}

class MPQuickWithdrawInfo {
  final String? flatFee;
  final num? percentageFee;
  final String? amountMin;
  final String? amountMax;
  final List<MPJsonQuickWithdrawList>? jsonQuickWithdrawList;

  MPQuickWithdrawInfo({
    this.flatFee,
    this.percentageFee,
    this.amountMin,
    this.amountMax,
    this.jsonQuickWithdrawList,
  });

  MPQuickWithdrawInfo.fromJson(Map<String, dynamic> json)
      : flatFee = json['flatFee'].toString(),
        percentageFee = json['percentageFee'] as num?,
        amountMin = json['amountMin'].toString(),
        amountMax = json['amountMax'].toString(),
        jsonQuickWithdrawList = (json['jsonQuickWithdrawList'] as List?)
            ?.map((dynamic e) => MPJsonQuickWithdrawList.fromJson(e as Map<String, dynamic>))
            .toList();

  Map<String, dynamic> toJson() => {
        'flatFee': flatFee,
        'percentageFee': percentageFee,
        'amountMin': amountMin,
        'amountMax': amountMax,
        'jsonQuickWithdrawList': jsonQuickWithdrawList?.map((e) => e.toJson()).toList()
      };
}

class MPJsonQuickWithdrawList {
  final String? flatFee;
  final num? percentageFee;
  final String? amountMin;
  final String? amountMax;

  MPJsonQuickWithdrawList({
    this.flatFee,
    this.percentageFee,
    this.amountMin,
    this.amountMax,
  });

  MPJsonQuickWithdrawList.fromJson(Map<String, dynamic> json)
      : flatFee = (json['flatFee'].toString().isNotEmpty && json['flatFee'].toString() != 'null') ? json['flatFee'].toString() : '0',
        percentageFee = json['percentageFee'] as num?,
        amountMin = json['amountMin'].toString(),
        amountMax = json['amountMax'].toString();

  Map<String, dynamic> toJson() =>
      {'flatFee': flatFee, 'percentageFee': percentageFee, 'amountMin': amountMin, 'amountMax': amountMax};
}

class MPBanner {
  final String? uNGVON;
  final String? aCTIVEATM360;

  MPBanner({
    this.uNGVON,
    this.aCTIVEATM360,
  });

  MPBanner.fromJson(Map<String, dynamic> json)
      : uNGVON = json['UNG_VON'] as String?,
        aCTIVEATM360 = json['ACTIVE_ATM360'] as String?;

  Map<String, dynamic> toJson() => {'UNG_VON': uNGVON, 'ACTIVE_ATM360': aCTIVEATM360};
}

class MPConfigParamRequires {
  final String? serviceCode;
  final String? emailParam;
  final String? mobileNoParam;
  final String? descriptionParam;
  final int? amountThreshold;

  MPConfigParamRequires({
    this.serviceCode,
    this.emailParam,
    this.mobileNoParam,
    this.descriptionParam,
    this.amountThreshold,
  });

  MPConfigParamRequires.fromJson(Map<String, dynamic> json)
      : serviceCode = json['serviceCode'] as String?,
        emailParam = json['emailParam'] as String?,
        mobileNoParam = json['mobileNoParam'] as String?,
        descriptionParam = json['descriptionParam'] as String?,
        amountThreshold = json['amountThreshold'] as int?;

  Map<String, dynamic> toJson() => {
        'serviceCode': serviceCode,
        'emailParam': emailParam,
        'mobileNoParam': mobileNoParam,
        'descriptionParam': descriptionParam,
        'amountThreshold': amountThreshold
      };
}

class MPItemBanner {
  final String? linkBanner;
  final String? linkWebview;

  MPItemBanner({
    this.linkBanner,
    this.linkWebview,
  });

  MPItemBanner.fromJson(Map<String, dynamic> json)
      : linkBanner = json['linkBanner'] as String?,
        linkWebview = json['linkWebview'] as String?;

  Map<String, dynamic> toJson() => {'linkBanner': linkBanner, 'linkWebview': linkWebview};
}

class MPExchangeQrFeeCustomer {
  final String? issuerCode;
  final String? issuerName;
  final num? fee;
  final String? paymentMethod;

  MPExchangeQrFeeCustomer({
    this.issuerCode,
    this.issuerName,
    this.fee,
    this.paymentMethod,
  });

  MPExchangeQrFeeCustomer.fromJson(Map<String, dynamic> json)
      : issuerCode = json['issuerCode'] as String?,
        issuerName = json['issuerName'] as String?,
        fee = json['fee'] as num?,
        paymentMethod = json['paymentMethod'] as String?;

  Map<String, dynamic> toJson() =>
      {'issuerCode': issuerCode, 'issuerName': issuerName, 'fee': fee, 'paymentMethod': paymentMethod};
}

class MPBankQR {
  final String? shortName;
  final String? longName;
  final String? logo;
  final String? description;
  final String? descriptionEn;
  final String? longNameEn;
  final List<MPQrChild>? qrChildren;
  final int? menuFk;
  final num? feeMinAmount;
  final int? flatFee;
  final String? type;

  MPBankQR({
    this.shortName,
    this.longName,
    this.logo,
    this.description,
    this.descriptionEn,
    this.longNameEn,
    this.qrChildren,
    this.menuFk,
    this.feeMinAmount,
    this.flatFee,
    this.type,
  });

  MPBankQR.fromJson(Map<String, dynamic> json)
      : shortName = json['shortName'] as String?,
        longName = json['longName'] as String?,
        logo = json['logo'] as String?,
        description = json['description'] as String?,
        descriptionEn = json['descriptionEn'] as String?,
        longNameEn = json['longNameEn'] as String?,
        qrChildren =
            (json['qrChildren'] as List?)?.map((dynamic e) => MPQrChild.fromJson(e as Map<String, dynamic>)).toList(),
        menuFk = json['menuFk'] as int?,
        feeMinAmount = json['feeMinAmount'] as num?,
        flatFee = json['flatFee'] as int?,
        type = json['type'] as String?;

  Map<String, dynamic> toJson() => {
        'shortName': shortName,
        'longName': longName,
        'logo': logo,
        'description': description,
        'descriptionEn': descriptionEn,
        'longNameEn': longNameEn,
        'qrChildren': qrChildren?.map((e) => e.toJson()).toList(),
        'menuFk': menuFk,
        'feeMinAmount': feeMinAmount,
        'flatFee': flatFee,
        'type': type
      };
}

class MPInternationalQR {
  final String? shortName;
  final String? longName;
  final String? logo;
  final String? description;
  final String? descriptionEn;
  final String? longNameEn;
  final List<MPQrChild>? qrChildren;
  final int? menuFk;

  MPInternationalQR({
    this.shortName,
    this.longName,
    this.logo,
    this.description,
    this.descriptionEn,
    this.longNameEn,
    this.qrChildren,
    this.menuFk,
  });

  MPInternationalQR.fromJson(Map<String, dynamic> json)
      : shortName = json['shortName'] as String?,
        longName = json['longName'] as String?,
        logo = json['logo'] as String?,
        description = json['description'] as String?,
        descriptionEn = json['descriptionEn'] as String?,
        longNameEn = json['longNameEn'] as String?,
        qrChildren =
            (json['qrChildren'] as List?)?.map((dynamic e) => MPQrChild.fromJson(e as Map<String, dynamic>)).toList(),
        menuFk = json['menuFk'] as int?;

  Map<String, dynamic> toJson() => {
        'shortName': shortName,
        'longName': longName,
        'logo': logo,
        'description': description,
        'descriptionEn': descriptionEn,
        'longNameEn': longNameEn,
        'qrChildren': qrChildren?.map((e) => e.toJson()).toList(),
        'menuFk': menuFk
      };
}

class MPQrChild {
  final String? shortNameChild;
  final String? longNameChild;
  final String? logoChild;
  final String? faviconChild;
  final double? fee;
  final String? qrType;
  final String? description;
  final String? descriptionEn;
  final String? userManualLink;
  final String? amountMin;
  final num? feeMinAmount;
  final int? flatFee;
  final int? checkAllowCurrency;

  MPQrChild({
    this.shortNameChild,
    this.longNameChild,
    this.logoChild,
    this.faviconChild,
    this.fee,
    this.qrType,
    this.description,
    this.descriptionEn,
    this.userManualLink,
    this.amountMin,
    this.feeMinAmount,
    this.flatFee,
    this.checkAllowCurrency,
  });

  MPQrChild.fromJson(Map<String, dynamic> json)
      : shortNameChild = json['shortNameChild'] as String?,
        longNameChild = json['longNameChild'] as String?,
        logoChild = json['logoChild'] as String?,
        faviconChild = json['faviconChild'] as String?,
        // fee = (json['fee'] != null) ? double.parse(json['fee'].toString()) : 0.0,
        fee = checkIsNullOrEmptyToString(json['fee']).isNotEmpty ? double.parse(json['fee'].toString()) : 0.0,
        qrType = json['qrType'] as String?,
        description = json['description'] as String?,
        descriptionEn = json['descriptionEn'] as String?,
        userManualLink = json['userManualLink'] as String?,
        amountMin = json['amountMin'].toString(),
        feeMinAmount = json['feeMinAmount'] as num?,
        flatFee = json['flatFee'] as int?,
        checkAllowCurrency = json['checkAllowCurrency'] as int?;

  Map<String, dynamic> toJson() => {
        'shortNameChild': shortNameChild,
        'longNameChild': longNameChild,
        'logoChild': logoChild,
        'faviconChild': faviconChild,
        'fee': fee,
        'qrType': qrType,
        'description': description,
        'descriptionEn': descriptionEn,
        'userManualLink': userManualLink,
        'amountMin': amountMin,
        'feeMinAmount': feeMinAmount,
        'flatFee': flatFee,
        'checkAllowCurrency': checkAllowCurrency
      };
}

class MPItemMenuHome {
  final String? title;
  final String? titleEn;
  final String? iconLink;
  final String? logo;
  final String? position;
  final String? status;
  final String? serviceCode;
  final String? code;
  final String? menuType;
  final int? id;
  final int? rownum;
  final int? version;
  final String? subName;
  final String? subNameEn;

  MPItemMenuHome({
    this.title,
    this.titleEn,
    this.iconLink,
    this.logo,
    this.position,
    this.status,
    this.serviceCode,
    this.code,
    this.menuType,
    this.id,
    this.rownum,
    this.version,
    this.subName,
    this.subNameEn,
  });

  MPItemMenuHome.fromJson(Map<String, dynamic> json)
      : title = json['title'] as String?,
        titleEn = json['titleEn'] as String?,
        iconLink = json['iconLink'] as String?,
        logo = json['logo'] as String?,
        position = json['position'].toString(),
        status = json['status'] as String?,
        serviceCode = json['serviceCode'] as String?,
        code = json['code'] as String?,
        menuType = json['menuType'] as String?,
        id = json['id'] as int?,
        rownum = json['rownum'] as int?,
        version = json['version'] as int?,
        subName = json['subName'] ?? '',
        subNameEn = json['subNameEn'] ?? '';

  Map<String, dynamic> toJson() => {
        'title': title,
        'titleEn': titleEn,
        'iconLink': iconLink,
        'logo': logo,
        'position': position,
        'status': status,
        'serviceCode': serviceCode,
        'code': code,
        'menuType': menuType,
        'id': id,
        'rownum': rownum,
        'version': version,
        'subName': subName,
        'subNameEn': subNameEn,
      };
}

class MPItemQr {
  final String? qrType;
  final String? qrName;
  final String? description;
  final String? descriptionEn;
  final String? imgPartner;
  final String? imgLogo;
  final String? checkStatus;

  MPItemQr({
    this.qrType,
    this.qrName,
    this.description,
    this.descriptionEn,
    this.imgPartner,
    this.imgLogo,
    this.checkStatus,
  });

  MPItemQr.fromJson(Map<String, dynamic> json)
      : qrType = json['qrType'] as String?,
        qrName = json['qrName'] as String?,
        description = json['description'] as String?,
        descriptionEn = json['descriptionEn'] as String?,
        imgPartner = json['imgPartner'] as String?,
        imgLogo = json['imgLogo'] as String?,
        checkStatus = json['checkStatus'] as String?;

  Map<String, dynamic> toJson() => {
        'qrType': qrType,
        'qrName': qrName,
        'description': description,
        'descriptionEn': descriptionEn,
        'imgPartner': imgPartner,
        'imgLogo': imgLogo,
        'checkStatus': checkStatus
      };
}

class MPMores {
  final String? tgVer;
  final String? smeFee;
  String? emqxUsername;
  String? emqxPass;
  String? emqxUrl;

  MPMores({
    this.tgVer,
    this.smeFee,
    this.emqxUsername,
    this.emqxPass,
    this.emqxUrl,
  });


  MPMores.fromJson(Map<String, dynamic> json) :
        tgVer = json['tgVer'] as String?,
        smeFee = json['smeFee'] ?? '0',
        emqxUsername = json['emqxUsername'] as String?,
        emqxPass = json['emqxPass'] as String?,
        emqxUrl = json['emqxUrl'] as String?
  ;

  Map<String, dynamic> toJson() => {
    'tgVer': tgVer,
    'smeFee': smeFee,
    'emqxUsername': emqxUsername,
    'emqxPass': emqxPass,
    'emqxUrl': emqxUrl,
  };
}

class MPError {
  final int? code;
  final String? message;
  final String? messageEn;

  MPError({
    this.code,
    this.message,
    this.messageEn,
  });

  MPError.fromJson(Map<String, dynamic> json)
      : code = json['code'] as int?,
        message = json['message'] as String?,
        messageEn = json['messageEn'] as String?;

  Map<String, dynamic> toJson() => {'code': code, 'message': message, 'messageEn': messageEn};
}
