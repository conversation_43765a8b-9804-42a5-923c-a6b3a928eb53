class QrDataConfig {
  List<Data>? data;

  QrDataConfig({this.data});

  QrDataConfig.fromJson(Map<String, dynamic> json) {
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(new Data.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Data {
  String? groupName;
  String? shortName;
  String? logo;
  String? longName;
  String? longNameEn;
  String? description;
  String? descriptionEn;
  List<QrChildren>? qrChildren;
  int? feeMinAmount;

  Data(
      {this.groupName,
        this.shortName,
        this.logo,
        this.longName,
        this.longNameEn,
        this.description,
        this.descriptionEn,
        this.qrChildren,
        this.feeMinAmount});

  Data.fromJson(Map<String, dynamic> json) {
    groupName = json['groupName'];
    shortName = json['shortName'];
    logo = json['logo'];
    longName = json['longName'];
    longNameEn = json['longNameEn'];
    description = json['description'];
    descriptionEn = json['descriptionEn'];
    if (json['qrChildren'] != null) {
      qrChildren = <QrChildren>[];
      json['qrChildren'].forEach((v) {
        qrChildren!.add(new QrChildren.fromJson(v));
      });
    }
    feeMinAmount = json['feeMinAmount'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['groupName'] = this.groupName;
    data['shortName'] = this.shortName;
    data['logo'] = this.logo;
    data['longName'] = this.longName;
    data['longNameEn'] = this.longNameEn;
    data['description'] = this.description;
    data['descriptionEn'] = this.descriptionEn;
    if (this.qrChildren != null) {
      data['qrChildren'] = this.qrChildren!.map((v) => v.toJson()).toList();
    }
    data['feeMinAmount'] = this.feeMinAmount;
    return data;
  }
}

class QrChildren {
  final String? shortNameChild;
  final String? longNameChild;
  final String? qrType;
  final String? description;
  final String? descriptionEn;
  final String? userManualLink;
  final String? groupName;
  final String? logoChild;
  final String? amountMin;
  final int? checkAllowCurrency;

  QrChildren(
      {this.shortNameChild,
        this.longNameChild,
        this.qrType,
        this.description,
        this.descriptionEn,
        this.userManualLink,
        this.groupName,
        this.logoChild,
        this.amountMin,
        this.checkAllowCurrency});

  QrChildren.fromJson(Map<String, dynamic> json) :
  shortNameChild = json['shortNameChild'],
    longNameChild = json['longNameChild'],
    qrType = json['qrType'],
    description = json['description'],
    descriptionEn = json['descriptionEn'],
    userManualLink = json['userManualLink'],
    groupName = json['groupName'],
    logoChild = json['logoChild'],
    amountMin = json['amountMin'] ?? "",
    checkAllowCurrency = json['checkAllowCurrency'];


  Map<String, dynamic> toJson() => {
    'shortNameChild': this.shortNameChild,
    'longNameChild': this.longNameChild,
    'qrType': this.qrType,
    'description': this.description,
    'descriptionEn': this.descriptionEn,
    'userManualLink': this.userManualLink,
    'groupName': this.groupName,
    'logoChild': this.logoChild,
    'amountMin': this.amountMin,
    'checkAllowCurrency': this.checkAllowCurrency,
  };
}