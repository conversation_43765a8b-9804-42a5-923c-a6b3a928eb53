import 'dart:ui';

import 'package:mposxs/app/util/constants.dart';

class TypeQrPayment{
  final TypeQr? typeQr;
  final String? nameQR;
  final String? desception;
  final Color? color;

  TypeQrPayment({this.typeQr, this.nameQR, this.desception, this.color});

  TypeQrPayment.fromJson(Map<String, dynamic> json) :
    typeQr = json['typeQr'],
    nameQR= json['nameQR'],
    desception   = json['desception'],
    color   = json['color'];

  Map<String, dynamic> toJson() => {
  'typeQr': this.typeQr,
  'nameQR': this.nameQR,
  'desception': this.desception,
  'color': this.color,
  };
}