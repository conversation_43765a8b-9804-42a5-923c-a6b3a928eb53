
import 'package:flutter/material.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/constants.dart';

class TypePayment{
  TypePay? typePay;
  int? position;
  String? title;
  String? sub_name;
  String? sub_nameEn;
  Color? bgColor;
  TextStyle? textStyle;
  String? logo;
  String? codeBNPLItem;
  String? amountMin;

  TypePayment({this.sub_name,this.sub_nameEn, this.textStyle, this.typePay, this.position, this.title, this.bgColor, this.logo, this.codeBNPLItem, this.amountMin});

  TypePayment.fromJson(Map<String, dynamic> json) {
    typePay = convertStringToTypePay(json['typePay']);
    position= json['position'];
    title   = json['title'];
    sub_name   = json['sub_name'];
    sub_nameEn   = json['sub_nameEn'];
    bgColor = Color(json['bgColor']);
    textStyle = json['textStyle'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['typePay']   = this.typePay!.name;
    data['position']  = this.position;
    data['title']     = this.title;
    data['sub_name']     = this.sub_name;
    data['sub_nameEn']     = this.sub_nameEn;
    data['bgColor']   = this.bgColor!.value;
    data['textStyle']   = this.textStyle;
    AppUtils.log('TypePayment toJson ${data.toString()}');
    return data;
  }

  convertStringToTypePay(String? typePay) {
    AppUtils.log('typePay=$typePay -> ${TypePay.QR_PAY.name}');
    if (typePay == TypePay.QR_PAY.name) {
      return TypePay.QR_PAY;
    }
    else if (typePay == TypePay.CREATE_LINK.name) {
      return TypePay.CREATE_LINK;
    }
    else if (typePay == TypePay.INSTALLMENT.name) {
      return TypePay.INSTALLMENT;
    }
    else if (typePay == TypePay.MOTO.name) {
      return TypePay.MOTO;
    }
    else if (typePay == TypePay.QR_WALLET.name) {
      return TypePay.QR_WALLET;
    }
    else if (typePay == TypePay.BNPL.name) {
      return TypePay.BNPL;
    }
    else if (typePay == TypePay.CHECK_INSTALLMENT_CARD.name) {
      return TypePay.CHECK_INSTALLMENT_CARD;
    }
    else if (typePay == TypePay.DEPOSIT.name) {
      return TypePay.DEPOSIT;
    }
    else if (typePay == TypePay.MANUAL.name) {
      return TypePay.MANUAL;
    }
    else {
      return TypePay.SWIPE_CARD;
    }
  }
}