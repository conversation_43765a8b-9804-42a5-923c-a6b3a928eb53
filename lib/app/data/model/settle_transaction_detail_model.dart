class SettleTransactionDetailModel {
  bool? caPublicKeyExpired;
  bool? checkPANOnly;
  int? emvProcessOfflineResult;
  bool? emvRestrictInternationCard;
  bool? isNFC;
  bool? offlinePIN;
  String? platform;
  String? readerSerialNo;
  int? receiptWidth;
  bool? restrictInternationCard;
  String? serviceName;
  String? sessionKey;
  int? settlementBatchId;
  String? tokenL2;
  int? transactionDate;
  TransactionDetail? transactionDetail;
  String? transactionID;
  String? udid;
  String? userID;
  String? versionNo;

  SettleTransactionDetailModel(
      {this.caPublicKeyExpired,
      this.checkPANOnly,
      this.emvProcessOfflineResult,
      this.emvRestrictInternationCard,
      this.isNFC,
      this.offlinePIN,
      this.platform,
      this.readerSerialNo,
      this.receiptWidth,
      this.restrictInternationCard,
      this.serviceName,
      this.sessionKey,
      this.settlementBatchId,
      this.tokenL2,
      this.transactionDate,
      this.transactionDetail,
      this.transactionID,
      this.udid,
      this.userID,
      this.versionNo});

  SettleTransactionDetailModel.fromJson(Map<String, dynamic> json) {
    caPublicKeyExpired = json['caPublicKeyExpired'];
    checkPANOnly = json['checkPANOnly'];
    emvProcessOfflineResult = json['emvProcessOfflineResult'];
    emvRestrictInternationCard = json['emvRestrictInternationCard'];
    isNFC = json['isNFC'];
    offlinePIN = json['offlinePIN'];
    platform = json['platform'];
    readerSerialNo = json['readerSerialNo'];
    receiptWidth = json['receiptWidth'];
    restrictInternationCard = json['restrictInternationCard'];
    serviceName = json['serviceName'];
    sessionKey = json['sessionKey'];
    settlementBatchId = json['settlementBatchId'];
    tokenL2 = json['tokenL2'];
    transactionDate = json['transactionDate'];
    transactionDetail =
        json['transactionDetail'] != null ? new TransactionDetail.fromJson(json['transactionDetail']) : null;
    transactionID = json['transactionID'];
    udid = json['udid'];
    userID = json['userID'];
    versionNo = json['versionNo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['caPublicKeyExpired'] = this.caPublicKeyExpired;
    data['checkPANOnly'] = this.checkPANOnly;
    data['emvProcessOfflineResult'] = this.emvProcessOfflineResult;
    data['emvRestrictInternationCard'] = this.emvRestrictInternationCard;
    data['isNFC'] = this.isNFC;
    data['offlinePIN'] = this.offlinePIN;
    data['platform'] = this.platform;
    data['readerSerialNo'] = this.readerSerialNo;
    data['receiptWidth'] = this.receiptWidth;
    data['restrictInternationCard'] = this.restrictInternationCard;
    data['serviceName'] = this.serviceName;
    data['sessionKey'] = this.sessionKey;
    data['settlementBatchId'] = this.settlementBatchId;
    data['tokenL2'] = this.tokenL2;
    data['transactionDate'] = this.transactionDate;
    if (this.transactionDetail != null) {
      data['transactionDetail'] = this.transactionDetail!.toJson();
    }
    data['transactionID'] = this.transactionID;
    data['udid'] = this.udid;
    data['userID'] = this.userID;
    data['versionNo'] = this.versionNo;
    return data;
  }
}

class TransactionDetail {
  String? amountAuthorized;
  Application? application;
  String? applicationLabel;
  String? approvalCode;
  String? batchNo;
  String? cardHolderName;
  int? cardType;
  String? invoiceNumber;
  String? maskedPAN;
  String? rREFNo;
  String? traceNo;
  String? transactionCert;
  int? transactionDate;
  String? transactionID;
  int? transactionStatus;
  String? transactionTime;
  int? trxType;
  String? udid;
  String? wfId;
  String? itemDescription;

  TransactionDetail(
      {this.amountAuthorized,
      this.application,
      this.applicationLabel,
      this.approvalCode,
      this.batchNo,
      this.cardHolderName,
      this.cardType,
      this.invoiceNumber,
      this.maskedPAN,
      this.rREFNo,
      this.traceNo,
      this.transactionCert,
      this.transactionDate,
      this.transactionID,
      this.transactionStatus,
      this.transactionTime,
      this.trxType,
      this.itemDescription,
      this.wfId,
      this.udid});

  TransactionDetail.fromJson(Map<String, dynamic> json) {
    amountAuthorized = json['amountAuthorized'];
    application = json['application'] != null ? new Application.fromJson(json['application']) : null;
    applicationLabel = json['applicationLabel'];
    approvalCode = json['approvalCode'];
    batchNo = json['batchNo'];
    cardHolderName = json['cardHolderName'];
    cardType = json['cardType'];
    invoiceNumber = json['invoiceNumber'];
    maskedPAN = json['maskedPAN'];
    rREFNo = json['RREFNo'];
    traceNo = json['traceNo'];
    transactionCert = json['transactionCert'];
    transactionDate = json['transactionDate'];
    transactionID = json['transactionID'];
    transactionStatus = json['transactionStatus'];
    transactionTime = json['transactionTime'];
    trxType = json['trxType'];
    itemDescription = json['itemDescription'];
    wfId = json['wfId'];
    udid = json['udid'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['amountAuthorized'] = this.amountAuthorized;
    if (this.application != null) {
      data['application'] = this.application!.toJson();
    }
    data['applicationLabel'] = this.applicationLabel;
    data['approvalCode'] = this.approvalCode;
    data['batchNo'] = this.batchNo;
    data['cardHolderName'] = this.cardHolderName;
    data['cardType'] = this.cardType;
    data['invoiceNumber'] = this.invoiceNumber;
    data['maskedPAN'] = this.maskedPAN;
    data['RREFNo'] = this.rREFNo;
    data['traceNo'] = this.traceNo;
    data['transactionCert'] = this.transactionCert;
    data['transactionDate'] = this.transactionDate;
    data['transactionID'] = this.transactionID;
    data['transactionStatus'] = this.transactionStatus;
    data['transactionTime'] = this.transactionTime;
    data['trxType'] = this.trxType;
    data['udid'] = this.udid;
    data['wfId'] = this.wfId;
    data['itemDescription'] = this.itemDescription;
    return data;
  }
}

class Application {
  String? applicationName;
  String? mID;
  String? tID;

  Application({this.applicationName, this.mID, this.tID});

  Application.fromJson(Map<String, dynamic> json) {
    applicationName = json['applicationName'];
    mID = json['MID'];
    tID = json['TID'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['applicationName'] = this.applicationName;
    data['MID'] = this.mID;
    data['TID'] = this.tID;
    return data;
  }
}
