import 'package:mposxs/app/data/model/base_response.dart';

class GroupDataByTimeTransactionHistoryModel {
  String? date;
  List<Data>? data;

  GroupDataByTimeTransactionHistoryModel({this.date, this.data});

  GroupDataByTimeTransactionHistoryModel.fromJson(Map<String, dynamic> json) {
    date = json['date'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(new Data.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['date'] = this.date;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class TransactionHistoryModel implements BaseResponseData {
  int? pageIndex;
  int? pageSize;
  int? pageCount;
  int? dataCount;
  List<Data>? data;

  TransactionHistoryModel({this.pageIndex, this.pageSize, this.pageCount, this.dataCount, this.data});

  TransactionHistoryModel.fromJson(Map<String, dynamic> json) {
    pageIndex = json['pageIndex'];
    pageSize = json['pageSize'];
    pageCount = json['pageCount'];
    dataCount = json['dataCount'];
    if (json['data'] != null) {
      data = <Data>[];
      json['data'].forEach((v) {
        data!.add(new Data.fromJson(v));
      });
    }
  }

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['pageIndex'] = this.pageIndex;
    data['pageSize'] = this.pageSize;
    data['pageCount'] = this.pageCount;
    data['dataCount'] = this.dataCount;
    if (this.data != null) {
      data['data'] = this.data!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class Data {
  int? createdDate;
  String? createdBy;
  int? status;
  String? txid;
  String? cardholderName;
  String? pan;
  String? mid;
  String? tid;
  String? authCode;
  String? rrn;
  String? accquirer;
  int? amount;
  String? issuerCode;
  String? udid;
  TransactionInstallment? transactionInstallment;
  TransactionPush? transactionPush;
  int? sendLoyalty;
  int? sendCRM;
  String? applicationUsageControl;
  TransactionInstallment? transactionRequest;
  TransactionFeedback? transactionFeedback;
  String? transactionType;
  String? transactionPushType;
  String? issuerName;
  String? issuerNameEn;
  int? rownum;
  int? id;
  int? version;
  int? count;
  String? errorCode;
  String? errorMessage;
  String? description;

  Data(
      {this.createdDate,
      this.createdBy,
      this.status,
      this.txid,
      this.cardholderName,
      this.pan,
      this.mid,
      this.tid,
      this.authCode,
      this.rrn,
      this.accquirer,
      this.amount,
      this.issuerCode,
      this.udid,
      this.transactionInstallment,
      this.transactionPush,
      this.sendLoyalty,
      this.sendCRM,
      this.applicationUsageControl,
      this.transactionRequest,
      this.transactionFeedback,
      this.transactionType,
      this.transactionPushType,
      this.issuerName,
      this.issuerNameEn,
      this.rownum,
      this.id,
      this.version,
      this.count,
      this.errorCode,
      this.errorMessage,
      this.description});

  Data.fromJson(Map<String, dynamic> json) {
    createdDate = json['createdDate'];
    createdBy = json['createdBy'];
    status = json['status'];
    txid = json['txid'];
    cardholderName = json['cardholderName'];
    pan = json['pan'];
    mid = json['mid'];
    tid = json['tid'];
    authCode = json['authCode'];
    rrn = json['rrn'];
    accquirer = json['accquirer'];
    amount = json['amount'];
    issuerCode = json['issuerCode'];
    udid = json['udid'];
    transactionInstallment = json['transactionInstallment'] != null
        ? new TransactionInstallment.fromJson(json['transactionInstallment'])
        : null;
    transactionPush = json['transactionPush'] != null ? new TransactionPush.fromJson(json['transactionPush']) : null;
    sendLoyalty = json['sendLoyalty'];
    sendCRM = json['sendCRM'];
    applicationUsageControl = json['applicationUsageControl'];
    transactionRequest =
        json['transactionRequest'] != null ? new TransactionInstallment.fromJson(json['transactionRequest']) : null;
    transactionFeedback =
        json['transactionFeedback'] != null ? new TransactionFeedback.fromJson(json['transactionFeedback']) : null;
    transactionType = json['transactionType'];
    transactionPushType = json['transactionPushType'];
    issuerName = json['issuerName'];
    issuerNameEn = json['issuerNameEn'];
    rownum = json['rownum'];
    id = json['id'];
    version = json['version'];
    count = json['count'];
    errorCode = json['errorCode'];
    errorMessage = json['errorMessage'];
    description = json['description'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['createdDate'] = this.createdDate;
    data['createdBy'] = this.createdBy;
    data['status'] = this.status;
    data['txid'] = this.txid;
    data['cardholderName'] = this.cardholderName;
    data['pan'] = this.pan;
    data['mid'] = this.mid;
    data['tid'] = this.tid;
    data['authCode'] = this.authCode;
    data['rrn'] = this.rrn;
    data['accquirer'] = this.accquirer;
    data['amount'] = this.amount;
    data['issuerCode'] = this.issuerCode;
    data['udid'] = this.udid;
    if (this.transactionInstallment != null) {
      data['transactionInstallment'] = this.transactionInstallment!.toJson();
    }
    if (this.transactionPush != null) {
      data['transactionPush'] = this.transactionPush!.toJson();
    }
    data['sendLoyalty'] = this.sendLoyalty;
    data['sendCRM'] = this.sendCRM;
    data['applicationUsageControl'] = this.applicationUsageControl;
    if (this.transactionRequest != null) {
      data['transactionRequest'] = this.transactionRequest!.toJson();
    }
    if (this.transactionFeedback != null) {
      data['transactionFeedback'] = this.transactionFeedback!.toJson();
    }
    data['transactionType'] = this.transactionType;
    data['transactionPushType'] = this.transactionPushType;
    data['issuerName'] = this.issuerName;
    data['issuerNameEn'] = this.issuerNameEn;
    data['rownum'] = this.rownum;
    data['id'] = this.id;
    data['version'] = this.version;
    data['count'] = this.count;
    data['errorCode'] = this.errorCode;
    data['errorMessage'] = this.errorMessage;
    data['description'] = this.description;
    return data;
  }
}

class TransactionInstallment {
  int? rownum;
  int? id;
  int? version;
  int? count;

  TransactionInstallment({this.rownum, this.id, this.version, this.count});

  TransactionInstallment.fromJson(Map<String, dynamic> json) {
    rownum = json['rownum'];
    id = json['id'];
    version = json['version'];
    count = json['count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['rownum'] = this.rownum;
    data['id'] = this.id;
    data['version'] = this.version;
    data['count'] = this.count;
    return data;
  }
}

class TransactionPush {
  String? type;
  int? rownum;
  int? id;
  int? version;
  int? count;
  String? errorCode;
  String? errorMessage;

  TransactionPush({this.type, this.rownum, this.id, this.version, this.count, this.errorCode, this.errorMessage});

  TransactionPush.fromJson(Map<String, dynamic> json) {
    type = json['type'];
    rownum = json['rownum'];
    id = json['id'];
    version = json['version'];
    count = json['count'];
    errorCode = json['errorCode'];
    errorMessage = json['errorMessage'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['type'] = this.type;
    data['rownum'] = this.rownum;
    data['id'] = this.id;
    data['version'] = this.version;
    data['count'] = this.count;
    data['errorCode'] = this.errorCode;
    data['errorMessage'] = this.errorMessage;
    return data;
  }
}

class TransactionFeedback {
  int? rownum;
  int? version;
  int? count;

  TransactionFeedback({this.rownum, this.version, this.count});

  TransactionFeedback.fromJson(Map<String, dynamic> json) {
    rownum = json['rownum'];
    version = json['version'];
    count = json['count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['rownum'] = this.rownum;
    data['version'] = this.version;
    data['count'] = this.count;
    return data;
  }
}
