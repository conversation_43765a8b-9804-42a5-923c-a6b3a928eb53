class ErrorResponse {
  int? code = 0;
  String? message;

  ErrorResponse({this.code = 0, this.message = ""});

  ErrorResponse.parserData(Map<String, dynamic>? data) {
    message = "";
    code = 0;
    if (data != null) {
      Map<String, dynamic>? error = data['error'];
      if (error != null) {
        code = error['code'];
        message = code != 0 ? "Mã $code - ${data['message']}" : data['message'];
      } else {
        code = data['code'] != null ? data['code'] : 0;
        message = code != 0 ? "Mã $code - ${data['message']}" : data['message'];
      }
      if (code == 200 || code == 1000) {
        code = 1000;
        message = "DO_SERVICE_SUCCESS";
      }
    } else {
      message = "Không lấy được dữ liệu. Vui lòng chuyển đổi kết nối sang mạng ổn định hơn sau đó thử lại.";
    }
  }
}
