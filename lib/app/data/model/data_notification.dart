// class DataNotification {
//   String? _amount;
//   String? _cardholderName;
//   String? _badge;
//   String? _deviceIdentifier;
//   String? _transactionStatus;
//   String? _orderCode;
//   String? _notificationId;
//   String? _body;
//   String? _udid;
//   String? _category;
//   String? _title;
//   String? _message;
//   String? _timePayment;
//   String? _issuer;
//   String? _pan;
//   String? _icon;
//   String? _methodPayment;
//
//   DataNotification(
//       {String? badge,
//         String? amount,
//         String? cardholderName,
//         String? deviceIdentifier,
//         String? transactionStatus,
//         String? orderCode,
//         String? notificationId,
//         String? body,
//         String? udid,
//         String? category,
//         String? title,
//         String? timePayment,
//         String? issuer,
//         String? pan,
//         String? icon,
//         String? message,
//         String? methodPayment}) {
//     if (badge != null) {
//       this._badge = badge;
//     }
//     if (amount != null) {
//       this._amount = amount;
//     }
//     if (cardholderName != null) {
//       this._cardholderName = cardholderName;
//     }
//     if (deviceIdentifier != null) {
//       this._deviceIdentifier = deviceIdentifier;
//     }
//     if (transactionStatus != null) {
//       this._transactionStatus = transactionStatus;
//     }
//     if (orderCode != null) {
//       this._orderCode = orderCode;
//     }
//     if (notificationId != null) {
//       this._notificationId = notificationId;
//     }
//     if (body != null) {
//       this._body = body;
//     }
//     if (udid != null) {
//       this._udid = udid;
//     }
//     if (category != null) {
//       this._category = category;
//     }
//     if (title != null) {
//       this._title = title;
//     }
//     if (message != null) {
//       this._message = message;
//     }
//     if (message != null) {
//       this._timePayment = timePayment;
//     }
//     if (issuer != null) {
//       this._issuer = issuer;
//     }
//     if (pan != null) {
//       this._pan = pan;
//     }
//     if (icon != null) {
//       this._icon = icon;
//     }
//     if (methodPayment != null) {
//       this._methodPayment = methodPayment;
//     }
//   }
//
//   String? get amount => _amount;
//   set amount(String? amount) => _amount = amount;
//   String? get cardholderName => _cardholderName;
//   set cardholderName(String? cardholderName) => _cardholderName = cardholderName;
//   String? get badge => _badge;
//   set badge(String? badge) => _badge = badge;
//   String? get deviceIdentifier => _deviceIdentifier;
//   set deviceIdentifier(String? deviceIdentifier) =>
//       _deviceIdentifier = deviceIdentifier;
//   String? get transactionStatus => _transactionStatus;
//   set transactionStatus(String? transactionStatus) =>
//       _transactionStatus = transactionStatus;
//   String? get orderCode => _orderCode;
//   set orderCode(String? orderCode) => _orderCode = orderCode;
//   String? get notificationId => _notificationId;
//   set notificationId(String? notificationId) =>
//       _notificationId = notificationId;
//   String? get body => _body;
//   set body(String? body) => _body = body;
//   String? get udid => _udid;
//   set udid(String? udid) => _udid = udid;
//   String? get category => _category;
//   set category(String? category) => _category = category;
//   String? get title => _title;
//   set title(String? title) => _title = title;
//   String? get message => _message;
//   set message(String? message) => _message = message;
//   String? get timePayment => _timePayment;
//   set timePayment(String? timePayment) => _timePayment = timePayment;
//   String? get issuer => _issuer;
//   set issuer(String? issuer) => _issuer = issuer;
//   String? get pan => _pan;
//   set pan(String? pan) => _pan = pan;
//   String? get icon => _icon;
//   set icon(String? icon) => _icon = icon;
//   String? get methodPayment => _methodPayment;
//   set methodPayment(String? methodPayment) => _methodPayment = methodPayment;
//
//   DataNotification.fromJson(Map<String, dynamic> json) {
//     _amount = json['amount'];
//     _cardholderName = json['cardholderName'];
//     _badge = json['badge'];
//     _deviceIdentifier = json['deviceIdentifier'];
//     _transactionStatus = json['transactionStatus'];
//     _orderCode = json['orderCode'];
//     _notificationId = json['notificationId'];
//     _body = json['body'];
//     _udid = json['udid'];
//     _category = json['category'];
//     _title = json['title'];
//     _message = json['message'];
//     _timePayment = json['timePayment'];
//     _issuer = json['issuer'];
//     _pan = json['pan'];
//     _icon = json['icon'];
//     _methodPayment = json['methodPayment'] ?? "";
//   }
//
//   Map<String, dynamic> toJson() {
//     final Map<String, dynamic> data = new Map<String, dynamic>();
//     data['amount'] = this._amount;
//     data['cardholderName'] = this._cardholderName;
//     data['badge'] = this._badge;
//     data['deviceIdentifier'] = this._deviceIdentifier;
//     data['transactionStatus'] = this._transactionStatus;
//     data['orderCode'] = this._orderCode;
//     data['notificationId'] = this._notificationId;
//     data['body'] = this._body;
//     data['udid'] = this._udid;
//     data['category'] = this._category;
//     data['title'] = this._title;
//     data['message'] = this._message;
//     data['timePayment'] = this._timePayment;
//     data['issuer'] = this._issuer;
//     data['pan'] = this._pan;
//     data['icon'] = this._icon;
//     data['methodPayment'] = this._methodPayment;
//     return data;
//   }
// }