import 'dart:convert';

class DataNotification {
  String? _notiRequestId;
  String? _badge;
  String? _banner;
  String? _content;
  String? _summary;
  String? _referId;
  String? _notifyId;
  String? _udid;
  String? _groupCode;
  String? _category;
  String? _sub_category;
  Map? _payload;

  DataNotification(
      {String? notiRequestId,
        String? badge,
        String? banner,
        String? content,
        String? summary,
        String? referId,
        String? notifyId,
        String? udid,
        String? groupCode,
        String? category,
        String? sub_category,
        String? payload}) {
    if (notiRequestId != null) {
      this._notiRequestId = notiRequestId;
    }
    if (badge != null) {
      this._badge = badge;
    }
    if (banner != null) {
      this._banner = banner;
    }
    if (content != null) {
      this._content = content;
    }
    if (summary != null) {
      this._summary = summary;
    }
    if (referId != null) {
      this._referId = referId;
    }
    if (notifyId != null) {
      this._notifyId = notifyId;
    }
    if (udid != null) {
      this._udid = udid;
    }
    if (groupCode != null) {
      this._groupCode = groupCode;
    }
    if (category != null) {
      this._category = category;
    }
    if (sub_category != null) {
      this._sub_category = sub_category;
    }
    if (payload != null) {
      this._payload = payload as Map<String, Object>;
    }
  }


  String? get notiRequestId => _notiRequestId;

  set notiRequestId(String? value) {
    _notiRequestId = value;
  }

  String? get badge => _badge;

  set badge(String? value) {
    _badge = value;
  }

  String? get banner => _banner;

  set banner(String? value) {
    _banner = value;
  }

  String? get content => _content;

  set content(String? value) {
    _content = value;
  }

  String? get summary => _summary;

  set summary(String? value) {
    _summary = value;
  }

  String? get referId => _referId;

  set referId(String? value) {
    _referId = value;
  }

  String? get notifyId => _notifyId;

  set notifyId(String? value) {
    _notifyId = value;
  }

  String? get udid => _udid;

  set udid(String? value) {
    _udid = value;
  }

  String? get groupCode => _groupCode;

  set groupCode(String? value) {
    _groupCode = value;
  }

  String? get category => _category;

  set category(String? value) {
    _category = value;
  }

  String? get sub_category => _sub_category;

  set sub_category(String? value) {
    _sub_category = value;
  }

  Map? get payload => _payload;

  set payload(Map? value) {
    _payload = value;
  }



  DataNotification.fromJson(Map<String, dynamic> json) {
    _notiRequestId = json['notiRequestId'];
    _badge = json['badge'];
    _banner = json['banner'];
    _content = json['content'];
    _summary = json['summary'];
    _referId = json['referId'];
    _notifyId = json['notifyId'];
    _udid = json['udid'];
    _groupCode = json['groupCode'];
    _category = json['category'];
    _sub_category = json['sub_category'];
    _payload = jsonDecode(json['payload'] ?? '');
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['notiRequestId'] = this._notiRequestId;
    data['badge'] = this._badge;
    data['banner'] = this._banner;
    data['content'] = this._content;
    data['summary'] = this._summary;
    data['referId'] = this._referId;
    data['notifyId'] = this._notifyId;
    data['udid'] = this._udid;
    data['groupCode'] = this._groupCode;
    data['category'] = this._category;
    data['sub_category'] = this._sub_category;
    data['payload'] = this._payload;
    return data;
  }

  convertMapToPayload(var map) {
    if (map!=null) {
      payload = {};
      payload?['amount'] = map['money']??'0';
      payload?['timePayment'] = map['']??'0';
      payload?['cardholderName'] = map['']??'';
      payload?['pan'] = map['']??'';
      payload?['icon'] = map['']??'';
    }
  }
}