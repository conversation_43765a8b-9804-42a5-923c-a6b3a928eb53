import 'base_response.dart';

class UserInfo implements BaseResponseData{
  String? mposKey;
  Config? config;
  String? rawData;
  String? emailMerchant;
  String? bankName;
  String? merchantName;
  List<InstallmentInfo>? installmentInfo;
  List<InstallmentVaymuonInfo>? installmentVaymuonInfo;
  List<ExchangeInfo>? exchangeInfo;
  List<ExchangeQrInfo>? exchangeQrInfo;
  List<ExchangeLinkCardInfo>? exchangeLinkCardInfo;
  List<ExchangeVaymuonInfo>? exchangeVaymuonInfo;
  QuickWithdrawInfo? quickWithdrawInfo;
  int? isAllowRooted;
  int? restrictInternationalCard;
  int? emvRestrictInternationalCard;
  int? isSaveLogin;
  int? isShowNormalPayment;
  int? isShowInstallmentPayment;
  int? isShowNew;
  int? isShowChangePassword;
  int? isShowTransactionHistory;
  int? checkFeeTrans;
  int? checkFeeInstallment;
  int? checkFeeChange;
  int? checkFeeChangeQr;
  int? checkFeeCustomerQr;
  int? sendTrxReceipt;
  int? isShowVimoQr;
  int? isFeedback;
  int? isMacqFlow;
  int? feedbackAmountMin;
  int? minAmount;
  int? optCheckInstallmentBin;
  int? optConvertVimo;
  int? optConvertVimoAmount;
  String? kData;
  bool? outage;
  String? outageMessage;
  int? enableNormalPayment;
  int? isPayLink;
  int? isNormalPayLink;
  int? createLinkPaymentFlag;
  int? isPayCard;
  int? optCheckAffiliate;
  int? showMenuLending;
  int? mandatoryCheckLimit;
  int? paybyVasBalanceFlag;
  bool? hasPasswordLv2;
  int? permitSettlement;
  int? permitVoid;
  int? connectType;
  String? secretKey;
  String? urlImageMerchant;
  String? username;
  List<MenuHome>? menuHome;
  int? merchantId;
  String? isSaleService;
  String? isDisableCheckGps;
  String? mvisaTid;
  String? mvisaMid;
  String? mvisaMidMaster;
  String? mcc;
  String? affiliateLink;
  String? affiliateImage;
  String? menuLendingLink;
  String? nextlendRequestLink;
  String? nextlendNotifyTitle;
  String? nextlendNotifyDescription;
  String? authoriserName;
  String? authoriserContactNumber;
  String? businessName;
  String? businessAddress;
  String? utmSource;
  bool? enableRethinkdb;
  String? rethinkHostName;
  int? rethinkPort;
  String? rethinkDbName;
  String? rethinkUsername;
  String? rethinkUserPassword;
  bool? requiredInsDescription;
  List<ListQr>? listQr;
  List<BankQr>? listBankQR;
  List<ConfigParamRequires>? configParamRequires;
  bool? isEnableVetcService;
  bool? check;
  Banners? banners;
  Error? error;

  int? lastTimeCache;
  String? pass;
  String? muid;

  UserInfo(
      {this.mposKey,
      this.config,
      this.rawData,
      this.emailMerchant,
      this.bankName,
      this.merchantName,
      this.installmentInfo,
      this.installmentVaymuonInfo,
      this.exchangeInfo,
      this.exchangeQrInfo,
      this.exchangeLinkCardInfo,
      this.exchangeVaymuonInfo,
      this.quickWithdrawInfo,
      this.isAllowRooted,
      this.restrictInternationalCard,
      this.emvRestrictInternationalCard,
      this.isSaveLogin,
      this.isShowNormalPayment,
      this.isShowInstallmentPayment,
      this.isShowNew,
      this.isShowChangePassword,
      this.isShowTransactionHistory,
      this.checkFeeTrans,
      this.checkFeeInstallment,
      this.checkFeeChange,
      this.checkFeeChangeQr,
      this.checkFeeCustomerQr,
      this.sendTrxReceipt,
      this.isShowVimoQr,
      this.isFeedback,
      this.isMacqFlow,
      this.feedbackAmountMin,
      this.minAmount,
      this.optCheckInstallmentBin,
      this.optConvertVimo,
      this.optConvertVimoAmount,
      this.kData,
      this.outage,
      this.outageMessage,
      this.enableNormalPayment,
      this.isPayLink,
      this.isNormalPayLink,
      this.createLinkPaymentFlag,
      this.isPayCard,
      this.optCheckAffiliate,
      this.showMenuLending,
      this.mandatoryCheckLimit,
      this.paybyVasBalanceFlag,
      this.hasPasswordLv2,
      this.permitSettlement,
      this.permitVoid,
      this.connectType,
      this.secretKey,
      this.urlImageMerchant,
      this.username,
      this.menuHome,
      this.merchantId,
      this.isSaleService,
      this.isDisableCheckGps,
      this.mvisaTid,
      this.mvisaMid,
      this.mvisaMidMaster,
      this.mcc,
      this.affiliateLink,
      this.affiliateImage,
      this.menuLendingLink,
      this.nextlendRequestLink,
      this.nextlendNotifyTitle,
      this.nextlendNotifyDescription,
      this.authoriserName,
      this.authoriserContactNumber,
      this.businessName,
      this.businessAddress,
      this.utmSource,
      this.enableRethinkdb,
      this.rethinkHostName,
      this.rethinkPort,
      this.rethinkDbName,
      this.rethinkUsername,
      this.rethinkUserPassword,
      this.requiredInsDescription,
      this.listQr,
      this.listBankQR,
      this.configParamRequires,
      this.isEnableVetcService,
      this.check,
      this.banners,
      this.error});

  UserInfo.fromJson(Map<String, dynamic> json) {
    config = json['config'] != null ? new Config.fromJson(json['config']) : null;
    emailMerchant = json['emailMerchant'];
    bankName = json['bankName'];
    merchantName = json['merchantName'];
    if (json['installmentInfo'] != null) {
      installmentInfo = <InstallmentInfo>[];
      json['installmentInfo'].forEach((v) {
        installmentInfo!.add(new InstallmentInfo.fromJson(v));
      });
    }
    if (json['installmentVaymuonInfo'] != null) {
      installmentVaymuonInfo = <InstallmentVaymuonInfo>[];
      json['installmentVaymuonInfo'].forEach((v) {
        installmentVaymuonInfo!.add(new InstallmentVaymuonInfo.fromJson(v));
      });
    }
    if (json['exchangeInfo'] != null) {
      exchangeInfo = <ExchangeInfo>[];
      json['exchangeInfo'].forEach((v) {
        exchangeInfo!.add(new ExchangeInfo.fromJson(v));
      });
    }
    if (json['exchangeQrInfo'] != null) {
      exchangeQrInfo = <ExchangeQrInfo>[];
      json['exchangeQrInfo'].forEach((v) {
        exchangeQrInfo!.add(new ExchangeQrInfo.fromJson(v));
      });
    }
    if (json['exchangeLinkCardInfo'] != null) {
      exchangeLinkCardInfo = <ExchangeLinkCardInfo>[];
      json['exchangeLinkCardInfo'].forEach((v) {
        exchangeLinkCardInfo!.add(new ExchangeLinkCardInfo.fromJson(v));
      });
    }
    if (json['exchangeVaymuonInfo'] != null) {
      exchangeVaymuonInfo = <ExchangeVaymuonInfo>[];
      json['exchangeVaymuonInfo'].forEach((v) {
        exchangeVaymuonInfo!.add(new ExchangeVaymuonInfo.fromJson(v));
      });
    }
    quickWithdrawInfo =
        json['quickWithdrawInfo'] != null ? new QuickWithdrawInfo.fromJson(json['quickWithdrawInfo']) : null;
    isAllowRooted = json['isAllowRooted'];
    restrictInternationalCard = json['restrictInternationalCard'];
    emvRestrictInternationalCard = json['emvRestrictInternationalCard'];
    isSaveLogin = json['isSaveLogin'];
    isShowNormalPayment = json['isShowNormalPayment'];
    isShowInstallmentPayment = json['isShowInstallmentPayment'];
    isShowNew = json['isShowNew'];
    isShowChangePassword = json['isShowChangePassword'];
    isShowTransactionHistory = json['isShowTransactionHistory'];
    checkFeeTrans = json['checkFeeTrans'];
    checkFeeInstallment = json['checkFeeInstallment'];
    checkFeeChange = json['checkFeeChange'];
    checkFeeChangeQr = json['checkFeeChangeQr'];
    checkFeeCustomerQr = json['checkFeeCustomerQr'];
    sendTrxReceipt = json['sendTrxReceipt'];
    isShowVimoQr = json['isShowVimoQr'];
    isFeedback = json['isFeedback'];
    isMacqFlow = json['isMacqFlow'];
    feedbackAmountMin = getInFromMap(json, 'feedbackAmountMin');//['feedbackAmountMin'];
    minAmount = json['minAmount'];
    optCheckInstallmentBin = json['optCheckInstallmentBin'];
    optConvertVimo = json['optConvertVimo'];
    optConvertVimoAmount = getInFromMap(json, 'optConvertVimoAmount');//json['optConvertVimoAmount'];
    kData = json['kData'];
    outage = json['outage'];
    outageMessage = json['outageMessage'];
    enableNormalPayment = json['enableNormalPayment'];
    isPayLink = json['isPayLink'];
    isNormalPayLink = json['isNormalPayLink'];
    createLinkPaymentFlag = json['createLinkPaymentFlag'];
    isPayCard = json['isPayCard'];
    optCheckAffiliate = json['optCheckAffiliate'];
    showMenuLending = json['showMenuLending'];
    mandatoryCheckLimit = json['mandatoryCheckLimit'];
    paybyVasBalanceFlag = json['paybyVasBalanceFlag'];
    hasPasswordLv2 = json['hasPasswordLv2'];
    permitSettlement = json['permitSettlement'];
    permitVoid = json['permitVoid'];

    //anhvt lam giong voi xu ly ben iOS
    if (json['config'] != null) {
      connectType = int.parse(json['config']['connectType'].toString());
      urlImageMerchant = json['config']['logoUrl'];
      String? strSecretKey = json['config']['secretKey'];
      if (strSecretKey == null) {
        strSecretKey = '';
      }
      String strTemp;
      if (strSecretKey.length < 16) {
        strTemp = strSecretKey + 'FFFFFFFFFFFFFFFF';
      } else {
        strTemp = strSecretKey;
      }
      secretKey = strTemp.substring(0, 16);
    } else {
      connectType = 0;
      secretKey = null;
      urlImageMerchant = '';
    }

    username = json['username'];
    if (json['menuHome'] != null) {
      menuHome = <MenuHome>[];
      json['menuHome'].forEach((v) {
        menuHome!.add(new MenuHome.fromJson(v));
      });
    }
    merchantId = getInFromMap(json, 'merchantId');//json['merchantId'];
    isSaleService = json['isSaleService'];
    isDisableCheckGps = json['isDisableCheckGps'];
    mvisaTid = json['mvisaTid'];
    mvisaMid = json['mvisaMid'];
    mvisaMidMaster = json['mvisaMidMaster'];
    mcc = json['mcc'];
    affiliateLink = json['affiliateLink'];
    affiliateImage = json['affiliateImage'];
    menuLendingLink = json['menuLendingLink'];
    nextlendRequestLink = json['nextlendRequestLink'];
    nextlendNotifyTitle = json['nextlendNotifyTitle'];
    nextlendNotifyDescription = json['nextlendNotifyDescription'];
    authoriserName = json['authoriserName'];
    authoriserContactNumber = json['authoriserContactNumber'];
    businessName = json['businessName'];
    businessAddress = json['businessAddress'];
    utmSource = json['utmSource'];
    enableRethinkdb = json['enableRethinkdb'];
    rethinkHostName = json['rethinkHostName'];
    rethinkPort = getInFromMap(json, 'rethinkPort');
    rethinkDbName = json['rethinkDbName'];
    rethinkUsername = json['rethinkUsername'];
    rethinkUserPassword = json['rethinkUserPassword'];
    requiredInsDescription = json['requiredInsDescription'];
    if (json['listQr'] != null) {
      listQr = <ListQr>[];
      json['listQr'].forEach((v) {
        listQr!.add(new ListQr.fromJson(v));
      });
    }
    if (json['listBankQR'] != null) {
      listBankQR = <BankQr>[];
      json['listBankQR'].forEach((v) {
        listBankQR!.add(new BankQr.fromJson(v));
      });
    }
    if (json['configParamRequires'] != null) {
      configParamRequires = <ConfigParamRequires>[];
      json['configParamRequires'].forEach((v) {
        configParamRequires!.add(new ConfigParamRequires.fromJson(v));
      });
    }
    isEnableVetcService = json['isEnableVetcService'];
    check = json['check'];
    banners = json['banners'] != null ? new Banners.fromJson(json['banners']) : null;
    error = json['error'] != null ? new Error.fromJson(json['error']) : null;
    lastTimeCache = json['lastTimeCache']!=null?json['lastTimeCache']:0;
    pass = json['pass']!=null?json['pass']:'';
    muid = json['muid']!=null?json['muid']:'';

  }

  @override
  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['mposKey'] = this.mposKey != null ? this.mposKey : '';
    if (this.config != null) {
      data['config'] = this.config!.toJson();
    }
    data['emailMerchant'] = this.emailMerchant;
    data['bankName'] = this.bankName;
    data['merchantName'] = this.merchantName;
    if (this.installmentInfo != null) {
      data['installmentInfo'] = this.installmentInfo!.map((v) => v.toJson()).toList();
    }
    if (this.installmentVaymuonInfo != null) {
      data['installmentVaymuonInfo'] = this.installmentVaymuonInfo!.map((v) => v.toJson()).toList();
    }
    if (this.exchangeInfo != null) {
      data['exchangeInfo'] = this.exchangeInfo!.map((v) => v.toJson()).toList();
    }
    if (this.exchangeQrInfo != null) {
      data['exchangeQrInfo'] = this.exchangeQrInfo!.map((v) => v.toJson()).toList();
    }
    if (this.exchangeLinkCardInfo != null) {
      data['exchangeLinkCardInfo'] = this.exchangeLinkCardInfo!.map((v) => v.toJson()).toList();
    }
    if (this.exchangeVaymuonInfo != null) {
      data['exchangeVaymuonInfo'] = this.exchangeVaymuonInfo!.map((v) => v.toJson()).toList();
    }
    if (this.quickWithdrawInfo != null) {
      data['quickWithdrawInfo'] = this.quickWithdrawInfo!.toJson();
    }
    data['isAllowRooted'] = this.isAllowRooted;
    data['restrictInternationalCard'] = this.restrictInternationalCard;
    data['emvRestrictInternationalCard'] = this.emvRestrictInternationalCard;
    data['isSaveLogin'] = this.isSaveLogin;
    data['isShowNormalPayment'] = this.isShowNormalPayment;
    data['isShowInstallmentPayment'] = this.isShowInstallmentPayment;
    data['isShowNew'] = this.isShowNew;
    data['isShowChangePassword'] = this.isShowChangePassword;
    data['isShowTransactionHistory'] = this.isShowTransactionHistory;
    data['checkFeeTrans'] = this.checkFeeTrans;
    data['checkFeeInstallment'] = this.checkFeeInstallment;
    data['checkFeeChange'] = this.checkFeeChange;
    data['checkFeeChangeQr'] = this.checkFeeChangeQr;
    data['checkFeeCustomerQr'] = this.checkFeeCustomerQr;
    data['sendTrxReceipt'] = this.sendTrxReceipt;
    data['isShowVimoQr'] = this.isShowVimoQr;
    data['isFeedback'] = this.isFeedback;
    data['isMacqFlow'] = this.isMacqFlow;
    data['feedbackAmountMin'] = this.feedbackAmountMin;
    data['minAmount'] = this.minAmount;
    data['optCheckInstallmentBin'] = this.optCheckInstallmentBin;
    data['optConvertVimo'] = this.optConvertVimo;
    data['optConvertVimoAmount'] = this.optConvertVimoAmount;
    data['kData'] = this.kData;
    data['outage'] = this.outage;
    data['outageMessage'] = this.outageMessage;
    data['enableNormalPayment'] = this.enableNormalPayment;
    data['isPayLink'] = this.isPayLink;
    data['isNormalPayLink'] = this.isNormalPayLink;
    data['createLinkPaymentFlag'] = this.createLinkPaymentFlag;
    data['isPayCard'] = this.isPayCard;
    data['optCheckAffiliate'] = this.optCheckAffiliate;
    data['showMenuLending'] = this.showMenuLending;
    data['mandatoryCheckLimit'] = this.mandatoryCheckLimit;
    data['paybyVasBalanceFlag'] = this.paybyVasBalanceFlag;
    data['hasPasswordLv2'] = this.hasPasswordLv2;
    data['permitSettlement'] = this.permitSettlement;
    data['permitVoid'] = this.permitVoid;
    data['connectType'] = this.connectType;
    data['username'] = this.username;
    if (this.menuHome != null) {
      data['menuHome'] = this.menuHome!.map((v) => v.toJson()).toList();
    }
    data['merchantId'] = this.merchantId;
    data['isSaleService'] = this.isSaleService;
    data['isDisableCheckGps'] = this.isDisableCheckGps;
    data['mvisaTid'] = this.mvisaTid;
    data['mvisaMid'] = this.mvisaMid;
    data['mvisaMidMaster'] = this.mvisaMidMaster;
    data['affiliateLink'] = this.affiliateLink;
    data['menuLendingLink'] = this.menuLendingLink;
    data['nextlendNotifyTitle'] = this.nextlendNotifyTitle;
    data['authoriserName'] = this.authoriserName;
    data['authoriserContactNumber'] = this.authoriserContactNumber;
    data['businessName'] = this.businessName;
    data['businessAddress'] = this.businessAddress;
    data['utmSource'] = this.utmSource;
    data['enableRethinkdb'] = this.enableRethinkdb;
    data['rethinkHostName'] = this.rethinkHostName;
    data['rethinkPort'] = this.rethinkPort;
    data['rethinkDbName'] = this.rethinkDbName;
    data['rethinkUsername'] = this.rethinkUsername;
    data['rethinkUserPassword'] = this.rethinkUserPassword;
    data['requiredInsDescription'] = this.requiredInsDescription;
    data['configParamRequires'] = this.configParamRequires;
    if (this.listQr != null) {
      data['listQr'] = this.listQr!.map((v) => v.toJson()).toList();
    }
    if (this.configParamRequires != null) {
      data['configParamRequires'] = this.configParamRequires!.map((v) => v.toJson()).toList();
    }
    data['isEnableVetcService'] = this.isEnableVetcService;
    data['check'] = this.check;
    if (this.banners != null) {
      data['banners'] = this.banners!.toJson();
    }
    if (this.error != null) {
      data['error'] = this.error!.toJson();
    }

    data['lastTimeCache'] = this.lastTimeCache;
    data['pass'] = this.pass;
    data['muid'] = this.muid;

    return data;
  }

  bool isRunMacq(){
    return isMacqFlow == 1;
  }
}

class Config {
  String? secretKey;
  String? merchantName;
  String? logoUrl;
  int? connectType;
  int? isShowNormalPayment;
  int? isShowInstallmentPayment;
  int? isShowNew;
  int? isShowChangePassword;
  int? isShowTransactionHistory;

  Config(
      {this.secretKey,
      this.merchantName,
      this.logoUrl,
      this.connectType,
      this.isShowNormalPayment,
      this.isShowInstallmentPayment,
      this.isShowNew,
      this.isShowChangePassword,
      this.isShowTransactionHistory});

  Config.fromJson(Map<String, dynamic> json) {
    secretKey = json['secretKey'];
    merchantName = json['merchantName'];
    logoUrl = json['logoUrl'];
    connectType = json['connectType'];
    isShowNormalPayment = json['isShowNormalPayment'];
    isShowInstallmentPayment = json['isShowInstallmentPayment'];
    isShowNew = json['isShowNew'];
    isShowChangePassword = json['isShowChangePassword'];
    isShowTransactionHistory = json['isShowTransactionHistory'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['secretKey'] = this.secretKey;
    data['merchantName'] = this.merchantName;
    data['logoUrl'] = this.logoUrl;
    data['connectType'] = this.connectType;
    data['isShowNormalPayment'] = this.isShowNormalPayment;
    data['isShowInstallmentPayment'] = this.isShowInstallmentPayment;
    data['isShowNew'] = this.isShowNew;
    data['isShowChangePassword'] = this.isShowChangePassword;
    data['isShowTransactionHistory'] = this.isShowTransactionHistory;
    return data;
  }
}

class InstallmentInfo {
  int? bankId;
  String? bankName;
  String? bankLongName;
  String? logo;
  String? idCardNumber;
  int? minAmount;
  int? maxAmount;
  String? policy;
  List<ListPeriod>? listPeriod;
  int? binNumberDigit;
  List<String>? binList;

  InstallmentInfo(
      {this.bankId,
      this.bankName,
      this.bankLongName,
      this.logo,
      this.idCardNumber,
      this.minAmount,
      this.maxAmount,
      this.policy,
      this.listPeriod,
      this.binNumberDigit,
      this.binList});

  InstallmentInfo.fromJson(Map<String, dynamic> json) {
    bankId = json['bankId'];
    bankName = json['bankName'];
    bankLongName = json['bankLongName'];
    logo = json['logo'];
    idCardNumber = json['idCardNumber'];
    minAmount = json['minAmount'];
    maxAmount = json['maxAmount'];
    policy = json['policy'];
    if (json['listPeriod'] != null) {
      listPeriod = <ListPeriod>[];
      json['listPeriod'].forEach((v) {
        listPeriod!.add(new ListPeriod.fromJson(v));
      });
    }
    binNumberDigit = json['binNumberDigit'];
    if (json['binList'] != null) {
      binList = <String>[];
      json['binList'].forEach((v) {
        binList!.add(v);
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['bankId'] = this.bankId;
    data['bankName'] = this.bankName;
    data['bankLongName'] = this.bankLongName;
    data['logo'] = this.logo;
    data['idCardNumber'] = this.idCardNumber;
    data['minAmount'] = this.minAmount;
    data['maxAmount'] = this.maxAmount;
    data['policy'] = this.policy;
    if (this.listPeriod != null) {
      data['listPeriod'] = this.listPeriod!.map((v) => v.toJson()).toList();
    }
    data['binNumberDigit'] = this.binNumberDigit;
    data['binList'] = this.binList;
    return data;
  }
}

class ListPeriod {
  int? installmentOutId;
  String? period;
  String? periodType;
  double? rate;
  double? linkCardRate;

  ListPeriod({this.installmentOutId, this.period, this.periodType, this.rate, this.linkCardRate});

  ListPeriod.fromJson(Map<String, dynamic> json) {
    installmentOutId = json['installmentOutId'];
    period = json['period'];
    periodType = json['periodType'];
    if(json['rate']!=null) {
      try {
        rate = double.parse(json['rate'].toString());
      } catch (e) {
        print(e);
      }
    }
    if(json['linkCardRate']!=null) {
      try {
        linkCardRate = double.parse(json['linkCardRate'].toString());
      } catch (e) {
        print(e);
      }
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['installmentOutId'] = this.installmentOutId;
    data['period'] = this.period;
    data['periodType'] = this.periodType;
    data['rate'] = this.rate;
    data['linkCardRate'] = this.linkCardRate;
    return data;
  }
}

class InstallmentVaymuonInfo {
  int? bankId;
  String? bankName;
  String? bankLongName;
  String? logo;
  String? idCardNumber;
  int? minAmount;
  int? maxAmount;
  List<ListPeriod>? listPeriod;

  InstallmentVaymuonInfo(
      {this.bankId,
      this.bankName,
      this.bankLongName,
      this.logo,
      this.idCardNumber,
      this.minAmount,
      this.maxAmount,
      this.listPeriod});

  InstallmentVaymuonInfo.fromJson(Map<String, dynamic> json) {
    bankId = json['bankId'];
    bankName = json['bankName'];
    bankLongName = json['bankLongName'];
    logo = json['logo'];
    idCardNumber = json['idCardNumber'];
    minAmount = json['minAmount'];
    maxAmount = json['maxAmount'];
    if (json['listPeriod'] != null) {
      listPeriod = List.empty(growable: true);
      json['listPeriod'].forEach((v) {
        listPeriod!.add(new ListPeriod.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['bankId'] = this.bankId;
    data['bankName'] = this.bankName;
    data['bankLongName'] = this.bankLongName;
    data['logo'] = this.logo;
    data['idCardNumber'] = this.idCardNumber;
    data['minAmount'] = this.minAmount;
    data['maxAmount'] = this.maxAmount;
    if (this.listPeriod != null) {
      data['listPeriod'] = this.listPeriod!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class ExchangeInfo {
  String? issuerCode;
  String? issuerName;
  double? fee;
  String? paymentMethod;

  ExchangeInfo({this.issuerCode, this.issuerName, this.fee, this.paymentMethod});

  ExchangeInfo.fromJson(Map<String, dynamic> json) {
    issuerCode = json['issuerCode'];
    issuerName = json['issuerName'];
    fee = double.parse(json['fee'].toString());
    paymentMethod = json['paymentMethod'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['issuerCode'] = this.issuerCode;
    data['issuerName'] = this.issuerName;
    data['fee'] = this.fee;
    data['paymentMethod'] = this.paymentMethod;
    return data;
  }
}

class ExchangeQrInfo {
  String? issuerCode;
  String? issuerName;
  double? fee;
  String? paymentMethod;

  ExchangeQrInfo({this.issuerCode, this.issuerName, this.fee, this.paymentMethod});

  ExchangeQrInfo.fromJson(Map<String, dynamic> json) {
    issuerCode = json['issuerCode'];
    issuerName = json['issuerName'];
    fee = double.parse(json['fee'].toString());
    paymentMethod = json['paymentMethod'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['issuerCode'] = this.issuerCode;
    data['issuerName'] = this.issuerName;
    data['fee'] = this.fee;
    data['paymentMethod'] = this.paymentMethod;
    return data;
  }
}

class ExchangeLinkCardInfo {
  String? issuerCode;
  String? issuerName;
  double? fee;
  String? paymentMethod;

  ExchangeLinkCardInfo({this.issuerCode, this.issuerName, this.fee, this.paymentMethod});

  ExchangeLinkCardInfo.fromJson(Map<String, dynamic> json) {
    issuerCode = json['issuerCode'];
    issuerName = json['issuerName'];
    fee = double.parse(json['fee'].toString());
    paymentMethod = json['paymentMethod'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['issuerCode'] = this.issuerCode;
    data['issuerName'] = this.issuerName;
    data['fee'] = this.fee;
    data['paymentMethod'] = this.paymentMethod;
    return data;
  }
}

class ExchangeVaymuonInfo {
  String? issuerCode;
  String? issuerName;
  double? fee;
  String? paymentMethod;

  ExchangeVaymuonInfo({this.issuerCode, this.issuerName, this.fee, this.paymentMethod});

  ExchangeVaymuonInfo.fromJson(Map<String, dynamic> json) {
    issuerCode = json['issuerCode'];
    issuerName = json['issuerName'];
    fee = double.parse(json['fee'].toString());
    paymentMethod = json['paymentMethod'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['issuerCode'] = this.issuerCode;
    data['issuerName'] = this.issuerName;
    data['fee'] = this.fee;
    data['paymentMethod'] = this.paymentMethod;
    return data;
  }
}

int? getInFromMap(Map<String, dynamic> json, String key){
  return json[key] is String? int.parse(json[key]):json[key];
}

class QuickWithdrawInfo {
  int? amountMin;
  int? amountMax;
  List<JsonQuickWithdrawList>? jsonQuickWithdrawList;

  QuickWithdrawInfo({this.amountMin, this.amountMax, this.jsonQuickWithdrawList});

  QuickWithdrawInfo.fromJson(Map<String, dynamic> json) {
    amountMin = getInFromMap(json, 'amountMin');//json['amountMin'] is String? int.parse(json['amountMin']):json['amountMin'];
    amountMax = getInFromMap(json, 'amountMax');//json['amountMax'] is String? int.parse(json['amountMax']):json['amountMax'];
    if (json['jsonQuickWithdrawList'] != null) {
      jsonQuickWithdrawList = List.empty(growable: true);
      json['jsonQuickWithdrawList'].forEach((v) {
        jsonQuickWithdrawList!.add(new JsonQuickWithdrawList.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['amountMin'] = this.amountMin;
    data['amountMax'] = this.amountMax;
    if (this.jsonQuickWithdrawList != null) {
      data['jsonQuickWithdrawList'] = this.jsonQuickWithdrawList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class JsonQuickWithdrawList {
  int? flatFee;
  double? percentageFee;
  int? amountMin;
  int? amountMax;

  JsonQuickWithdrawList({this.flatFee, this.percentageFee, this.amountMin, this.amountMax});

  JsonQuickWithdrawList.fromJson(Map<String, dynamic> json) {
    flatFee = getInFromMap(json, 'flatFee');//json['flatFee'];
    percentageFee = json['percentageFee']!=null?double.parse(json['percentageFee'].toString()):0;
    amountMin = getInFromMap(json, 'amountMin');//json['amountMin'];
    amountMax = getInFromMap(json, 'amountMax');//json['amountMax'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['flatFee'] = this.flatFee;
    data['percentageFee'] = this.percentageFee;
    data['amountMin'] = this.amountMin;
    data['amountMax'] = this.amountMax;
    return data;
  }
}

class MenuHome {
  String? title;
  String? titleEn;
  String? iconLink;
  int? position;
  String? status;
  String? serviceCode;
  String? menuType;
  int? rownum;
  int? id;
  int? version;
  int? count;

  MenuHome(
      {this.title,
      this.titleEn,
      this.iconLink,
      this.position,
      this.status,
      this.serviceCode,
      this.menuType,
      this.rownum,
      this.id,
      this.version,
      this.count});

  MenuHome.fromJson(Map<String, dynamic> json) {
    title = json['title'];
    titleEn = json['titleEn'];
    iconLink = json['iconLink'];
    position = json['position'];
    status = json['status'];
    serviceCode = json['serviceCode'];
    menuType = json['menuType'];
    rownum = json['rownum'];
    id = json['id'];
    version = json['version'];
    count = json['count'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['title'] = this.title;
    data['titleEn'] = this.titleEn;
    data['iconLink'] = this.iconLink;
    data['position'] = this.position;
    data['status'] = this.status;
    data['serviceCode'] = this.serviceCode;
    data['menuType'] = this.menuType;
    data['rownum'] = this.rownum;
    data['id'] = this.id;
    data['version'] = this.version;
    data['count'] = this.count;
    return data;
  }
}

class BankQr {
  String? shortName;
  String? longName;
  String? longNameEn;
  String? logo;
  String? description;
  String? descriptionEn;
  List<BankQrChild>? qrChildren;

  BankQr.fromJson(Map<String, dynamic> json) {
    shortName = json['shortName'];
    longName = json['longName'];
    longNameEn = json['longNameEn'];
    logo = json['logo'];
    description = json['description'];
    descriptionEn = json['descriptionEn'];
    if (json['qrChildren'] != null) {
      qrChildren = <BankQrChild>[];
      json['qrChildren'].forEach((v) {
        qrChildren!.add(new BankQrChild.fromJson(v));
      });
    }
  }
}

class BankQrChild {
  String? shortNameChild;
  String? longNameChild;
  String? logoChild;
  double? fee;
  String? description;
  String? descriptionEn;
  String? qrType;
  String? userManualLink;

  BankQrChild.fromJson(Map<String, dynamic> json) {
    shortNameChild = json['shortNameChild'];
    longNameChild = json['longNameChild'];
    logoChild = json['logoChild'];
    fee = json['fee'];
    description = json['description'];
    descriptionEn = json['descriptionEn'];
    qrType = json['qrType'];
    userManualLink = json['userManualLink'];
  }
}

class ListQr {
  String? qrType;
  String? qrName;
  String? description;
  String? descriptionEn;
  String? imgPartner;
  String? imgLogo;
  String? checkStatus;

  ListQr(
      {this.qrType,
      this.qrName,
      this.description,
      this.descriptionEn,
      this.imgPartner,
      this.imgLogo,
      this.checkStatus});

  ListQr.fromJson(Map<String, dynamic> json) {
    qrType = json['qrType'];
    qrName = json['qrName'];
    description = json['description'];
    descriptionEn = json['descriptionEn'];
    imgPartner = json['imgPartner'];
    imgLogo = json['imgLogo'];
    checkStatus = json['checkStatus'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['qrType'] = this.qrType;
    data['qrName'] = this.qrName;
    data['description'] = this.description;
    data['descriptionEn'] = this.descriptionEn;
    data['imgPartner'] = this.imgPartner;
    data['imgLogo'] = this.imgLogo;
    data['checkStatus'] = this.checkStatus;
    return data;
  }
}

class Error {
  int? code;
  String? message;
  String? messageEn;

  Error({this.code, this.message, this.messageEn});

  Error.fromJson(Map<String, dynamic> json) {
    code = json['code'];
    message = json['message'];
    messageEn = json['messageEn'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['code'] = this.code;
    data['message'] = this.message;
    data['messageEn'] = this.messageEn;
    return data;
  }
}

class Banners {
  String? aCTIVEATM360;
  String? uNGVON;

  Banners({this.aCTIVEATM360, this.uNGVON});

  Banners.fromJson(Map<String, dynamic> json) {
    aCTIVEATM360 = json['ACTIVE_ATM360'];
    uNGVON = json['UNG_VON'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['ACTIVE_ATM360'] = this.aCTIVEATM360;
    data['UNG_VON'] = this.uNGVON;
    return data;
  }
}

class ConfigParamRequires {
  String? serviceCode;
  String? emailParam;
  String? mobileNoParam;
  String? descriptionParam;
  int? amountThreshold;

  ConfigParamRequires(
      {this.serviceCode, this.emailParam, this.mobileNoParam, this.descriptionParam, this.amountThreshold});

  ConfigParamRequires.fromJson(Map<String, dynamic> json) {
    serviceCode = json['serviceCode'];
    emailParam = json['emailParam'];
    mobileNoParam = json['mobileNoParam'];
    descriptionParam = json['descriptionParam'];
    amountThreshold = json['amountThreshold'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['serviceCode'] = this.serviceCode;
    data['emailParam'] = this.emailParam;
    data['mobileNoParam'] = this.mobileNoParam;
    data['descriptionParam'] = this.descriptionParam;
    data['amountThreshold'] = this.amountThreshold;
    return data;
  }
}
