import 'package:cashiermodule/Pages/deposit_page/deposit_controller.dart';
import 'package:cashiermodule/Pages/deposit_page/deposit_page.dart';
import 'package:cashiermodule/Pages/deposit_page/deposit_payment_result_controller.dart';
import 'package:cashiermodule/Pages/deposit_page/deposit_payment_result_page.dart';
import 'package:cashiermodule/Pages/deposit_page/detail_deposit_controller.dart';
import 'package:cashiermodule/Pages/deposit_page/detail_deposit_page.dart';
import 'package:cashiermodule/Pages/deposit_page/detail_trans_deposit_controller.dart';
import 'package:cashiermodule/Pages/deposit_page/detail_trans_deposit_page.dart';
import 'package:cashiermodule/Pages/deposit_page/history_deposit_controller.dart';
import 'package:cashiermodule/Pages/deposit_page/history_deposit_page.dart';
import 'package:cashiermodule/Pages/deposit_page/query_deposit_controller.dart';
import 'package:cashiermodule/Pages/deposit_page/query_deposit_page.dart';
import 'package:cashiermodule/Pages/detail_trans_page/detail_trans_controller.dart';
import 'package:cashiermodule/Pages/detail_trans_page/detail_trans_page.dart';
import 'package:cashiermodule/Pages/detail_transaction_online/detail_transaction_online_controller.dart';
import 'package:cashiermodule/Pages/detail_transaction_online/detail_transaction_online_page.dart';
import 'package:cashiermodule/Pages/fwd_page/fwd_detail_trans_controller.dart';
import 'package:cashiermodule/Pages/fwd_page/fwd_detail_trans_page.dart';
import 'package:cashiermodule/Pages/history_page/history_all_trans/history_all_trans_controller.dart';
import 'package:cashiermodule/Pages/history_page/history_all_trans/history_all_trans_page.dart';
import 'package:cashiermodule/Pages/history_page/history_trans_card/history_trans_card_controller.dart';
import 'package:cashiermodule/Pages/history_page/history_trans_card/history_trans_card_page.dart';
import 'package:cashiermodule/Pages/history_page/history_transaction_controller.dart';
import 'package:cashiermodule/Pages/history_page/history_transaction_page.dart';
import 'package:cashiermodule/Pages/home_new/controller/swipe_card_controller.dart';
import 'package:cashiermodule/Pages/home_new/screen/swipe_card_screen.dart';
import 'package:cashiermodule/Pages/moto_page/MotoCardPage.dart';
import 'package:cashiermodule/Pages/moto_page/MotoPaymentInforPage.dart';
import 'package:cashiermodule/Pages/moto_page/MotoPaymentResult.dart';
import 'package:cashiermodule/Pages/moto_page/controller/moto_card_controller.dart';
import 'package:cashiermodule/Pages/moto_page/controller/moto_payment_info_controller.dart';
import 'package:cashiermodule/Pages/moto_page/controller/moto_payment_result_controller.dart';
import 'package:cashiermodule/Pages/notification/bindings/notification_detail_binding.dart';
import 'package:cashiermodule/Pages/notification/bindings/notification_list_binding.dart';
import 'package:cashiermodule/Pages/notification/screens/notification_detail_page.dart';
import 'package:cashiermodule/Pages/notification/screens/notification_list_page.dart';
import 'package:cashiermodule/Pages/push_payment_page/list_order_page/controller/pp_order_controller.dart';
import 'package:cashiermodule/Pages/push_payment_page/list_order_page/pp_order_page.dart';
import 'package:cashiermodule/Pages/push_payment_page/push_payment_home/controller/push_payment_home_controller.dart';
import 'package:cashiermodule/Pages/push_payment_page/push_payment_home/push_payment_homepage.dart';
import 'package:cashiermodule/Pages/sme_card/sme_card_controller.dart';
import 'package:cashiermodule/Pages/sme_card/sme_card_page.dart';
import 'package:cashiermodule/Pages/transaction_result_page/trans_result_controller.dart';
import 'package:cashiermodule/Pages/transaction_result_page/trans_result_page.dart';
import 'package:cashiermodule/Pages/webview/webview_info_controller.dart' as module;
import 'package:cashiermodule/Pages/webview/webview_info_screen.dart' as module;
import 'package:get/get.dart';
import 'package:mposxs/app/binding/SettingsBinding.dart';
import 'package:mposxs/app/binding/change_pass_binding.dart';
import 'package:mposxs/app/binding/continue_payment_binding.dart';
import 'package:mposxs/app/binding/history_all_binding.dart';
import 'package:mposxs/app/binding/history_binding.dart';
import 'package:mposxs/app/binding/history_detail_binding.dart';
import 'package:mposxs/app/binding/installment_enter_amount_binding.dart';
import 'package:mposxs/app/binding/installment_enter_info_binding.dart';
import 'package:mposxs/app/binding/installment_link_binding.dart';
import 'package:mposxs/app/binding/installment_list_bank_binding.dart';
import 'package:mposxs/app/binding/instalment_confirm_binding.dart';
import 'package:mposxs/app/binding/login_binding.dart';
import 'package:mposxs/app/binding/main_binding.dart';
import 'package:mposxs/app/binding/payment_finish_binding.dart';
import 'package:mposxs/app/binding/payment_init_binding.dart';
import 'package:mposxs/app/binding/qr_code_binding.dart';
import 'package:mposxs/app/binding/qr_code_p12_binding.dart';
import 'package:mposxs/app/binding/qr_enter_info_binding.dart';
import 'package:mposxs/app/binding/qr_list_source_binding.dart';
import 'package:mposxs/app/binding/splash_binding.dart';
import 'package:mposxs/app/binding/webview_info_binding.dart';
import 'package:mposxs/app/ui/screen/change_pass_screen.dart';
import 'package:mposxs/app/ui/screen/continue_payment_screen.dart';
import 'package:mposxs/app/ui/screen/history/history_all_screen.dart';
import 'package:mposxs/app/ui/screen/history/history_detail_screen.dart';
import 'package:mposxs/app/ui/screen/installment/installment_confirm_screen.dart';
import 'package:mposxs/app/ui/screen/installment/installment_enter_amount_screen.dart';
import 'package:mposxs/app/ui/screen/installment/installment_enter_info_screen.dart';
import 'package:mposxs/app/ui/screen/installment/installment_link_screen.dart';
import 'package:mposxs/app/ui/screen/installment/installment_list_bank_screen.dart';
import 'package:mposxs/app/ui/screen/login/hello_merchant.dart';
import 'package:mposxs/app/ui/screen/login/login_screen.dart';
import 'package:mposxs/app/ui/screen/main/main_screen.dart';
import 'package:mposxs/app/ui/screen/payment/payment_finish_screen.dart';
import 'package:mposxs/app/ui/screen/payment/payment_init_screen.dart';
import 'package:mposxs/app/ui/screen/qr/qr_code_p12_screen.dart';
import 'package:mposxs/app/ui/screen/qr/qr_code_screen.dart';
import 'package:mposxs/app/ui/screen/qr/qr_enter_info_screen.dart';
import 'package:mposxs/app/ui/screen/qr/qr_list_source_screen.dart';
import 'package:mposxs/app/ui/screen/settings/settings_screen.dart';
import 'package:mposxs/app/ui/screen/splash_screen.dart';
import 'package:mposxs/app/ui/screen/webview_info_screen.dart';

class AppPages {
  // static final args = Get.arguments as Map<String, dynamic>?;
  static final pages = [
    GetPage(name: AppRoute.splash_screen, page: () => SplashScreen(), binding: SplashBinding()),
    GetPage(name: AppRoute.login_screen, page: () => LoginScreen(), binding: LoginBinding()),
    GetPage(name: AppRoute.hello_screen, page: () => HelloMerchant()),
    GetPage(name: AppRoute.main_screen, page: () => MainScreen(), bindings: [MainBinding(), PaymentInitBinding(), HistoryBinding()]),
    GetPage(name: AppRoute.change_pass_screen, page: () => ChangePassScreen(), binding: ChangePassBinding()),
    GetPage(name: AppRoute.settings_screen, page: () => SettingsScreen(), binding: SettingsBinding(), transition: Transition.downToUp, transitionDuration: Duration(milliseconds: 300)),
    GetPage(name: AppRoute.payment_init_screen, page: () => PaymentInitScreen(), binding: PaymentInitBinding()),
    GetPage(name: AppRoute.history_detail_screen, page: () => HistoryDetailScreen(), binding: HistoryDetailBinding()),
    GetPage(name: AppRoute.history_all_screen, page: () => HistoryAllScreen(), binding: HistoryAllBinding()),
    GetPage(name: AppRoute.qr_list_source_screen, page: () => QrListSourceScreen(), binding: QrListSourceBinding()),
    GetPage(name: AppRoute.qr_enter_info_screen, page: () => QrEnterInfoScreen(), binding: QrEnterInfoBinding()),
    GetPage(name: AppRoute.qr_code_screen, page: () => QrCodeScreen(), binding: QrCodeBinding()),
    GetPage(name: AppRoute.payment_finish_screen, page: () => PaymentFinishScreen(), binding: PaymentFinishBinding(), transition: Transition.rightToLeft, transitionDuration: Duration(milliseconds: 500)),
    GetPage(name: AppRoute.webview_info_screen, page: () => WebViewInfoScreen(), binding: WebViewInfoBinding(), transition: Transition.fade, transitionDuration: Duration(milliseconds: 300)),
    GetPage(name: AppRoute.installment_list_bank_screen, page: () => InstallmentListBankScreen(), binding: InstallmentListBankBinding()),
    GetPage(name: AppRoute.installment_enter_amount_screen, page: () => InstallmentEnterAmountScreen(), binding: InstallmentEnterAmountBinding()),
    GetPage(name: AppRoute.installment_enter_info_screen, page: () => InstallmentEnterInfoScreen(), binding: InstallmentEnterInfoBinding()),
    GetPage(name: AppRoute.installment_confirm_screen, page: () => InstallmentConfirmScreen(), binding: InstallmentConfirmBinding()),
    GetPage(name: AppRoute.installment_link_screen, page: () => InstallmentLinkScreen(), binding: InstallmentLinkBinding()),
    GetPage(name: AppRoute.continue_payment_screen, page: () => ContinuePaymentScreen(), binding: ContinuePaymentBinding()),
    GetPage(name: AppRoute.qr_code_p12_screen, page: () => QrCodeP12Screen(), binding: QrCodeP12Binding(), transition: Transition.upToDown, transitionDuration: Duration(milliseconds: 300)),
    GetPage(name: AppRoute.push_payment, page: () => PushPaymentHomePage(), binding: PushPaymentHomeBinding()),
    GetPage(
        name: AppRoute.listOrderPage, page: () => ListOrderPage(), binding: ListOrderBinding()),
    GetPage(
        name: AppRoute.swipeCard, page: () => SwipeCardScreen(), binding: SwipeCardBinding()),
    GetPage(
        name: AppRoute.moto_screen, page: () => MotoPaymentInforPage(), binding: MotoPaymentInfoBinding()),
    GetPage(
        name: AppRoute.MOTO_CARD_PAGE, page: () => MotoCardPage(), binding: MotoCardInfoBinding()),
    GetPage(
        name: AppRoute.MOTO_RESULT_PAGE, page: () => MotoPaymentResult(), binding: MotoPaymentResultBinding()),
    GetPage(
        name: AppRoute.HISTORY_CARD_PAGE, page: () => HistoryTransCardPage(), binding: HistoryTransCardBinding()),
    GetPage(
        name: AppRoute.HISTORY_PAGE, page: () => HistoryTransPage(), binding: HistoyTransBinding()),
    GetPage(
        name: AppRoute.HISTORY_ALL_PAGE, page: () => HistoryAllTransPage(), binding: HistoryAllTransBinding()),
    GetPage(
        name: AppRoute.FWD_DETAIL_TRANS_PAGE, page: () => FwdDetailTransPage(), binding: FwdDetailTransBinding()),
    GetPage(
        name: AppRoute.DEPOSIT_PAGE, page: () => DepositPage(), binding: DepositBinding(), transition: Transition.rightToLeft, transitionDuration: Duration(milliseconds: 300)),
    GetPage(
        name: AppRoute.QUERY_DEPOSIT_PAGE, page: () => QueryDepositPage(), binding: QueryDepositBinding()),
    GetPage(
        name: AppRoute.HISTORY_DEPOSIT_PAGE, page: () => HistoryDepositPage(), binding: HistoryDepositBinding()),
    GetPage(
        name: AppRoute.DETAIL_DEPOSIT_PAGE, page: () => DetailDepositPage(), binding: DetailDepositBinding()),
    GetPage(
        name: AppRoute.DETAIL_TRANS_DEPOSIT_PAGE, page: () => DetailTransDepositPage(), binding: DetailTransDepositBinding()),
    GetPage(
        name: AppRoute.DETAIL_TRANS_PAGE, page: () => DetailTransPage(), binding: DetailTransBinding()),
    GetPage(
        name: AppRoute.SME_CARD_PAGE, page: () => SMECardPage(), binding: SMECardBinding()),
    GetPage(
        name: AppRoute.TRANS_RESULT, page: () => TransResultPage(), binding: TransResultBinding()),
    GetPage(
        name: AppRoute.DETAIL_TRANS_ONLINE_PAGE, page: () => DetailTransactionOnlinePage(), binding: DetailTransactionOnlineBinding()),
    GetPage(
        name: AppRoute.RESULT_DEPOSIT_PAGE, page: () => DepositPaymentResultPage(), binding: DepositPaymentResultBinding()),
    GetPage(
        name: AppRoute.NOTIFICATION_LIST, page: () => NotificationListPage(), binding: NotificationListBinding(), transition: Transition.downToUp, transitionDuration: Duration(milliseconds: 300)),
    GetPage(
        name: AppRoute.NOTIFICATION_DETAIL, page: () => NotificationDetailPage(), binding: NotificationDetailBiding()),
    GetPage(
        name: AppRoute.WEBVIEW_INFO, page: () => module.WebViewInfoScreen(), binding: module.WebViewInfoBinding()),
  ];
}

class AppRoute {
  static const String splash_screen = '/splash_screen';
  static const String login_screen = '/login_screen';
  static const String hello_screen = '/hello_screen';
  static const String main_screen = '/main_screen';
  static const String change_pass_screen = '/change_pass_screen';
  static const String settings_screen = '/settings_screen';
  static const String continue_payment_screen = '/continue_payment_screen';
  static const String payment_init_screen = '/payment_init_screen';
  static const String qr_list_source_screen = '/qr_list_source_screen';
  static const String qr_enter_info_screen = '/qr_enter_info_screen';
  static const String qr_code_screen = '/qr_code_screen';
  static const String payment_finish_screen = '/payment_finish_screen';
  static const String webview_info_screen = '/webview_info_screen';
  static const String installment_list_bank_screen = '/installment_list_bank_screen';
  static const String installment_enter_amount_screen = '/installment_enter_amount_screen';
  static const String installment_enter_info_screen = '/installment_enter_info_screen';
  static const String installment_confirm_screen = '/installment_confirm_screen';
  static const String installment_link_screen = '/installment_link_screen';
  static const String history_detail_screen = '/history_detail_screen';
  static const String history_all_screen = '/history_all_screen';
  static const String qr_code_p12_screen = '/qr_code_p12_screen';
  static const String push_payment = '/cashier';
  static const String listOrderPage = '/listOrderPage';
  static const String swipeCard = '/swipeCard';
  static const String moto_screen = '/motoPaymentInfor';
  static const String MOTO_CARD_PAGE = '/motoCardInfo';
  static const String MOTO_RESULT_PAGE = '/motoPaymentResult';
  static const String HISTORY_PAGE = '/history';
  static const String HISTORY_CARD_PAGE = '/historyCard';
  static const String HISTORY_ALL_PAGE = '/historyAllTrans';
  static const String FWD_DETAIL_TRANS_PAGE = '/fwdDetailTransPage';
  static const String DEPOSIT_PAGE = '/depositPage';
  static const String DETAIL_DEPOSIT_PAGE = '/detailDepositPage';
  static const String DETAIL_TRANS_DEPOSIT_PAGE = '/detailTransDepositPage';
  static const String HISTORY_DEPOSIT_PAGE = '/historyDepositPage';
  static const String QUERY_DEPOSIT_PAGE = '/queryDepositPage';
  static const String DETAIL_TRANS_PAGE = '/historyDetail';
  static const String DETAIL_TRANS_ONLINE_PAGE = '/historyOnlineDetail';
  static const String RESULT_DEPOSIT_PAGE = '/resultDepositPage';
  static const String NOTIFICATION_LIST = '/notificationList';
  static const String NOTIFICATION_DETAIL = '/notificationDetail';
  static const String WEBVIEW_INFO = '/webviewInfo';
  static const String SME_CARD_PAGE = '/smeCardPage';
  static const String TRANS_RESULT = '/transResult';


}
