import 'package:cashiermodule/Utilities/Logger.dart';
import 'package:flutter_tts/flutter_tts.dart';
class TtsControl {
  static TtsControl _shareInstance = TtsControl._instance();

  factory TtsControl() => _shareInstance;

  TtsControl._instance();

  FlutterTts flutterTts = FlutterTts();

  initTts() async {
    // await flutterTts.setLanguage('en-US');
    await flutterTts.setLanguage('vi-VN');
    await flutterTts.awaitSpeakCompletion(true);
    await flutterTts.setVolume(1.0);
    await flutterTts.setSpeechRate(0.5);
    await flutterTts.setPitch(1.0);
  }

  speak(String text) async {
    stop();
    Logger().write('tts lang: ${await flutterTts.isLanguageAvailable('vi-VN')}');
    try {
      flutterTts.speak(text);
    } catch (e) {
      Logger().write('error speak: ${e.toString()}');
    }
  }

  stop() {
    flutterTts.stop();
  }
}