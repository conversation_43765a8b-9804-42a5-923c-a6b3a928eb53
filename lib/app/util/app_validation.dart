// check

import 'package:flutter/widgets.dart';
import 'package:mposxs/app/res/string/app_strings.dart';

import 'app_utils.dart';

convertPhoneToOrigin(String phone) {
  String phoneNumber = phone.trim();
  if (phoneNumber.length != 0) {
    phoneNumber = phoneNumber.replaceFirst('(+84)', '0');
    phoneNumber = phoneNumber.replaceFirst('+84', '0');
    // phoneNumber = phoneNumber.replace('0084', '0');
    phoneNumber = phoneNumber.replaceAll(' ', '');
    // phoneNumber = phoneNumber.replace(/[^0-9]/g, '');
  }
  return phoneNumber;
}

checkValidPhone(BuildContext? context, String phone) {
  String phoneNumber = convertPhoneToOrigin(phone);
  bool isValid = false;
  if (phoneNumber.length == 10) {
    String firstNumber = phoneNumber.substring(0, 2);
    isValid = (firstNumber == '09' ||
        firstNumber == '08' ||
        firstNumber == '07' ||
        firstNumber == '03' ||
        firstNumber == '05');
  }
  return isValid ? null : AppStrings.getString(AppStrings.invalidPhone);
}

checkValidEmail(BuildContext? context, String email) {
  String textEmail = email.trim();
  Pattern pattern =
      r'^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$';
  RegExp regex = RegExp(pattern as String);
  if (!regex.hasMatch(textEmail))
    return AppStrings.getString(AppStrings.invalidEmail);
  else
    return null;
}

checkEmptyEmail(BuildContext context, String email) {
  return email.length == 0 ? AppStrings.getString(AppStrings.emptyEmail) : null;
}

checkEmptyAndValidEmail(BuildContext context, String email) {
  if (checkEmptyEmail(context, email) != null) {
    return checkEmptyEmail(context, email);
  }
  if (checkValidEmail(context, email) != null) {
    return checkValidEmail(context, email);
  }
  return null;
}

String checkIsNullOrEmptyToString(Object? o) {
  if (o != null) {
    return o.toString();
  }
  return "";
}

bool isNullEmpty(Object? o) => o == null || "" == o;

bool isNullEmptyOrFalse(Object? o) => o == null || false == o || "" == o;

bool isNullEmptyFalseOrZero(Object? o) => o == null || false == o || 0 == o || "" == o || "0" == o;

bool isNumeric(dynamic s) {
  String sConvert = s.toString();
  if (sConvert == null) {
    return false;
  }
  return (double.tryParse(sConvert) != null || int.tryParse(sConvert) != null);
}

int convertStringToInt(String? data) {
  if ((data != null) && (data.isNotEmpty) && isNumeric(data)) {
    return int.parse(data);
  }else {
    return 0;
  }
}

checkValidAccountName(String accountName) {
  if (isNullEmpty(accountName)) {
    return AppStrings.getString(AppStrings.accountNameEmpty);
  }
  if (accountName.length > 64) {
    return AppStrings.getString(AppStrings.accountNameTooLong);
  }
  return null;
}

checkValidPassword(String password) {
  if (isNullEmpty(password)) {
    return AppStrings.getString(AppStrings.passwordEmpty);
  }
  if (password.length > 64) {
    return AppStrings.getString(AppStrings.passwordTooLong);
  }
  return null;
}

buildFormatBankNumber(String bankNumber) {
  StringBuffer formattedString = StringBuffer();

  int length = bankNumber.length;
  int index = 0;
  while (index < length) {
    if (index + 4 <= length) {
      formattedString.write(bankNumber.substring(index, index + 4));
      index += 4;
      if (index < length) {
        formattedString.write('  ');
      }
    } else {
      formattedString.write(bankNumber.substring(index));
      break;
    }
  }

  return formattedString.toString();
}

bool validateAmount(BuildContext? context, String amount, double min) {
  if (isNullEmpty(amount) || amount == '0') {
    if (context != null) {
      AppUtils.showDialogError(context, AppStrings.getString(AppStrings.errorAmountEmpty));
    }
    return false;
  }
  if (int.parse(amount) < min) {
    if (context != null) {
      AppUtils.showDialogError(context, AppStrings.getString(AppStrings.errorAmountMin)!.replaceFirst('%s', AppUtils.formatCurrency(min)));
    }
    return false;
  }
  return true;
}

bool checkMinMaxAmount(String amount, String minAmount, String maxAmount) {
  if (isNullEmpty(amount) || isNullEmpty(minAmount) || isNullEmpty(maxAmount)) {
    return false;
  }
  amount = convertMoney(amount);
  return int.parse(minAmount) <= int.parse(amount) && int.parse(amount) <= int.parse(maxAmount);
}

String convertMoney(String amountMoney) {
  return amountMoney.replaceAll("[^\\d]", "").trim();
}