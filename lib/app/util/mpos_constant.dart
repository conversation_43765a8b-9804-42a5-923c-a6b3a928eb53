class MposConstant {
  static const SUPPORT_PHONE = "1900-63-64-88";
  static const SUPPORT_EMAIL = "<EMAIL>";

  static const READER_NONE = 0;
  static const READER_AR01 = 1; //Audio reader
  static const READER_PR01 = 2; //Bluetooth reader
  static const READER_PR02 = 4; //Bluetooth reader
  static const READER_SP01 = 5; //SmartPos device
  static const READER_SP02 = 6; //mini SmartPos device + P8 (SmartPos pro)

  static const READER_QRCODE = 10;
  static const READER_LINKCARD = 11;
  static const ALL_TYPE = 12;

  static const String CARD_PAYMENT = "CARD_PAYMENT";
  static const String INSTALLMENT_CARD_PAYMENT = "INSTALLMENT_CARD_PAYMENT";
  static const String QR_PAYMENT = "QR_PAYMENT";
  static const String VAS_WALLET_PAYMENT = "VAS_WALLET_PAYMENT";
  static const String VAS_CARD_PAYMENT = "VAS_CARD_PAYMENT";
  static const String CARD_ENTER_PAYMENT = "CARD_ENTER_PAYMENT";
  static const String CREATE_LINK_PAYMENT = "CREATE_LINK_PAYMENT";
  static const String NAPAS_LOCAL = "NAPAS_LOCAL";
  static const String SAMSUNG_PAY_LOCAL = "SAMSUNG_PAY_LOCAL";
  static const String ATM_360 = "ATM_360";
  static const String UNSIGN_CARD_PAYMENT = "UNSIGN_CARD_PAYMENT"; //anhvt: dung cho case ky tiep

  static const double MIN_AMOUNT_SCAN = 50;
  // static const double MIN_AMOUNT_SCAN = 5000;
  static const double MAX_AMOUNT_SCAN = 100000000000;
  static const double MIN_AMOUNT_ENTER = 1000;
  static const double MAX_AMOUNT_ENTER = 100000000000;

  static const int PAYMENT_STATE_SUCCESS = 1;
  static const int PAYMENT_STATE_FAIL = 2;
  static const int PAYMENT_STATE_REVIEW = 3;

  static const String VAS_TELCO = 'TELCO';
  static const String VAS_NCOV = 'ncov';
  static const String VAS_COMMON = 'VAS_COMMON';

  static const String TELCO_PREPAID = 'prepaid';
  static const String TELCO_POSTPAID = 'postpaid';
  static const String TELCO_BUYCARD = 'buycard';

  static const String URL_LANDING_INSTALLMENT = 'https://www.mpos.vn/tra-gop-khong-phan-tram';
  static const String URL_CHECK_CARD_INSTALLMENT = 'https://kiemtrathe.mpos.vn/?params=';
  static const String URL_MPOS_GUIDE = 'https://chuyendoiso.nextpay.vn/mpos-guide/';
  static const String URL_DEFAULT = 'https://mpos.vn/';
  static const String URL_DEFAULT_LOGO = 'https://mpos.vn/';

  static const int CONNECTTYPE_NON = 0;
  static const int CONNECTTYPE_1_AMERCHANT_AMPOS_PAYMENT_AMERCHANT = 1;
  static const int CONNECTTYPE_2_AMERCHANT_SMPOS_PAYMENT_SMPOS = 2;
  static const int CONNECTTYPE_3_AMERCHANT_AMPOS_SMPOS_PAYMENT_SMPOS_AMERCHANT = 3;

  static const int MERCHANT_PAY_SWIPE_CARD = 1;
  static const int MERCHANT_PAY_QR_MVISA = 2;

  static const String PAYMENT_METHOD_TYPE_CARD = "CARD";
  static const String PAYMENT_METHOD_TYPE_QR_ALL = "QR_ALL";
  static const String PAYMENT_METHOD_TYPE_MVISA = "MVISA";
  static const String PAYMENT_METHOD_TYPE_LINKCARD = "LINKCARD";

  static const String PAYMENT_INIT_SOURCE_SCAN = "PAYMENT_INIT_SOURCE_SCAN";
  static const String PAYMENT_INIT_SOURCE_ENTER = "PAYMENT_INIT_SOURCE_ENTER";
  static const String PAYMENT_INIT_SOURCE_BUTTON = "PAYMENT_INIT_SOURCE_BUTTON";

  static const String BANNER_NEXT_LEND = "BANNER_NEXT_LEND";
  static const String BANNER_ACTIVE_360 = "BANNER_ACTIVE_360";

  static const String QR_TYPE_MVISA = "MVISA";
  static const String QR_TYPE_DOMESTIC = "VIMOQR";

  static const int BASIC_PAYMENT = 1;
  static const int SERVICE_PAYMENT = 2;

  static const String cardPayment = 'CARD_PAYMENT';
  static const String installmentPayment = 'INSTALLMENT_PAYMENT';
  static const String motoPayment = 'MOTO_MACQ_PAYMENT';
  static const String qrCodePayment = 'QRCode_PAYMENT';
  static const String vasPayment = 'VAS_PAYMENT';
  static const String linkCardPayment = 'LinkCard_PAYMENT';
  static const String ncovPayment = 'NCOV_PAYMENT';
  static const String nextLend = 'NEXT_LEND';
  static const String integratedPAYMENT = 'Integrated_PAYMENT';
  static const String bnplPayment = 'BNPL_PAYMENT';
  static const String history_tag = "HISTORY";
  static const String account_tag = "ACCOUNT";

  static const String PREFIX_DESCRIPTION_INSTALLMENT = "INSTALLMENT-";
  static const String PREFIX_DESCRIPTION_CASHBACK = "CASHBACK-";
  static const String PREFIX_DESCRIPTION_OPEN99 = "OPEN99-";
  static const String PREFIX_DESCRIPTION_SERVICE_PREPAID = "SERVICE-PREPAID";
  static const String PREFIX_DESCRIPTION_SERVICE_POSTPAID = "SERVICE-POSTPAID";
  static const String PREFIX_DESCRIPTION_SERVICE_BUY_CARD = "SERVICE-BUYCARD";
  static const String PREFIX_MOBILE_DESCRIPTION_SERVICE = "-";

  static const int AUTO_PRINT_DEFAULT = 1; // 0: NONE; 1: PRINT 1; 2: PRINT 2 RECEIPT

  static const String qr_name_VietQr = "VietQR";
  static const String name_group_qr_VAQR = "VAQR";
  static const String name_group_qr_QR_BANK = "QR_BANK";
  static const String name_group_qr_QR_QT = "QR_QT";
  static const String name_group_qr_QR_VI = "QR_VI";

  static const String TYPE_CARD             = "CARD";
  static const String TYPE_DEPOSIT          = "DEPOSIT";
  static const String TYPE_MOTO             = "MOTO";
  static const String TYPE_MOTO_DEPOSIT     = "MOTO_DEPOSIT";
  static const String TYPE_QRCODE           = "QRCODE";
  static const String TYPE_NPQR_VA          = "NPQR_VA";           //todo code emart_vietqr use: "QR_VAQR"

  static const String action_tcp_add_order            = 'ADD_ORDER';
  static const String action_tcp_void                 = 'VOID_TRANS';
  static const String action_tcp_cancel_order         = 'CANCEL_ORDER';
  static const String action_tcp_add_order_deposit    = 'ADD_DEPOSIT';
  static const String action_tcp_set_final_deposit    = 'SET_FINAL_DEPOSIT';
  static const String action_tcp_finish_deposit       = 'FINISH_DEPOSIT';
  static const String action_tcp_add_moto             = 'ADD_MOTO';
  static const String action_tcp_add_deposit_moto     = 'ADD_DEPOSIT_MOTO';

  // static const String action_tcp_add_order = 'action_tcp_add_order';
  // static const String action_tcp_void = 'action_tcp_void';
  // static const String action_tcp_cancel_order = 'action_tcp_cancel_order';
  static const String action_tcp_state_payment = 'action_tcp_state_payment';

  static const int ERROR_CANCEL_ORDER = -1;
  static const int ERROR_NOT_SUPPORT = -2;
  static const int ERROR_NOT_EXITS_SCREEN = -5;
  static const int ERROR_DATA_INVALID = -6;
  static const int ERROR_DEFAULT = -10;

  static const String TYPE_NOTI_QR_STATIC = 'STATIC_QR';
  static const String TYPE_NOTI_QR_DYNAMIC = 'DYNAMIC_QR';

}

//Khai báo các tên event gửi lên firebase để thống kê
//Cách đặt tên event phải tuân thủ đúng format:
//+ event mở màn hình
//+ event bấm nút
//+ event gọi API
//+ event lỗi API
//+ event lỗi SDK
//
class EventAnalytics {
  static const String ERROR_API = "ERROR_API";
  static const String ERROR_SDK = "ERROR_SDK";
  static const String ERROR_SDK_SCAN_CARD = "ERROR_SDK_SCAN_CARD";
}
