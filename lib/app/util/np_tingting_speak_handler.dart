import 'dart:developer' as dev;
import 'dart:io';

// import 'package:ffmpeg_kit_flutter_audio/ffmpeg_kit.dart';
// import 'package:ffmpeg_kit_flutter_audio/ffmpeg_session.dart';
// import 'package:ffmpeg_kit_flutter_audio/ffprobe_kit.dart';
// import 'package:ffmpeg_kit_flutter_audio/return_code.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/services.dart';
import 'package:just_audio/just_audio.dart';
import 'package:mposxs/app/data/provider/local_storage.dart';
import 'package:path_provider/path_provider.dart';

class NPTingTingSpeakerHandler {
  static final String soundNorth = "north";
  static final String soundSouth = "south";
  static final String pathSoundNorth = "lib/app/res/sound/north";
  static final String pathSoundSouth = "lib/app/res/sound/south";
  static String pathSoundCurrent = "";

  static final List<String> digitsName = [
    'NUM_ZERO_KHONG',
    'NUM_ONE_MOTJ',
    'NUM_TWO',
    'NUM_THREE',
    'NUM_FOUR_BON',
    'NUM_FIVE_NAM',
    'NUM_SIX',
    'NUM_SEVEN',
    'NUM_EIGHT',
    'NUM_NINE',
  ];

  static final List<String> thousandsName = [
    'SPACE',
    'NUM_THOUSAND',
    'NUM_MILLION',
    'NUM_BILLION',
  ];

  static List<String> _readTriple(String triplet, bool showZeroHundred) {
    if (triplet.length != 3) return [];

    List<String> lstEnum = [];

    int? a = int.tryParse(triplet[0]);
    int? b = int.tryParse(triplet[1]);
    int? c = int.tryParse(triplet[2]);

    if (a == null || b == null || c == null) return [];

    if (a == 0) {
      if (b == 0 && c == 0) {
        return lstEnum;
      }

      if (showZeroHundred) {
        lstEnum.add('NUM_ZERO_KHONG');
        lstEnum.add('NUM_HUNDRED');
        lstEnum.addAll(_readPair(b, c));
        return lstEnum;
      }

      if (b == 0) {
        lstEnum.add(digitsName[c]);
        return lstEnum;
      } else {
        return _readPair(b, c);
      }
    }

    lstEnum.add(digitsName[a]);
    lstEnum.add('NUM_HUNDRED');
    lstEnum.addAll(_readPair(b, c));
    return lstEnum;
  }

  static List<String> _readPair(int b, int c) {
    List<String> lstEnum = [];
    List<String> temp = [];

    switch (b) {
      case 0:
        if (c != 0) {
          lstEnum.add('NUM_AND');
          lstEnum.add(digitsName[c]);
        }
        return lstEnum;
      case 1:
        switch (c) {
          case 0:
            temp.add('SPACE');
            break;
          case 5:
            temp.add('NUM_FIVE_LAM');
            break;
          default:
            temp.add(digitsName[c]);
            break;
        }
        lstEnum.add('NUM_TEN_MUOIF');
        lstEnum.addAll(temp);
        return lstEnum;
      default:
        switch (c) {
          case 0:
            break;
          case 1:
            temp.add('NUM_ONE_MOTS');
            break;
          case 4:
            temp.add('NUM_FOUR_TU');
            break;
          case 5:
            temp.add('NUM_FIVE_LAM');
            break;
          default:
            temp.add(digitsName[c]);
            break;
        }
        lstEnum.add(digitsName[b]);
        lstEnum.add('NUM_ZERO_MUOI');
        lstEnum.addAll(temp);
        return lstEnum;
    }
  }

  static List<String> _numToArrayEnum(int num) {
    if (num == 0) {
      return ['NUM_ZERO_KHONG'];
    }

    if (num < 0) {
      List<String> arrNum = _numToArrayEnum(-num);
      return ['NUM_NEGATIVE', ...arrNum];
    }

    List<String> lstEnum = _convertToEnum(num);
    //  group number
    List<String> lstGroup = _groupNumber(lstEnum);
    return lstGroup;
  }

  static List<String> _convertToEnum(int num) {
    String str = num.toString();

    // zero padding in front of string to prepare for splitting
    switch (str.length % 3) {
      case 1:
        str = "00$str";
        break;
      case 2:
        str = "0$str";
        break;
      default:
        break;
    }

    // Split into chunks of 3 digits each
    List<String> groupOfThousand = _splitStringByLength(str, 3);

    bool showZeroHundred = _doShowZeroHundred(groupOfThousand);

    int index = 0;
    List<String> result = [];
    for (String item in groupOfThousand) {
      List<String> lstTripe = _readTriple(item, showZeroHundred && index > 0);
      for (String e in lstTripe) {
        if (e.isNotEmpty) {
          result.add(e);
        }
      }
      if (groupOfThousand.length - 1 - index > 0 && item != "000") {
        switch (groupOfThousand.length - 1 - index) {
          case 1:
          case 2:
          case 3:
            result.add(thousandsName[groupOfThousand.length - 1 - index]);
            break;
          case 4:
            result.add(thousandsName[1]);
            result.add(thousandsName[3]);
            break;
          case 5:
            result.add(thousandsName[2]);
            result.add(thousandsName[3]);
            break;
          case 6:
            result.add(thousandsName[3]);
            result.add(thousandsName[3]);
            break;
        }
      }
      index++;
    }
    return result;
  }

  static List<String> _splitStringByLength(String input, int length) {
    List<String> result = [];
    for (int i = 0; i < input.length; i += length) {
      result.add(input.substring(i, i + length > input.length ? input.length : i + length));
    }
    return result;
  }

  static bool _doShowZeroHundred(List<String> groupOfThousand) {
    int count = 0;
    int i = groupOfThousand.length - 1;
    while (i >= 0 && groupOfThousand[i] == "000") {
      count++;
      i--;
    }

    return count < groupOfThousand.length - 1;
  }

  static List<String> _groupNumber(List<String> lstEnum) {
    Map<String, String> map = {};
    map['NUM_ANDNUM_ONE_MOTJ'] = 'NUM_LINH_MOT';
    map['NUM_ANDNUM_TWO'] = 'NUM_LINH_HAI';
    map['NUM_ANDNUM_THREE'] = 'NUM_LINH_BA';
    map['NUM_ANDNUM_FOUR_BON'] = 'NUM_LINH_BON';
    map['NUM_ANDNUM_FIVE_NAM'] = 'NUM_LINH_NAM';
    map['NUM_ANDNUM_SIX'] = 'NUM_LINH_SAU';
    map['NUM_ANDNUM_SEVEN'] = 'NUM_LINH_BAY';
    map['NUM_ANDNUM_EIGHT'] = 'NUM_LINH_TAM';
    map['NUM_ANDNUM_NINE'] = 'NUM_LINH_CHIN';
    map['NUM_TEN_MUOIFNUM_ONE_MOTJ'] = 'NUM_MUOIF_MOTJ';
    map['NUM_TEN_MUOIFNUM_TWO'] = 'NUM_MUOIF_HAI';
    map['NUM_TEN_MUOIFNUM_THREE'] = 'NUM_MUOIF_BA';
    map['NUM_TEN_MUOIFNUM_FOUR_BON'] = 'NUM_MUOIF_BON';
    map['NUM_TEN_MUOIFNUM_FIVE_LAM'] = 'NUM_MUOIF_LAM';
    map['NUM_TEN_MUOIFNUM_SIX'] = 'NUM_MUOIF_SAU';
    map['NUM_TEN_MUOIFNUM_SEVEN'] = 'NUM_MUOIF_BAY';
    map['NUM_TEN_MUOIFNUM_EIGHT'] = 'NUM_MUOIF_TAM';
    map['NUM_TEN_MUOIFNUM_NINE'] = 'NUM_MUOIF_CHIN';
    map['NUM_TWONUM_ZERO_MUOI'] = 'NUM_HAI_MUOI';
    map['NUM_THREENUM_ZERO_MUOI'] = 'NUM_BA_MUOI';
    map['NUM_FOUR_BONNUM_ZERO_MUOI'] = 'NUM_BON_MUOI';
    map['NUM_FIVE_NAMNUM_ZERO_MUOI'] = 'NUM_NAM_MUOI';
    map['NUM_SIXNUM_ZERO_MUOI'] = 'NUM_SAU_MUOI';
    map['NUM_SEVENNUM_ZERO_MUOI'] = 'NUM_BAY_MUOI';
    map['NUM_EIGHTNUM_ZERO_MUOI'] = 'NUM_TAM_MUOI';
    map['NUM_NINENUM_ZERO_MUOI'] = 'NUM_CHIN_MUOI';
    map['NUM_TWONUM_ZERO_MUOINUM_ONE_MOTS'] = 'NUM_HAI_MUOI_MOTS';
    map['NUM_THREENUM_ZERO_MUOINUM_ONE_MOTS'] = 'NUM_BA_MUOI_MOTS';
    map['NUM_FOUR_BONNUM_ZERO_MUOINUM_ONE_MOTS'] = 'NUM_BON_MUOI_MOTS';
    map['NUM_FIVE_NAMNUM_ZERO_MUOINUM_ONE_MOTS'] = 'NUM_NAM_MUOI_MOTS';
    map['NUM_SIXNUM_ZERO_MUOINUM_ONE_MOTS'] = 'NUM_SAU_MUOI_MOTS';
    map['NUM_SEVENNUM_ZERO_MUOINUM_ONE_MOTS'] = 'NUM_BAY_MUOI_MOTS';
    map['NUM_EIGHTNUM_ZERO_MUOINUM_ONE_MOTS'] = 'NUM_TAM_MUOI_MOTS';
    map['NUM_NINENUM_ZERO_MUOINUM_ONE_MOTS'] = 'NUM_CHIN_MUOI_MOTS';
    map['NUM_TWONUM_ZERO_MUOINUM_FIVE_LAM'] = 'NUM_HAI_MUOI_LAM';
    map['NUM_THREENUM_ZERO_MUOINUM_FIVE_LAM'] = 'NUM_BA_MUOI_LAM';
    map['NUM_FOUR_BONNUM_ZERO_MUOINUM_FIVE_LAM'] = 'NUM_BON_MUOI_LAM';
    map['NUM_FIVE_NAMNUM_ZERO_MUOINUM_FIVE_LAM'] = 'NUM_NAM_MUOI_LAM';
    map['NUM_SIXNUM_ZERO_MUOINUM_FIVE_LAM'] = 'NUM_SAU_MUOI_LAM';
    map['NUM_SEVENNUM_ZERO_MUOINUM_FIVE_LAM'] = 'NUM_BAY_MUOI_LAM';
    map['NUM_EIGHTNUM_ZERO_MUOINUM_FIVE_LAM'] = 'NUM_TAM_MUOI_LAM';
    map['NUM_NINENUM_ZERO_MUOINUM_FIVE_LAM'] = 'NUM_CHIN_MUOI_LAM';
    map['NUM_ZERO_KHONGNUM_HUNDRED'] = 'NUM_ZERO_HUNDRED';
    map['NUM_ONE_MOTJNUM_HUNDRED'] = 'NUM_ONE_HUNDRED';
    map['NUM_TWONUM_HUNDRED'] = 'NUM_TWO_HUNDRED';
    map['NUM_THREENUM_HUNDRED'] = 'NUM_THREE_HUNDRED';
    map['NUM_FOUR_BONNUM_HUNDRED'] = 'NUM_FOUR_HUNDRED';
    map['NUM_FIVE_NAMNUM_HUNDRED'] = 'NUM_FIVE_HUNDRED';
    map['NUM_SIXNUM_HUNDRED'] = 'NUM_SIX_HUNDRED';
    map['NUM_SEVENNUM_HUNDRED'] = 'NUM_SEVEN_HUNDRED';
    map['NUM_EIGHTNUM_HUNDRED'] = 'NUM_EIGHT_HUNDRED';
    map['NUM_NINENUM_HUNDRED'] = 'NUM_NINE_HUNDRED';

    List<String> result = [];
    for (int i = 0; i < lstEnum.length; i++) {
      if (i < lstEnum.length - 2 && map.containsKey(lstEnum[i] + lstEnum[i + 1] + lstEnum[i + 2])) {
        result.add(map[lstEnum[i] + lstEnum[i + 1] + lstEnum[i + 2]] ?? '');
        i = i + 2;
      } else if (i < lstEnum.length - 1 && map.containsKey(lstEnum[i] + lstEnum[i + 1])) {
        result.add(map[lstEnum[i] + lstEnum[i + 1]] ?? '');
        i++;
      } else {
        result.add(lstEnum[i]);
      }
    }
    return result;
  }

  static final List<String> _listNotificationsToVoice = [];
  static final List<List<String>> _listPlayListVoice = [];
  static bool _isNotificationVoicing = false;

  static void playDemoVoice(String source){
    List<String> listSoundFile = [];
    if (source ==soundNorth) {
      listSoundFile.add('lib/app/res/sound/Switch_to_North.mp3');
    } else {
      listSoundFile.add('lib/app/res/sound/Switch_to_South.mp3');
    }
    _listPlayListVoice.add(listSoundFile);
    _playSoundByPlayList();
  }

  static Future<void> soundConnectSuccess() async {
    List<String> listSoundFile = [];
    String pathToSound = 'lib/app/res/sound';
    listSoundFile.add('$pathToSound/FW_SERVER-CONN-SUCCESS.mp3'); //lib/app/res/sound

    _listPlayListVoice.add(listSoundFile);
    if (!_isNotificationVoicing) {
      _playSoundByPlayList();
    }
  }

  static Future<void> receivedCurrencyAmountSound(int num,{bool hasMerge = true}) async {
    List<String> listSound = _numToArrayEnum(num);
    List<String> listSoundFile = [];
    String pathToSound = await getPathToSound();
    listSoundFile.add('$pathToSound/Received.mp3'); //lib/app/res/sound
    // listSoundFile.add('lib/app/res/sound/NUM_RECEIVED.mp3');
    for (var e in listSound) {
      if (e != 'SPACE') {
        listSoundFile.add('$pathToSound/$e.mp3');
      }
    }
    listSoundFile.add('$pathToSound/NUM_UNIT_VND.mp3');

    if (hasMerge) {
      _playSoundByMergeFile(listSoundFile);
    } else {
      _listPlayListVoice.add(listSoundFile);
      if (!_isNotificationVoicing) {
        _playSoundByPlayList();
      }
    }
  }

  static Future<void> _playSoundByPlayList() async {
    if(_listPlayListVoice.isEmpty) {
      _isNotificationVoicing = false;
      return;
    }
    _isNotificationVoicing = true;
    var listSoundFile = _listPlayListVoice.removeAt(0);

    Directory tempDir = await getTemporaryDirectory();
    List<String> tempFiles = await _writeTempFile(listSoundFile, tempDir);

    List<AudioSource> children = [];
    for(int i = 0; i < tempFiles.length; i++){
      children.add(AudioSource.file(tempFiles[i]));
    }
    final playlist = ConcatenatingAudioSource(
      // Start loading next item just before reaching it
      useLazyPreparation: true,
      // Customise the shuffle algorithm
      shuffleOrder: DefaultShuffleOrder(),
      // Specify the playlist items
      children: children,
    );
    final _player = AudioPlayer();
    _player.setAudioSource(playlist, initialIndex: 0, initialPosition: Duration.zero);
    dev.log('tempFiles.length=${tempFiles.length}');
    if(tempFiles.length>8) {
      _player.setSpeed(1.3);
    } else if(tempFiles.length>6) {
      _player.setSpeed(1.2);
    } else if(tempFiles.length>4) {
      _player.setSpeed(1.1);
    } else {
      _player.setSpeed(1);
    }
    _player.play();
    await Future.delayed(Duration(seconds: tempFiles.length + 1));
    _isNotificationVoicing = false;
    _playSoundByPlayList();
  }

  static void _playSoundByMergeFile(var listSoundFile) async {
    String filePth;
    try {
      filePth = await _mergeAssetsMp3(listSoundFile, 'soundTingTing${DateTime.now().microsecondsSinceEpoch}.mp3');
    } catch (e) {
      dev.log("----error in here: $e");
      throw e;
    }
    _listNotificationsToVoice.add(filePth);
    if (!_isNotificationVoicing) {
      _playNotificationSoundFile();
    }
  }

  static void _playNotificationSoundFile() async {
    // if (_listNotificationsToVoice.isEmpty) {
    //   _isNotificationVoicing = false;
    //   return;
    // }
    // _isNotificationVoicing = true;
    //
    // String soundPath = _listNotificationsToVoice.removeAt(0);
    // dev.log('1- soundPath=$soundPath');
    // final mediaInfoSession = await FFprobeKit.getMediaInformation(soundPath);
    // final mediaInfo = mediaInfoSession.getMediaInformation();
    // final _player = AudioPlayer();
    // _player.setAudioSource(AudioSource.file(soundPath));
    // _player.play();
    // // FlutterRingtonePlayer.play(fromAsset: soundPath, looping: false, volume: 1.0);
    //
    // await Future.delayed(Duration(seconds: (double.tryParse(mediaInfo?.getDuration() ?? '0.0') ?? 0.0).round() + 1));
    // _isNotificationVoicing = false;
    // _playNotificationSoundFile();
  }

  static Future<List<String>> _writeTempFile(List<String> assetPaths, Directory tempDir) async {
    List<String> tempFiles = [];

    for (String assetPath in assetPaths) {
      ByteData data = await rootBundle.load(assetPath);
      String tempFilePath = "${tempDir.path}/${assetPath.split('/').last}";

      File tempFile = File(tempFilePath);
      await tempFile.writeAsBytes(data.buffer.asUint8List());
      tempFiles.add(tempFilePath);
    }
    return tempFiles;
  }

  static Future<String> _mergeAssetsMp3(List<String> assetPaths, String outputFileName) async {
    Directory tempDir = await getTemporaryDirectory();
    dev.log('tempDir.path=${tempDir.path}');

    List<String> tempFiles = await _writeTempFile(assetPaths, tempDir);

    String outputFilePathMerge = "${tempDir.path}/merge-$outputFileName";
    String outputFilePathFinal = "${tempDir.path}/$outputFileName";

    String inputFileList = tempFiles.map((file) => "-i $file").join(" ");
    String command = "$inputFileList -filter_complex concat=n=${tempFiles.length}:v=0:a=1 -y $outputFilePathMerge";
    debugPrint('command: $command');
    // try {
    //   bool resultMerge = await _ffmpegCommandExecute(command);
    //   if (resultMerge) {
    //         String commandVol = '-i $outputFilePathMerge -filter:a "volume=1.5" -c:a libmp3lame -y $outputFilePathFinal';
    //         debugPrint('commandVol: $commandVol');
    //         bool resultVol = await _ffmpegCommandExecute(commandVol);
    //         if (resultVol) {
    //           return outputFilePathFinal;
    //         } else {
    //           return outputFilePathMerge;
    //         }
    //       }
    // } catch (e) {
    //   print(e);
    //   throw e;
    // }
    return outputFilePathFinal;
  }

  // static Future<bool> _ffmpegCommandExecute(String command) async {
  //   try {
  //     FFmpegSession session = await FFmpegKit.execute(command);
  //     ReturnCode? returnCode = await session.getReturnCode();
  //     if (returnCode?.isValueSuccess() == true) {
  //       debugPrint("---ffmpegCommandExecute: OK");
  //       return true;
  //     } else {
  //       String? logs = await session.getAllLogsAsString();
  //       debugPrint("---ffmpegCommandExecute: ERROR: $logs");
  //     }
  //   } catch (e) {
  //     print(e);
  //     throw e;
  //   }
  //   return false;
  // }
  // static Future<bool> _ffmpegCommandExecute(String command) async {
  //   FFmpegSession session = await FFmpegKit.execute(command);
  //   ReturnCode? returnCode = await session.getReturnCode();
  //   if (returnCode?.isValueSuccess() == true) {
  //     debugPrint("---ffmpegCommandExecute: OK");
  //     return true;
  //   } else {
  //     String? logs = await session.getAllLogsAsString();
  //     debugPrint("---ffmpegCommandExecute: ERROR: $logs");
  //     return false;
  //   }
  // }

  static void setPathToSound(String langSound){
    if (langSound == soundSouth) {
      pathSoundCurrent = pathSoundSouth;
    }
    else {
      pathSoundCurrent = pathSoundNorth;
    }
  }
  static Future<String> getPathToSound() async {
    if (pathSoundCurrent.isNotEmpty) {
      return pathSoundCurrent;
    } else {
      LocalStorage localStorage = LocalStorage();
      String currLangSound = await localStorage.getLanguageSoundTingbox();
      if (currLangSound == soundSouth) {
        pathSoundCurrent = pathSoundSouth;
      }
      else {
        pathSoundCurrent = pathSoundNorth;
      }
    }
    return pathSoundNorth;
  }
}
