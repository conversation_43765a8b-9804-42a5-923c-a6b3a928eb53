import 'dart:convert';
import 'dart:io';
import 'dart:math';

import 'package:credit_card_type_detector/credit_card_type_detector.dart';
import 'package:device_apps/device_apps.dart';
// import 'package:device_info/device_info.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/data/model/qr_data_config.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/widget/dialog_alert.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../build_constants.dart';
import '../data/model/mp_data_login_model.dart';
import 'app_validation.dart';
import 'mpos_constant.dart';

class AppUtils {
  static int? random(min, max) {
    var rn = new Random();
    return min + rn.nextInt(max - min);
  }

  static void hideKeyboard(context) {
    FocusScopeNode currentFocus = FocusScope.of(context);
    if (!currentFocus.hasPrimaryFocus) {
      currentFocus.unfocus();
    }
  }

  static String convertPhoneToOrigin(String phone) {
    String phoneNumber = phone.trim();
    if (phoneNumber.length != 0) {
      phoneNumber = phoneNumber.replaceFirst('(+84)', '0');
      phoneNumber = phoneNumber.replaceFirst('+84', '0');
      // phoneNumber = phoneNumber.replace('0084', '0');
      phoneNumber = phoneNumber.replaceAll(' ', '');
      // phoneNumber = phoneNumber.replace(/[^0-9]/g, '');
    }
    return phoneNumber;
  }

  static void log(String text) {
    if (BuildConstants.currentEnvironment != Environment.PROD) {
      final pattern = new RegExp('.{1,800}');
      pattern.allMatches(text).forEach((match) => debugPrint(match.group(0)));
    }
  }

  static String? formatPhoneNumber(String? text) {
    String? filterText = text;
    if (isNullEmpty(filterText)) return '';
    if (filterText!.length < 2) return filterText;
    filterText = filterText.replaceAll(' ', '');
    String firstChars = filterText.substring(0, 2);
    if (firstChars == '09' || firstChars == '08' || firstChars == '07' || firstChars == '03' || firstChars == '05') {
      if (filterText.length > 3) {
        filterText = filterText.substring(0, 3) + ' ' + filterText.substring(3);
      }
      if (filterText.length > 7) {
        filterText = filterText.substring(0, 7) + ' ' + filterText.substring(7);
      }
    }
    return filterText.trim();
  }

  static Future<bool> isSimulatorDevice() async {
    DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    if (Platform.isIOS) {
      IosDeviceInfo iosDeviceInfo = await deviceInfo.iosInfo;
      return !iosDeviceInfo.isPhysicalDevice;
    } else {
      AndroidDeviceInfo androidDeviceInfo = await deviceInfo.androidInfo;
      return !androidDeviceInfo.isPhysicalDevice;
    }
  }

  static void openCallPhoneSupport() async {
    const url = 'tel:${MposConstant.SUPPORT_PHONE}';
    if (await canLaunch(url)) {
      await launch(url);
    } else {
      throw 'Could not launch $url';
    }
  }

  static void openAppByPackageName(String package) async{
    try {
      ///checks if the app is installed on your mobile device
      bool isInstalled = await DeviceApps.isAppInstalled(package);
      if (isInstalled) {
        DeviceApps.openApp(package);
      }
    } catch (e) {
      print(e);
    }
  }

  static void launchMailHotroMpos() async {
    String toMailId = MposConstant.SUPPORT_EMAIL;
    String subject = 'Request Support';
    String body = '';
    launchMail(toMailId, subject, body);
  }
  static void launchMail(String toMailId, String subject, String body) async {
    final Uri _emailLaunchUri = Uri(
        scheme: 'mailto', path: toMailId,
        // query: "subject=$subject&body=$body"
      // query: encodeQueryParameters(<String, String>{
      //   'subject': 'Example Subject & Symbols are allowed!',
      // }),
    );


    if (await canLaunchUrl(_emailLaunchUri)) {
      await launchUrl(_emailLaunchUri);
    } else {
      throw 'Could not launch $_emailLaunchUri';
    }
    // String a = _emailLaunchUri
    //     .toString()
    //     .replaceAll("+", "%20")
    //     .replaceAll("%2520", "%20");
    //
    // if (await canLaunch(a)) {
    //   await launch(a);
    // } else {
    //   throw 'Could not launch $a';
    // }
  }

  static showDialogError(BuildContext context, String? errText,
      {String? title, Function? onPressButton, String? image, TextAlign? descriptionTextAlign}) {

    if (context == null) {
      print('Cannot show dialog: context is null. Message: $errText');
      return;
    }

    if (!context.mounted) {
      print('Cannot show dialog: context is not mounted. Message: $errText');
      return;
    }
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return DialogAlert(
            image: image ?? null,
            onPress1stButton: onPressButton ?? null,
            title: title ?? AppStrings.getString(AppStrings.errorTitle),
            description: errText,
            descriptionTextAlign: descriptionTextAlign ?? null,
            text1stButton: AppStrings.getString(AppStrings.close),
          );
        });
  }

  static String getErrorByCode(BuildContext? context, var code, {String? defaultMessage}) {
    String? messageShow = '';
    String? messageMapping = AppStrings.getString('error_$code');
    if (code == 'HTTP Error' || code == 'Unknown') {
      messageShow = '$code: ${AppStrings.getString(AppStrings.networkError)}';
    } else {
      if (messageMapping == 'error_$code') {
        messageMapping = '';
      }
      if (!isNullEmpty(messageMapping)) {
        messageShow = isNullEmpty(code) ? messageMapping! : '$code: $messageMapping';
      } else if (!isNullEmpty(defaultMessage)) {
        messageShow = isNullEmpty(code) ? defaultMessage! : '$code: $defaultMessage';
      } else {
        messageShow = isNullEmpty(code)
            ? AppStrings.getString(AppStrings.errorUnknown)!
            : '$code: ${AppStrings.getString(AppStrings.errorUnknown)}';
      }
    }
    return messageShow;
  }

  static showDialogErrorNative(BuildContext context, PlatformException? platformException,
      {String? title, Function? onPressButton}) {
    var code = platformException?.code;
    String? message = platformException?.message;
    String? alertTitle = title ?? AppStrings.getString(AppStrings.errorTitle);
    if (BuildConstants.currentEnvironment != Environment.PROD) {
      alertTitle = '${alertTitle??'Notice'} (SDK)';
    }
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return DialogAlert(
            onPress1stButton: onPressButton ?? null,
            title: alertTitle,
            description: getErrorByCode(context, code, defaultMessage: message),
            text1stButton: AppStrings.getString(AppStrings.close),
          );
        });
  }

  static showDialogAlert(BuildContext context,
      {String? title,
      String? description,
      TextAlign? descriptionTextAlign,
      String? text1stButton,
      String? text2ndButton,
      Function? onPress1stButton,
      Function? onPress2ndButton,
      bool? isTwoButton,
      Widget? widgetDescription,
      String? image}) {
    showDialog(
        context: context,
        builder: (BuildContext context) {
          return DialogAlert(
            title: title,
            description: description,
            descriptionTextAlign: descriptionTextAlign,
            text1stButton: text1stButton,
            text2ndButton: text2ndButton,
            onPress1stButton: onPress1stButton,
            onPress2ndButton: onPress2ndButton,
            isTwoButton: isTwoButton,
            widgetDescription: widgetDescription,
            image: image,
          );
        });
  }

  static String formatCurrency(dynamic number) {
    if (isNullEmptyFalseOrZero(number) || !isNumeric(number)) {
      return '0';
    }
    dynamic numberConvert;
    if (number is String) {
      numberConvert = int.tryParse(number) ?? double.tryParse(number);
    } else {
      numberConvert = number;
    }
    return NumberFormat("#,###", "vi_VN").format(numberConvert ?? 0);
  }

  static String format4CharNumber(String text) {
    String filterText = text;
    if (isNullEmpty(filterText)) return '';
    final value = filterText.replaceAllMapped(RegExp(r".{4}"), (match) => "${match.group(0)} ");
    return value.trim();
  }

  static String? getCardTypeImage(var type) {
    switch (type) {
      case CreditCardType.amex:
        return AppImages.icCardAmex;
      case CreditCardType.jcb:
        return AppImages.icCardJCB;
      case CreditCardType.mastercard:
        return AppImages.icCardMaster;
      case CreditCardType.unionpay:
        return AppImages.icCardUnionPay;
      case CreditCardType.visa:
        return AppImages.icCardVisa;
      default:
        return null;
    }
  }

  static bool compareTimeSameDate(int date1, int date2){
    //
    String sDate1 = DateFormat('dd/MM/yyyy').format(DateTime.fromMillisecondsSinceEpoch(date1));
    String sDate2 = DateFormat('dd/MM/yyyy').format(DateTime.fromMillisecondsSinceEpoch(date2));
    // dev.log('sDate1=$sDate1 -- sDate2=$sDate2');
    if(sDate1 == sDate2) {
      return true;
    }
    return false;
  }

  static int getNextFibonacciByPosition(int position) {
    if (position <= 1) {
      return position;
    }
    int previous = 0;
    int current = 1;
    for (int i = 2; i <= position; i++) {
      int next = previous + current;
      previous = current;
      current = next;
    }
    return current;
  }

  static QrChildren getQRChidWithMethodPayment(String configQr, String paymentMethod) {
    try {
      final Map responseMap = json.decode(configQr);
      QrDataConfig _qrDataConfig = QrDataConfig.fromJson(responseMap as Map<String, dynamic>);
      for (var element in _qrDataConfig.data!) {
        for (var element1 in element.qrChildren!) {
          if (element1.qrType == paymentMethod) {
            print('ufoooo ${element1.shortNameChild ?? ''} --- ${element1.qrType}');
            return element1;
          }
        }
      }
    }catch (e) {
      MyAppController().appendLogError('getQRChidWithMethodPayment $e');
    }
    return new QrChildren();
  }

  String checkFeeQuickDraw(String amount, List<MPJsonQuickWithdrawList> listQuickDraw) {
    String amountConvert = convertMoney(amount);
    // List<MPJsonQuickWithdrawList> listQuickDraw = _appController.userInfo!.quickWithdrawInfo!.jsonQuickWithdrawList!;
    for (int i = 0; i < listQuickDraw.length; i++) {
      try {
        int minAmount = int.parse(listQuickDraw[i].amountMin ?? '0');
        int maxAmount = int.parse(listQuickDraw[i].amountMax ?? '0');
        if (minAmount <= int.parse(amountConvert) && int.parse(amountConvert) <= maxAmount) {
          num flatFee = num.parse(listQuickDraw[i].flatFee ?? '0');
          num percentageFee = listQuickDraw[i].percentageFee ?? 0;
          return calculatorMoneyForService(int.parse(amountConvert), flatFee, percentageFee);
        }
      }catch (e) {
        MyAppController().appendLogError('checkFeeQuickDraw err $e');
      }
    }
    return "";
  }

  String calculatorMoneyForService(int amount, num flatFee, num precentFee) {
    /**
     *  PHI_DICH_VU = PHI_CO_DINH (Ex: 35000) + PHAN_TRAM_TREN_TONG_TIEN (Ex: 0.5%)
     */
    num amount_a = precentFee * amount;
    num amount_b = (1.0 * amount_a / 100).round();
    amount_b = amount_b + flatFee;
    return amount_b.toString();
  }
}
