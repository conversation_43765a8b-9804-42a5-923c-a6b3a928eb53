import 'app_validation.dart';

class NumberToString {
  var _sequence = ['không', 'một', 'hai', 'ba', 'bốn', 'năm', 'sáu', 'bảy', 'tám', 'chín'];
  var _tensNamesEn = ["", " ten", " twenty", " thirty", " forty", " fifty", " sixty", " seventy", " eighty", " ninety"];
  var _numNamesEn = [
    "",
    " one",
    " two",
    " three",
    " four",
    " five",
    " six",
    " seven",
    " eight",
    " nine",
    " ten",
    " eleven",
    " twelve",
    " thirteen",
    " fourteen",
    " fifteen",
    " sixteen",
    " seventeen",
    " eighteen",
    " nineteen"
  ];

  //Doc hang chuc
  _readDozens(number, ranged) {
    String string = '';
    var dozen = (number / 10).floor(); //hang chuc
    var units = number % 10; //Don vi
    if (dozen > 1) {
      string = " " + _sequence[dozen] + " mươi";
      if (units == 1) {
        string += " mốt";
      }
    } else if (dozen == 1) {
      string = " mười";
      if (units == 1) {
        string += " một";
      }
    } else if (ranged && units > 0) {
      string = " lẻ";
    }
    if (units == 5 && dozen >= 1) {
      string += " lăm";
    } else if (units == 4 && dozen >= 1) {
      string += " tư";
    } else if (units > 1 || (units == 1 && dozen == 0)) {
      string += " " + _sequence[units];
    }
    return string;
  }

  //So va day du
  _readBlock(number, ranged) {
    String string = "";
    var hundreds = (number / 100).floor(); //Hang tram
    number = number % 100;
    if (ranged == true || hundreds > 0) {
      string = " " + _sequence[hundreds] + " trăm";
      string += _readDozens(number, true);
    } else
      string = _readDozens(number, false);

    return string;
  }

  //So va day du
  _readMillion(number, ranged) {
    String string = "";
    var million = (number / 1000000).floor(); //Trieu
    number = number % 1000000;
    if (million > 0) {
      string = _readBlock(million, ranged) + " triệu";
      ranged = true;
    }
    var thousands = (number / 1000).floor(); //Hang nghin
    number = number % 1000;
    if (thousands > 0) {
      string += _readBlock(thousands, ranged) + " nghìn";
      ranged = true;
    }
    if (number > 0) string += _readBlock(number, ranged);

    return string;
  }

  readNumber(number) {
    if (number == 0) {
      return _sequence[0];
    }
    String? string = "";
    String postfix = ""; //Hậu tố
    do {
      var billion = number % 1000000000; //Tỷ
      number = (number / 1000000000).floor();

      if (number > 0)
        string = _readMillion(billion, true) + postfix + string;
      else
        string = _readMillion(billion, false) + postfix + string;

      postfix = " tỷ";
    } while (number > 0);

    string = (string! + ' đồng').substring(1);
    string = "${string[0].toUpperCase()}${string.substring(1)}";
    return string;
  }

  readCurrency(String numberString) {
    if (isNullEmpty(numberString)) {
      return _sequence[0];
    }
    List<String> numberListString = numberString.split('.');
    List<int> numberList = [];
    if (numberListString != null) {
      for (var i = 0; i < numberListString.length; i++) {
        numberList.add(int.tryParse(numberListString[i]) ?? 0);
      }
    }
    String? stringL = "";
    String? stringR = "";
    var postfix = ""; //Hậu tố
    do {
      var billion = numberList[0] % 1000000000; //Tỷ
      numberList[0] = (numberList[0] / 1000000000).floor();
      if (numberList[0] > 0)
        stringL = _readMillion(billion, true) + postfix + stringL;
      else
        stringL = _readMillion(billion, false) + postfix + stringL;
      postfix = " tỷ";
    } while (numberList[0] > 0);
    if (numberList[1] != null) {
      postfix = ""; //Hậu tố
      do {
        var billion = numberList[1] % 1000000000; //Tỷ
        numberList[1] = (numberList[1] / 1000000000).floor();
        if (numberList[1] > 0)
          stringR = _readMillion(billion, true) + postfix + stringR;
        else
          stringR = _readMillion(billion, false) + postfix + stringR;
        postfix = " tỷ";
      } while (numberList[1] > 0);

      return "${stringL![1].toUpperCase()}${stringL.substring(2)}" + ' phẩy ' + stringR!.substring(1) + ' ' + 'đồng';
    }
    return "${stringL![1].toUpperCase()}${stringL.substring(2)}" + ' ' + 'đồng';
  }

  String _convertLessThanOneThousand(int number) {
    String soFar;
    if (number % 100 < 20) {
      soFar = _numNamesEn[number % 100];
      number ~/= 100;
    } else {
      soFar = _numNamesEn[number % 10];
      number ~/= 10;

      soFar = _tensNamesEn[number % 10] + soFar;
      number ~/= 10;
    }
    if (number == 0) return soFar;
    return _numNamesEn[number] + " hundred" + soFar;
  }

  String readNumberEn(number) {
    // 0 to 999 999 999 999
    if (number == 0) {
      return "zero";
    }
    String snumber = number.toString();

    // pad with "0"
    if (snumber.length < 12) {
      snumber = List.filled(12 - snumber.length, '0').join() + snumber;
    }

    // XXXnnnnnnnnn
    int? billions = int.tryParse(snumber.substring(0, 3));
    // nnnXXXnnnnnn
    int? millions = int.tryParse(snumber.substring(3, 6));
    // nnnnnnXXXnnn
    int? hundredThousands = int.tryParse(snumber.substring(6, 9));
    // nnnnnnnnnXXX
    int? thousands = int.tryParse(snumber.substring(9, 12));

    String tradBillions;
    switch (billions) {
      case 0:
        tradBillions = "";
        break;
      case 1:
        tradBillions = _convertLessThanOneThousand(billions!) + " billion";
        break;
      default:
        tradBillions = _convertLessThanOneThousand(billions!) + " billion";
    }
    String result = tradBillions;

    String tradMillions;
    switch (millions) {
      case 0:
        tradMillions = "";
        break;
      case 1:
        tradMillions = _convertLessThanOneThousand(millions!) + " million";
        break;
      default:
        tradMillions = _convertLessThanOneThousand(millions!) + " million";
    }
    result = result + tradMillions;

    String tradHundredThousands;
    switch (hundredThousands) {
      case 0:
        tradHundredThousands = "";
        break;
      case 1:
        tradHundredThousands = "one thousand";
        break;
      default:
        tradHundredThousands = _convertLessThanOneThousand(hundredThousands!) + " thousand";
    }
    result = result + tradHundredThousands;

    String tradThousand;
    tradThousand = _convertLessThanOneThousand(thousands!);
    result = result + tradThousand;

    // remove extra spaces!
    return result.replaceAll("^\\s+", "").replaceAll("\\b\\s{2,}\\b", " ") + " Dong";
  }
}
