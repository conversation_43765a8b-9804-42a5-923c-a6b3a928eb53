import 'package:get/get.dart';
import 'package:intl/intl.dart';

import 'en_strings.dart';
import 'vi_strings.dart';

class AppStrings extends Translations {
  static final String localeCodeVi = 'vi_VN';
  static final String localeCodeEn = 'en_US';

  static final String language_code_vi = 'vi';

  @override
  Map<String, Map<String, String>> get keys => {
        localeCodeVi: viStrings,
        localeCodeEn: enStrings,
      };

  static String? getString(String key) {
    Map<String, String> selectedLanguage = Get.locale.toString() == localeCodeEn ? enStrings : viStrings;
    return selectedLanguage != null && selectedLanguage.containsKey(key) && selectedLanguage[key] != null
        ? selectedLanguage[key]
        : '';
  }

  static String? getPluralSecond(int count){
    String key = Intl.plural(count, one: pluralOneSecond, other: pluralOtherSecond);
    print('getPluralSecond key=$key');

    return getString(key);
  }

  static String waitingTransTCP = 'waitingTransTCP';
  static String titleNotice = 'titleNotice';
  static String titleWarning = 'titleWarning';
  static String accountNameEmpty = 'accountNameEmpty';
  static String accountNameTooLong = 'accountNameTooLong';
  static String passwordEmpty = 'passwordEmpty';
  static String passwordTooLong = 'passwordTooLong';
  static String selectReader = 'selectReader';
  static String noteReaderConnectViaBluetooth = 'noteReaderConnectViaBluetooth';
  static String noteReaderConnectViaAudio = 'noteReaderConnectViaAudio';
  static String titleConnectDevice = 'titleConnectDevice';
  static String titleLoginNotDevice = 'titleLoginNotDevice';
  static String titleCallSupport = 'titleCallSupport';
  static String titleBackToSelectReader = 'titleBackToSelectReader';
  static String titleReSelectReader = 'titleReSelectReader';
  static String titleForgetPassword = 'titleForgetPassword';
  static String titleForgetPassword2 = 'titleForgetPassword2';
  static String titleForgetPasswordDialog = 'titleForgetPasswordDialog';
  static String hintForgetPasswordDialog = 'hintForgetPasswordDialog';
  static String labelForgetPasswordDialog = 'labelForgetPasswordDialog';
  static String paymentStateSuccess = 'paymentStateSuccess';
  static String paymentStateFail = 'paymentStateFail';
  static String paymentStateReview = 'paymentStateReview';

  static String titleNhanTienNhanh = 'titleNhanTienNhanh';
  static String titleTaoGiaoDichKhac = 'titleTaoGiaoDichKhac';
  static String titleTaoGiaoDichKhac2 = 'titleTaoGiaoDichKhac2';
  static String titleTaoLaiGiaoDich = 'titleTaoLaiGiaoDich';
  static String titleGuiBienNhan = 'titleGuiBienNhan';
  static String titleHotlineHoTro = 'titleHotlineHoTro';

  static String account = 'account';
  static String amountCardValue = 'amountCardValue';
  static String amountCashMinusInCard = 'amountCashMinusInCard';
  static String amountPay = 'amountPay';
  static String amountTopup = 'amountTopup';
  static String atmCard = 'atmCard';
  static String backHome = 'backHome';
  static String backHome2 = 'backHome2';
  static String cancel = 'cancel';
  static String cardValue = 'cardValue';
  static String check = 'check';
  static String close = 'close';
  static String completeTransaction = 'completeTransaction';
  static String confirmPay = 'confirmPay';
  static String consumerLoan = 'consumerLoan';
  static String content = 'content';
  static String contentPromotion1 = 'contentPromotion1';
  static String contentPromotion2 = 'contentPromotion2';
  static String contentWarningBackVM = 'contentWarningBackVM';
  static String continueText = 'continueText';
  static String copyLink = 'copyLink';
  static String createLink = 'createLink';
  static String createNewTransaction = 'createNewTransaction';
  static String currencyVND = 'currencyVND';
  static String currencyVNDShort = 'currencyVNDShort';
  static String customerInfo = 'customerInfo';
  static String day = 'day';
  static String description = 'description';
  static String descriptionPlaceHolder = 'descriptionPlaceHolder';
  static String deviceId = 'deviceId';
  static String doNotSupportDebitCard = 'doNotSupportDebitCard';
  static String email = 'email';
  static String emptyDescription = 'emptyDescription';
  static String emptyEmail = 'emptyEmail';
  static String emptyIdentity = 'emptyIdentity';
  static String emptyPhone = 'emptyPhone';
  static String emptyPhoneAndEmail = 'emptyPhoneAndEmail';
  static String emptyPhoneInstallment = 'emptyPhoneInstallment';
  static String emptyTopupItem = 'emptyTopupItem';
  static String emptyTopupPhone = 'emptyTopupPhone';
  static String enterCode = 'enterCode';
  static String enterCustomerCode = 'enterCustomerCode';
  static String enterPhone = 'enterPhone';
  static String errorCallApiResponseTitle = 'errorCallApiResponseTitle';
  static String errorCallApiTitle = 'errorCallApiTitle';
  static String errorQRVaymuon = 'errorQRVaymuon';
  static String errorTitle = 'errorTitle';
  static String error_1 = 'error_1';
  static String error_10 = 'error_10';
  static String error_1000 = 'error_1000';
  static String error_10000 = 'error_10000';
  static String error_10001 = 'error_10001';
  static String error_10004 = 'error_10004';
  static String error_10006 = 'error_10006';
  static String error_10007 = 'error_10007';
  static String error_1001 = 'error_1001';
  static String error_1002 = 'error_1002';
  static String error_1003 = 'error_1003';
  static String error_1004 = 'error_1004';
  static String error_1005 = 'error_1005';
  static String error_1006 = 'error_1006';
  static String error_1007 = 'error_1007';
  static String error_1008 = 'error_1008';
  static String error_1009 = 'error_1009';
  static String error_1010 = 'error_1010';
  static String error_1011 = 'error_1011';
  static String error_1012 = 'error_1012';
  static String error_1013 = 'error_1013';
  static String error_1014 = 'error_1014';
  static String error_1015 = 'error_1015';
  static String error_1016 = 'error_1016';
  static String error_1017 = 'error_1017';
  static String error_11 = 'error_11';
  static String error_11000 = 'error_11000';
  static String error_110001 = 'error_110001';
  static String error_110002 = 'error_110002';
  static String error_11001 = 'error_11001';
  static String error_11002 = 'error_11002';
  static String error_11003 = 'error_11003';
  static String error_11004 = 'error_11004';
  static String error_12 = 'error_12';
  static String error_12000 = 'error_12000';
  static String error_12001 = 'error_12001';
  static String error_12002 = 'error_12002';
  static String error_12003 = 'error_12003';
  static String error_12100 = 'error_12100';
  static String error_12200 = 'error_12200';
  static String error_12300 = 'error_12300';
  static String error_13 = 'error_13';
  static String error_13000 = 'error_13000';
  static String error_14 = 'error_14';
  static String error_14000 = 'error_14000';
  static String error_14001 = 'error_14001';
  static String error_14002 = 'error_14002';
  static String error_14003 = 'error_14003';
  static String error_14004 = 'error_14004';
  static String error_15 = 'error_15';
  static String error_16 = 'error_16';
  static String error_16002 = 'error_16002';
  static String error_17 = 'error_17';
  static String error_17000 = 'error_17000';
  static String error_18 = 'error_18';
  static String error_19 = 'error_19';
  static String error_19000 = 'error_19000';
  static String error_2 = 'error_2';
  static String error_20 = 'error_20';
  static String error_2000 = 'error_2000';
  static String error_20000 = 'error_20000';
  static String error_2001 = 'error_2001';
  static String error_2002 = 'error_2002';
  static String error_21 = 'error_21';
  static String error_22 = 'error_22';
  static String error_23 = 'error_23';
  static String error_24 = 'error_24';
  static String error_25 = 'error_25';
  static String error_26 = 'error_26';
  static String error_27 = 'error_27';
  static String error_28 = 'error_28';
  static String error_29 = 'error_29';
  static String error_3 = 'error_3';
  static String error_30 = 'error_30';
  static String error_3000 = 'error_3000';
  static String error_30001 = 'error_30001';
  static String error_3001 = 'error_3001';
  static String error_3002 = 'error_3002';
  static String error_3003 = 'error_3003';
  static String error_3004 = 'error_3004';
  static String error_3005 = 'error_3005';
  static String error_3011 = 'error_3011';
  static String error_3012 = 'error_3012';
  static String error_3020 = 'error_3020';
  static String error_3021 = 'error_3021';
  static String error_3030 = 'error_3030';
  static String error_3031 = 'error_3031';
  static String error_3032 = 'error_3032';
  static String error_3040 = 'error_3040';
  static String error_3041 = 'error_3041';
  static String error_3043 = 'error_3043';
  static String error_31 = 'error_31';
  static String error_3100 = 'error_3100';
  static String error_3101 = 'error_3101';
  static String error_3102 = 'error_3102';
  static String error_32 = 'error_32';
  static String error_3200 = 'error_3200';
  static String error_3201 = 'error_3201';
  static String error_33 = 'error_33';
  static String error_3300 = 'error_3300';
  static String error_3301 = 'error_3301';
  static String error_34 = 'error_34';
  static String error_35 = 'error_35';
  static String error_36 = 'error_36';
  static String error_37 = 'error_37';
  static String error_38 = 'error_38';
  static String error_39 = 'error_39';
  static String error_4 = 'error_4';
  static String error_40 = 'error_40';
  static String error_4000 = 'error_4000';
  static String error_4001 = 'error_4001';
  static String error_4002 = 'error_4002';
  static String error_4003 = 'error_4003';
  static String error_4004 = 'error_4004';
  static String error_4005 = 'error_4005';
  static String error_4006 = 'error_4006';
  static String error_4007 = 'error_4007';
  static String error_41 = 'error_41';
  static String error_4106 = 'error_4106';
  static String error_42 = 'error_42';
  static String error_43 = 'error_43';
  static String error_44 = 'error_44';
  static String error_45 = 'error_45';
  static String error_45000 = 'error_45000';
  static String error_45001 = 'error_45001';
  static String error_45002 = 'error_45002';
  static String error_45003 = 'error_45003';
  static String error_46 = 'error_46';
  static String error_47 = 'error_47';
  static String error_48 = 'error_48';
  static String error_49 = 'error_49';
  static String error_5 = 'error_5';
  static String error_50 = 'error_50';
  static String error_5000 = 'error_5000';
  static String error_50001 = 'error_50001';
  static String error_50002 = 'error_50002';
  static String error_50003 = 'error_50003';
  static String error_50004 = 'error_50004';
  static String error_50005 = 'error_50005';
  static String error_50006 = 'error_50006';
  static String error_50007 = 'error_50007';
  static String error_50008 = 'error_50008';
  static String error_5001 = 'error_5001';
  static String error_5002 = 'error_5002';
  static String error_5003 = 'error_5003';
  static String error_5004 = 'error_5004';
  static String error_5005 = 'error_5005';
  static String error_5006 = 'error_5006';
  static String error_5010 = 'error_5010';
  static String error_5011 = 'error_5011';
  static String error_5012 = 'error_5012';
  static String error_5013 = 'error_5013';
  static String error_5014 = 'error_5014';
  static String error_5020 = 'error_5020';
  static String error_51 = 'error_51';
  static String error_5110 = 'error_5110';
  static String error_5111 = 'error_5111';
  static String error_5112 = 'error_5112';
  static String error_5114 = 'error_5114';
  static String error_5115 = 'error_5115';
  static String error_5120 = 'error_5120';
  static String error_5131 = 'error_5131';
  static String error_5132 = 'error_5132';
  static String error_52 = 'error_52';
  static String error_53 = 'error_53';
  static String error_54 = 'error_54';
  static String error_55 = 'error_55';
  static String error_55000 = 'error_55000';
  static String error_5555 = 'error_5555';
  static String error_56 = 'error_56';
  static String error_57 = 'error_57';
  static String error_58 = 'error_58';
  static String error_59 = 'error_59';
  static String error_6 = 'error_6';
  static String error_60 = 'error_60';
  static String error_6000 = 'error_6000';
  static String error_60000 = 'error_60000';
  static String error_61 = 'error_61';
  static String error_62 = 'error_62';
  static String error_63 = 'error_63';
  static String error_64 = 'error_64';
  static String error_65 = 'error_65';
  static String error_66 = 'error_66';
  static String error_67 = 'error_67';
  static String error_68 = 'error_68';
  static String error_69 = 'error_69';
  static String error_7 = 'error_7';
  static String error_70 = 'error_70';
  static String error_7000 = 'error_7000';
  static String error_7001 = 'error_7001';
  static String error_7002 = 'error_7002';
  static String error_7003 = 'error_7003';
  static String error_7004 = 'error_7004';
  static String error_7005 = 'error_7005';
  static String error_7006 = 'error_7006';
  static String error_7007 = 'error_7007';
  static String error_7008 = 'error_7008';
  static String error_7009 = 'error_7009';
  static String error_71 = 'error_71';
  static String error_72 = 'error_72';
  static String error_73 = 'error_73';
  static String error_74 = 'error_74';
  static String error_75 = 'error_75';
  static String error_76 = 'error_76';
  static String error_8 = 'error_8';
  static String error_8001 = 'error_8001';
  static String error_8002 = 'error_8002';
  static String error_8003 = 'error_8003';
  static String error_8004 = 'error_8004';
  static String error_8005 = 'error_8005';
  static String error_8090 = 'error_8090';
  static String error_8091 = 'error_8091';
  static String error_8092 = 'error_8092';
  static String error_8093 = 'error_8093';
  static String error_8101 = 'error_8101';
  static String error_8102 = 'error_8102';
  static String error_8103 = 'error_8103';
  static String error_8104 = 'error_8104';
  static String error_8400 = 'error_8400';
  static String error_8500 = 'error_8500';
  static String error_8600 = 'error_8600';
  static String error_89 = 'error_89';
  static String error_9 = 'error_9';
  static String error_90 = 'error_90';
  static String error_9000 = 'error_9000';
  static String error_9001 = 'error_9001';
  static String error_9010 = 'error_9010';
  static String error_9011 = 'error_9011';
  static String error_9012 = 'error_9012';
  static String error_9013 = 'error_9013';
  static String error_91 = 'error_91';
  static String error_92 = 'error_92';
  static String error_93 = 'error_93';
  static String error_94 = 'error_94';
  static String error_95 = 'error_95';
  static String error_96 = 'error_96';
  static String error_97 = 'error_97';
  static String error_99 = 'error_99';
  static String error_999 = 'error_999';
  static String expireDate = 'expireDate';
  static String failure = 'failure';
  static String findSupplier = 'findSupplier';
  static String generateQRCode = 'generateQRCode';
  static String hello = 'hello';
  static String hintEnterAmount = 'hintEnterAmount';
  static String hintEnterDescription = 'hintEnterDescription';
  static String hintEnterEmail = 'hintEnterEmail';
  static String hintEnterPhone = 'hintEnterPhone';
  static String identity = 'identity';
  static String identityPlaceHolder = 'identityPlaceHolder';
  static String infoHolderCard = 'infoHolderCard';
  static String infoProduct = 'infoProduct';
  static String installmentAssistanceUnit = 'installmentAssistanceUnit';
  static String installmentByVaymuon = 'installmentByVaymuon';
  static String installmentConfirmCopyLinkSuccess = 'installmentConfirmCopyLinkSuccess';
  static String installmentInfoAmountLabel = 'installmentInfoAmountLabel';
  static String installmentInfoAmountNote = 'installmentInfoAmountNote';
  static String installmentButtonContinue = 'installmentButtonContinue';
  static String installmentConfirmLinkTitle = 'installmentConfirmLinkTitle';
  static String installmentConfirmMonth = 'installmentConfirmMonth';
  static String installmentConfirmMonthMoney = 'installmentConfirmMonthMoney';
  static String installmentContent = 'installmentContent';
  static String installmentContentExpire = 'installmentContentExpire';
  static String installmentCreateLink = 'installmentCreateLink';
  static String installmentCreateLinkButton = 'installmentCreateLinkButton';
  static String installmentInfoAmountTitle = 'installmentInfoAmountTitle';
  static String installmentInfoBankTitle = 'installmentInfoBankTitle';
  static String installmentInfoErrorAmountEmpty = 'installmentInfoErrorAmountEmpty';
  static String installmentInfoErrorAmountMax = 'installmentInfoErrorAmountMax';
  static String installmentInfoErrorAmountMin = 'installmentInfoErrorAmountMin';
  static String installmentInfoErrorAmountMissing = 'installmentInfoErrorAmountMissing';
  static String installmentInfoErrorAmountMissingMax = 'installmentInfoErrorAmountMissingMax';
  static String installmentInfoErrorCardEmpty = 'installmentInfoErrorCardEmpty';
  static String installmentInfoErrorPeriodEmpty = 'installmentInfoErrorPeriodEmpty';
  static String installmentInfoFeeInstallmentCard = 'installmentInfoFeeInstallmentCard';
  static String installmentInfoFeeScanCard = 'installmentInfoFeeScanCard';
  static String installmentInfoFeeCreateLink = 'installmentInfoFeeCreateLink';
  static String installmentInfoFeeEnterCard = 'installmentInfoFeeEnterCard';
  static String installmentInfoPeriodTitle = 'installmentInfoPeriodTitle';
  static String totalAmount = 'totalAmount';
  static String installmentLinkCloseAlert = 'installmentLinkCloseAlert';
  static String installmentSelectTypeCardLabel = 'installmentSelectTypeCardLabel';
  static String installmentLinkTitle = 'installmentLinkTitle';
  static String installmentListBankCheckCard6Char = 'installmentListBankCheckCard6Char';
  static String installmentListBankCheckTitle = 'installmentListBankCheckTitle';
  static String installmentListBankContinue = 'installmentListBankContinue';
  static String installmentListBankTitle = 'installmentListBankTitle';
  static String installmentListBankSearch = 'installmentListBankSearch';
  static String installmentPaymentTitle = 'installmentPaymentTitle';
  static String installmentSwipeCard = 'installmentSwipeCard';
  static String installmentSwipeCardButton = 'installmentSwipeCardButton';
  static String installmentVaymuon = 'installmentVaymuon';
  static String internationalCard = 'internationalCard';
  static String invalidEmail = 'invalidEmail';
  static String invalidIdentity = 'invalidIdentity';
  static String invalidPhone = 'invalidPhone';
  static String invalidReceivePhone = 'invalidReceivePhone';
  static String descriptionTooShort = 'descriptionTooShort';
  static String invalidTopupPhone = 'invalidTopupPhone';
  static String labelCustomerInfo = 'labelCustomerInfo';
  static String labelDescription = 'labelDescription';
  static String labelEmail = 'labelEmail';
  static String labelPaymentInfo = 'labelPaymentInfo';
  static String labelPhone = 'labelPhone';
  static String login = 'login';
  static String month = 'month';
  static String name = 'name';
  static String networkError = 'networkError';
  static String noDataError = 'noDataError';
  static String noteFailure = 'noteFailure';
  static String notePromotionInstallVaymuon = 'notePromotionInstallVaymuon';
  static String noteQRPayment = 'noteQRPayment';
  static String noteVaymuonInstallment = 'noteVaymuonInstallment';
  static String noteWaiting1 = 'noteWaiting1';
  static String noteWaiting2 = 'noteWaiting2';
  static String notpaid = 'notpaid';
  static String numberPhoneReciveCard = 'numberPhoneReciveCard';
  static String ok = 'ok';
  static String or = 'or';
  static String orPayoffEndPeriod = 'orPayoffEndPeriod';
  static String password = 'password';
  static String passwordOld = 'passwordOld';
  static String passwordNew = 'passwordNew';
  static String passwordReNew = 'passwordReNew';
  static String passwordOld2 = 'passwordOld2';
  static String passwordNew2 = 'passwordNew2';
  static String passwordReNew2 = 'passwordReNew2';
  static String pay = 'pay';
  static String payConsumerLoan = 'payConsumerLoan';
  static String payQRCodeVaymuon = 'payQRCodeVaymuon';
  static String phone = 'phone';
  static String phoneCard = 'phoneCard';
  static String placeHolderContent = 'placeHolderContent';
  static String postpaid = 'postpaid';
  static String prepaid = 'prepaid';
  static String reciveCodeCard = 'reciveCodeCard';
  static String reciveInvoice = 'reciveInvoice';
  static String remainTimePayment = 'remainTimePayment';
  static String reports = 'reports';
  static String scanCard = 'scanCard';
  static String selectInstallmentPeriod = 'selectInstallmentPeriod';
  static String service = 'service';
  static String shareLink = 'shareLink';
  static String skip = 'skip';
  static String success = 'success';
  static String supportQRCodeInstallment = 'supportQRCodeInstallment';
  static String timeoutError = 'timeoutError';
  static String titleBillPayment = 'titleBillPayment';
  static String titleInstallmentDay = 'titleInstallmentDay';
  static String titleInstallmentMonth = 'titleInstallmentMonth';
  static String titlePaymentSwipeCard = 'titlePaymentSwipeCard';
  static String topupForPhone = 'topupForPhone';
  static String topupPhone = 'topupPhone';
  static String transactionHistory = 'transactionHistory';
  static String transactions = 'transactions';
  static String useRules = 'useRules';
  static String useTips = 'useTips';
  static String viewDetailHere = 'viewDetailHere';
  static String waitingTransaction = 'waitingTransaction';

  static String labelTabHome = 'labelTabHome';
  static String labelTabHistory = 'labelTabHistory';
  static String labelTabWallet = 'labelTabWallet';
  static String labelTabSetting = 'labelTabSetting';
  static String labelTabAccount = 'labelTabAccount';
  static String labelHello = 'labelHello';
  static String labelServiceTitle = 'labelServiceTitle';
  static String labelServiceHistory = 'labelServiceHistory';

  static String titlePaymentInit = 'titlePaymentInit';
  static String connectingDevice = 'connectingDevice';
  static String labelChangePassword = 'labelChangePassword';
  static String labelChangePassword2 = 'labelChangePassword2';
  static String buttonChangePassword = 'buttonChangePassword';
  static String buttonSummary = 'buttonSummary';
  static String labelAccountProtect = 'labelAccountProtect';
  static String titleUserInfo = 'titleUserInfo';
  static String labelAccountSetting = 'labelAccountSetting';
  static String labelNotice = 'labelNotice';
  static String labelCallSupport = 'labelCallSupport';
  static String labelSupportCenter = 'labelSupportCenter';
  static String labelReferralReceiveReward = 'labelReferralReceiveReward';
  static String labelUserInfo = 'labelUserInfo';
  static String titleEnterPaymentInfo = 'titleEnterPaymentInfo';
  static String labelScanCard = 'labelScanCard';
  static String labelEnterCard = 'labelEnterCard';
  static String labelCreateLink = 'labelCreateLink';
  static String labelBnpl = 'labelBnpl';
  static String labelScanQR = 'labelScanQR';
  static String errorAmountEmpty = 'errorAmountEmpty';
  static String errorAmountMin = 'errorAmountMin';

  static String titlePaymentEnterInfo = 'titlePaymentEnterInfo';
  static String labelBillAmount = 'labelBillAmount';
  static String labelBillDescription = 'labelBillDescription';
  static String labelCustomerPhone = 'labelCustomerPhone';
  static String labelCustomerEmail = 'labelCustomerEmail';
  static String requireCustomerPhone = 'requireCustomerPhone';
  static String buttonConfirm = 'buttonConfirm';
  static String errorInvalidDescriptionLength = 'errorInvalidDescriptionLength';
  static String titleEnterCard = 'titleEnterCard';
  static String requireCustomerInfo = 'requireCustomerInfo';
  static String errorOpenUrl = 'errorOpenUrl';
  static String copyUrlSuccessful = 'copyUrlSuccessful';
  static String qrListSourceSearch = 'qrListSourceSearch';
  static String qrListSourceTitle = 'qrListSourceTitle';
  static String qrListSourceLabel = 'qrListSourceLabel';
  static String errorQrCodeEmpty = 'errorQrCodeEmpty';
  static String otherTransaction = 'otherTransaction';
  static String errorCheckTransactionQr = 'errorCheckTransactionQr';
  static String topupPhonePrep = 'topupPhonePrep';
  static String topupPhonePost = 'topupPhonePost';
  static String topupPhoneCard = 'topupPhoneCard';
  static String topupPhonePrepLabel = 'topupPhonePrepLabel';
  static String topupPhonePostLabel = 'topupPhonePostLabel';
  static String topupPhoneCardLabel = 'topupPhoneCardLabel';
  static String labelTelcomProvider = 'labelTelcomProvider';
  static String labelTelcomAmount = 'labelTelcomAmount';
  static String labelTelcomCashback = 'labelTelcomCashback';
  static String labelAmountPay = 'labelAmountPay';
  static String labelAmountPayColon = 'labelAmountPayColon';
  static String labelMoneySource = 'labelMoneySource';
  static String labelBalanceVasWallet = 'labelBalanceVasWallet';
  static String labelScanCardContent = 'labelScanCardContent';
  static String labelTransactionFee = 'labelTransactionFee';
  static String notLeftMoney = 'notLeftMoney';
  static String labelPasswordLogin = 'labelPasswordLogin';
  static String labelPasswordLogin2 = 'labelPasswordLogin2';
  static String labelDiscount = 'labelDiscount';
  static String messageCardUnavailable = 'messageCardUnavailable';
  static String messageCannotNormalPayment = 'messageCannotNormalPayment';
  static String messageQRUnavailable = 'messageQRUnavailable';
  static String earnMoney = 'earnMoney';
  static String vasListProductSearch = 'vasListProductSearch';
  static String vasListProductLabel = 'vasListProductLabel';
  static String vasListCustomerSearch = 'vasListCustomerSearch';
  static String titleHistoryTransaction = 'titleHistoryTransaction';
  static String labelAmountPayment = 'labelAmountPayment';
  static String labelFastWithdraw = 'labelFastWithdraw';
  static String settlement = 'settlement';
  static String transaction = 'transaction';
  static String seeAllTransaction = 'seeAllTransaction';
  static String authCode = 'authCode';
  static String rrnNo = 'rrnNo';
  static String canceled = 'canceled';
  static String settled = 'settled';
  static String refunded = 'refunded';
  static String processing = 'processing';
  static String reversal = 'reversal';
  static String titleConfirmPassword = 'titleConfirmPassword';
  static String labelInputPassword = 'labelInputPassword';
  static String noteConfirmPassword = 'noteConfirmPassword';
  static String titleConfirmSettle = 'titleConfirmSettle';
  static String desConfirmSettle = 'desConfirmSettle';
  static String titleAllTransaction = 'titleAllTransaction';
  static String labelSelectAll = 'labelSelectAll';
  static String titleConfirmFastWithdraw = 'titleConfirmFastWithdraw';
  static String desConfirmFastWithdraw = 'desConfirmFastWithdraw';
  static String labelFeeTransaction = 'labelFeeTransaction';
  static String labelTariffFastWithdraw = 'labelTariffFastWithdraw';
  static String labelAmountWithdraw = 'labelAmountWithdraw';
  static String noteConfirmFastWithdraw = 'noteConfirmFastWithdraw';
  static String labelRequestWithdraw = 'labelRequestWithdraw';
  static String labelCancel = 'labelCancel';
  static String labelCustomerCode = 'labelCustomerCode';
  static String errorUnknown = 'errorUnknown';
  static String contentChangePassword = 'contentChangePassword';
  static String contentChangePasswordNote = 'contentChangePasswordNote';
  static String contentChangePasswordNote2 = 'contentChangePasswordNote2';
  static String errorOldPasswordEmpty = 'errorOldPasswordEmpty';
  static String errorNewPasswordEmpty = 'errorNewPasswordEmpty';
  static String errorNewPasswordLength = 'errorNewPasswordLength';
  static String errorReNewPasswordEmpty = 'errorReNewPasswordEmpty';
  static String errorReNewPasswordMatch = 'errorReNewPasswordMatch';
  static String messageChangePasswordSuccess = 'messageChangePasswordSuccess';
  static String messageChangePasswordSuccess2 = 'messageChangePasswordSuccess2';
  static String titleInstallmentTransaction = 'titleInstallmentTransaction';
  static String labelPaymentMethod = 'labelPaymentMethod';
  static String labelStatus = 'labelStatus';
  static String labelCardHolderName = 'labelCardHolderName';
  static String labelCardType = 'labelCardType';
  static String labelCardNumber = 'labelCardNumber';
  static String labelTransactionId = 'labelTransactionId';
  static String labelTransactionIdColon = 'labelTransactionIdColon';
  static String labelTransactionId2 = 'labelTransactionId2';
  static String labelDeviceId = 'labelDeviceId';
  static String labelDVCNTId = 'labelDVCNTId';
  static String labelTransactionTime = 'labelTransactionTime';
  static String labelTransactionTimeColon = 'labelTransactionTimeColon';
  static String labelBtnReceiveFastMoney = 'labelBtnReceiveFastMoney';
  static String labelBtnCancelTransaction = 'labelBtnCancelTransaction';
  static String titleDetailTransaction = 'titleDetailTransaction';
  static String titleCardTransaction = 'titleCardTransaction';
  static String labelQRPay = 'labelQRPay';
  static String titleQrCodeTransaction = 'titleQrCodeTransaction';
  static String labelEnterCardPayment = 'labelEnterCardPayment';
  static String labelErrCode = 'labelErrCode';
  static String labelEmptyPassword = 'labelEmptyPassword';
  static String labelBatchNumber = 'labelBatchNumber';
  static String messageExit = 'messageExit';
  static String messageConfirmLogout = 'messageConfirmLogout';
  static String labelStoreName = 'labelStoreName';
  static String labelAuthorizedName = 'labelAuthorizedName';
  static String labelAuthorizedPhone = 'labelAuthorizedPhone';
  static String labelMerchantCode = 'labelMerchantCode';
  static String messageInvalidService = 'messageInvalidService';
  static String messagePendingTransaction = 'messagePendingTransaction';
  static String labelButtonSendEmail = 'labelButtonSendEmail';
  static String labelSendEmail = 'labelSendEmail';
  static String titleAtm360Service = 'titleAtm360Service';
  static String titleServiceCommissions = 'titleServiceCommissions';
  static String labelRechargeMoney = 'labelRechargeMoney';
  static String labelWithdraw = 'labelWithdraw';
  static String labelAllPayment = 'labelAllPayment';
  static String labelRechargeWithdraw = 'labelRechargeWithdraw';
  static String titleRechargeTransaction = 'titleRechargeTransaction';
  static String titlePaymentTransaction = 'titlePaymentTransaction';
  static String labelTypePayment = 'labelTypePayment';
  static String labelSourceRecharge = 'labelSourceRecharge';
  static String labelAmountAfter = 'labelAmountAfter';
  static String labelPaymentService = 'labelPaymentService';
  static String labelSourcePayment = 'labelSourcePayment';
  static String labelStatusService = 'labelStatusService';
  static String labelStatusPayment = 'labelStatusPayment';
  static String labelAmountCommission = 'labelAmountCommission';
  static String labelSeeBill = 'labelSeeBill';
  static String titleDetailBillWebview = 'titleDetailBillWebview';
  static String titleSendBill = 'titleSendBill';
  static String titleSendBillToEmail = 'titleSendBillToEmail';
  static String titleEmailReceiveBill = 'titleEmailReceiveBill';
  static String errorInstallmentNotSupportCard = 'errorInstallmentNotSupportCard';
  static String errorInstallmentTitle = 'errorInstallmentTitle';
  static String statusACTIVE = 'statusACTIVE';
  static String statusDENIED = 'statusDENIED';
  static String statusPROCESSING = 'statusPROCESSING';
  static String statusSUSPENDED = 'statusSUSPENDED';
  static String Status = 'Status';
  static String messageOrderCodeInvalid = 'messageOrderCodeInvalid';
  static String messageOrderCodeExist = 'messageOrderCodeExist';
  static String messageOrderCodeInput = 'messageOrderCodeInput';
  static String OrderId = 'OrderId';
  static String Description = 'Description';
  static String titleOrderDetail = 'titleOrderDetail';
  static String BackToAppMerchant = 'BackToAppMerchant';
  static String messageRequireLoginToScanCard = 'messageRequireLoginToScanCard';
  static String labelAmount = 'labelAmount';
  static String swipeCardUseMposReader = 'swipeCardUseMposReader';
  static String scanQRCode = 'scanQRCode';
  static String messageQuickMoneyMarkSuccess = 'messageQuickMoneyMarkSuccess';
  static String titleUpdateApp = 'titleUpdateApp';
  static String titleUpdateAppForce = 'titleUpdateAppForce';
  static String titleUpdate = 'titleUpdate';
  static String titleRechargeToWallet = 'titleRechargeToWallet';
  static String labelAmountWantRecharge = 'labelAmountWantRecharge';
  static String labelRechargeNow = 'labelRechargeNow';
  static String titleErrorConfirmRecharge = 'titleErrorConfirmRecharge';
  static String contentErrorConfirmRecharge = 'contentErrorConfirmRecharge';
  static String labelErrorAmount = 'labelErrorAmount';
  static String errorConnectReaderPR02 = 'errorConnectReaderPR02';
  static String errorConnectReaderPR01 = 'errorConnectReaderPR01';
  static String errorConnectReaderAR01 = 'errorConnectReaderAR01';
  static String titleDetailNotify = 'titleDetailNotify';
  static String labelVasBalance = 'labelVasBalance';
  static String label360Transaction = 'label360Transaction';
  static String errorCameraPermissionDeny = 'errorCameraPermissionDeny';
  static String contentErrServiceList = 'contentErrServiceList';
  static String titleConfirmRecharge = 'titleConfirmRecharge';
  static String titleConfirmWithdraw = 'titleConfirmWithdraw';
  static String titleConnectReaderFail = 'titleConnectReaderFail';
  static String titleConfirmCancel = 'titleConfirmCancel';
  static String titleBannerActiveAtm360 = 'titleBannerActiveAtm360';
  static String contentBannerActiveAtm360 = 'contentBannerActiveAtm360';
  static String titleInputWithdraw = 'titleInputWithdraw';
  static String labelAccountReceiveMoney = 'labelAccountReceiveMoney';
  static String messagePaymentNotSuccess = 'messagePaymentNotSuccess';
  static String labelErrorAmountPayment = 'labelErrorAmountPayment';
  static String titleAtm360Active = 'titleAtm360Active';
  static String labelAtm360Active = 'labelAtm360Active';
  static String hintAtm360Active = 'hintAtm360Active';
  static String buttonSendInfo = 'buttonSendInfo';
  static String buttonSend = 'buttonSend';
  static String buttonActiveNow = 'buttonActiveNow';
  static String contentActiveNow = 'contentActiveNow';
  static String titleSuccessRequest = 'titleSuccessRequest';
  static String messageRequestActiveAtm360Success = 'messageRequestActiveAtm360Success';
  static String messageSuccessEmailRecovery = 'messageSuccessEmailRecovery';
  static String messageNeedActiveAtm360 = 'messageNeedActiveAtm360';
  static String labelUpdating = 'labelUpdating';
  static String labelBackToAppMerchant = 'labelBackToAppMerchant';
  static String labelPendingSignature = 'labelPendingSignature';
  static String labelCancelTransaction = 'labelCancelTransaction';
  static String labelConfirmCancelTransaction = 'labelConfirmCancelTransaction';
  static String labelPaymentPeriod = 'labelPaymentPeriod';
  static String labelVoidReason = 'labelVoidReason';
  static String labelStatusWaitExplain = 'labelStatusWaitExplain';
  static String labelStatusKtExplain = 'labelStatusKtExplain';
  static String labelWaitForDisbursement = 'labelWaitForDisbursement';
  static String labelReversal = 'labelReversal';
  static String labelCustomer = 'labelCustomer';
  static String labelApprovalCode = 'labelApprovalCode';
  static String labelInvoiceCode = 'labelInvoiceCode';
  static String labelRefNo = 'labelRefNo';
  static String errorParamsNotValid = 'errorParamsNotValid';
  static String labelFeeFastReceive = 'labelFeeFastReceive';
  static String labelTransactionAmount = 'labelTransactionAmount';
  static String titleInstallmentGuide = 'titleInstallmentGuide';
  static String titleServiceTransaction = 'titleServiceTransaction';
  static String titleCommissionTransaction = 'titleCommissionTransaction';
  static String messagePermissionNotification = 'messagePermissionNotification';
  static String setting = 'setting';
  static String titleLoginByEmail = 'titleLoginByEmail';
  static String titlePopupConfirmPayment = 'titlePopupConfirmPayment';
  static String labelNotePopupConfirmPayment = 'labelNotePopupConfirmPayment';
  static String transactionVT = 'transactionVT';
  static String titlePopupConfirmPass = 'titlePopupConfirmPass';
  static String labelNotePopupConfirmPass = 'labelNotePopupConfirmPass';
  static String enterOrderCode = 'enterOrderCode';
  static String errorNotEnoughMoneyWarning = 'errorNotEnoughMoneyWarning';
  static String descriptionEmpty = 'descriptionEmpty';
  static String slPullUp = 'slPullUp';
  static String slFailed = 'slFailed';
  static String slCan = 'slCan';
  static String slEmpty = 'slEmpty';
  static String connecting = 'connecting';
  static String logout = 'logout';
  static String buttonPrint = 'buttonPrint';
  static String buttonDetail = 'buttonDetail';
  static String buttonRetry = 'buttonRetry';
  static String unSignPaymentResult = 'unSignPaymentResult';
  static String stillClose = 'stillClose';
  static String installment = 'installment';
  static String moto = 'moto';
  static String timeStart = 'timeStart';
  static String timeEnd = 'timeEnd';
  static String errorTimeRange = 'errorTimeRange';
  static String errorTimeRangeInvalid = 'errorTimeRangeInvalid';
  static String errorTimeRangeInvalid7 = 'errorTimeRangeInvalid7';
  static String titleSummary = 'titleSummary';
  static String summaryType = 'summaryType';
  static String summaryTotalTrans = 'summaryTotalTrans';
  static String summaryTotalAmount = 'summaryTotalAmount';
  static String summaryTransCard = 'summaryTransCard';
  static String summaryTransQR = 'summaryTransQR';
  static String summaryTransCb = 'summaryTransCb';
  static String summaryTotal = 'summaryTotal';
  static String summaryNote = 'summaryNote';
  static String errorSummaryPrint = 'errorSummaryPrint';
  static String errorLoginDioCode_7 = 'errorLoginDioCode_7';

  // mpos-lite
  static String errorPassNotMatch = 'errorPassNotMatch';
  static String warningMaUnSign = 'warningMaUnSign';
  static String warningMacqSkipSignature = 'warningMacqSkipSignature';
  static String titleQRPaymentEnterInfo = 'titleQRPaymentEnterInfo';
  static String feeForCustomer = 'feeForCustomer';
  static String viewHint = 'viewHint';
  static String onlyFor = 'onlyFor';
  static String oneCustomerOnePayment = 'oneCustomerOnePayment';
  static String titleNoteQr = 'titleNoteQr';
  static String qrNote1 = 'qrNote1';
  static String qrNote2 = 'qrNote2';
  static String qrNote3 = 'qrNote3';
  static String addFeeQR = 'addFeeQR';


  static String splashSlogan = 'splash_slogan';
  static String hotline = 'hotline';

  static String thankForUsing = 'thank_for_using';
  static String startPayment = 'start_payment';

  static String createNewTrans = 'create_new_trans';
  static String menuSettings= 'menu_settings';
  static String menuLoyal= 'menu_loyal';
  static String menuGuide   = 'menu_guide';
  static String menuUpgrade = 'menu_upgrade';
  static String menuContact = 'menu_contact';
  static String confirmExitApp = 'confirm_exit_app';

  static String selectTypePay = 'select_type_pay';
  static String settingSelectTypePay = 'setting_select_type_pay';
  static String hintSettingSelectTypePay = 'hint_setting_select_type_pay';

  static String setting_type_pay = 'setting_type_pay';
  static String setting_type_pay_desc = 'setting_type_pay_desc';
  static String setting_print = 'setting_print';
  static String setting_print_desc = 'setting_print_desc';

  static String setting_auto_print_none = 'setting_auto_print_none';
  static String setting_auto_print_1 = 'setting_auto_print_1';
  static String setting_auto_print_2 = 'setting_auto_print_2';
  static String setting_auto_print_3 = 'setting_auto_print_3';

  static String languages = 'languages';
  static String receipt = 'receipt';
  static String guild_auto_print = 'guild_auto_print';

  static String outageMessage = 'outageMessage';
  static String error_sdk_count_down_call_api = 'error_sdk_count_down_call_api';

  //plural
  static String pluralOneSecond = 'plural_one_second';
  static String pluralOtherSecond = 'plural_other_second';

  //p12
  static String tvScanGgAppleQr = 'tv_scan_qr_gg_apple';
  static String tvScanQr = 'tv_scan_qr';
  static String tvPaySuccess = 'tv_pay_success';
  static String msg_warning_close_qr_screen = 'msg_close_qr_screen';
  static String msg_oneCustomerOnePayment = 'msg_oneCustomerOnePayment';
  static String tv_create_qr_again = 'tv_create_qr_again';
  static String tv_creating_qr = 'tv_creating_qr';
  static String tv_detail_qr_vietqr = 'tv_detail_qr_vietqr';
  static String tv_detail_qr_vnpay = 'tv_detail_qr_vnpay';
  static String tv_detail_qr_mvisa_master = 'tv_detail_qr_mvisa_master';
  static String tv_detail_qr_wallet = 'tv_detail_qr_wallet';
  static String tv_name_group_qr_bank = 'tv_name_group_qr_bank';
  static String tv_name_group_qr_vietqr = 'tv_name_group_qr_vietqr';
  static String tv_name_group_qr_mvisamaster = 'tv_name_group_qr_mvisamaster';
  static String tv_name_group_qr_wallet = 'tv_name_group_qr_wallet';
  static String tv_name_group_qr_default = 'tv_name_group_qr_default';
  static String tv_detail_bank_number = 'tv_detail_bank_number';
  static String tv_push_payment = 'tv_push_payment';
  static String msg_noti_payment_success = 'msg_noti_payment_success';
  static String msg_err_fetch_config_qr = 'msg_err_fetch_config_qr';
  static String tv_guide_p12_qr = 'tv_guide_p12_qr';
  static String tv_guide_p12 = 'tv_guide_p12';
  static String tv_know_guide = 'tv_know_guide';
  static String msg_warning_not_support_pushpayment = 'msg_warning_not_support_pushpayment';
  static String msg_intro_static_qr_ag = 'msg_intro_static_qr_ag';
  static String msg_deception_static_qr = 'msg_deception_static_qr';
  static String tv_title_apple_google_qr = 'tv_title_apple_google_qr';
  static String tv_default_display_qr = 'tv_default_display_qr';
  static String tv_disable_display_ag_qr = 'tv_disable_display_ag_qr';
  static String tv_enable_display_ag_qr = 'tv_enable_display_ag_qr';
  static String tv_des_disable_display_ag_qr = 'tv_des_disable_display_ag_qr';
  static String tv_des_enable_display_ag_qr = 'tv_des_enable_display_ag_qr';
  static String msg_deception_static_qr_gg_apple = 'msg_deception_static_qr_gg_apple';
  static String tv_setting_default_ag_qr = 'tv_setting_default_ag_qr';
  static String tv_des_setting_default_ag_qr = 'tv_des_setting_default_ag_qr';
  static String msg_warning_get_token_firebase = 'msg_warning_get_token_firebase';
  static String msg_add_deception = 'msg_add_deception';
  static String tv_add_deception = 'tv_add_deception';
  static String tv_title_vietQR = 'tv_title_vietQR';
  static String tv_close_qr = 'tv_close_qr';
  static String loading = 'loading';
  static String msgCheckTransactionQrNotPay = 'msgCheckTransactionQrNotPay';
  static String tv_no_signature = 'tv_no_signature';
  static String tv_create_new_order = 'tv_create_new_order';
  static String msg_order_expired = 'msg_order_expired';
  static String tv_other_type_qr = 'tv_other_type_qr';
  static String title_qr_wallet = 'title_qr_wallet';
  static String tv_trans_no_signature = 'tv_trans_no_signature';
  static String tv_app_version = 'tv_app_version';
  static String tv_guide_scan_qr_link = 'tv_guide_scan_qr_link';
  static String title_link_card = 'title_link_card';
  static String tv_confirm_void_trans = 'tv_confirm_void_trans';
  static String content_note_quick_withdraw1 = 'content_note_quick_withdraw1';
  static String content_note_quick_withdraw2 = 'content_note_quick_withdraw2';
  static String title_cost_for_service = 'title_cost_for_service';
  static String des_quick_draw = 'des_quick_draw';
  static String tv_quick_money_mark_success = 'tv_quick_money_mark_success';
  static String tv_disable_status_bar = 'tv_disable_status_bar';
  static String tv_disable_nav_bar = 'tv_disable_nav_bar';
  static String tv_setting_auto_dismiss_dlg = 'tv_setting_auto_dismiss_dlg';
  static String check_card_installment = 'check_card_installment';
  static String check_card_installment_short = 'check_card_installment_short';
  static String title_deposit = 'title_deposit';
  static String title_wait_tcp_screen = 'title_wait_tcp_screen';
  static String title_no = 'title_no';
  static String tv_setting_hold_screen = 'tv_setting_hold_screen';
  static String tv_des_setting_hold_screen = 'tv_des_setting_hold_screen';

  static String tv_emqx_connecting = 'tv_emqx_connecting';
  static String tv_emqx_disconnect = 'tv_emqx_disconnect';
  static String tv_emqx_connected = 'tv_emqx_connected';

}
