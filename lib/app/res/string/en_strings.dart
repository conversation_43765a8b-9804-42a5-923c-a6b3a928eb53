const enStrings = {
  "waitingTransTCP": "Waiting new trans...",
  "title_deposit": "Deposit",
  "Deposit": "Deposit",
  "Tạm ứng": "Deposit",
  "check_card_installment": "Check installment card",
  "check_card_installment_short": "Review card",
  "Check installment card": "Check installment card",
  "Kiểm tra thẻ TG": "Check installment card",
  "titleNotice": "Notice",
  "titleWarning": "Warning",
  "accountNameEmpty": "The Username field is required",
  "accountNameTooLong": "The Username is invalid",
  "passwordEmpty": "The Password field is required",
  "passwordTooLong": "The Password is too long",
  "selectReader": "Select Reader",
  "noteReaderConnectViaBluetooth": "Device connected via bluetooth. \nLogin via MPOS account provided",
  "noteReaderConnectViaAudio": "Device connected via the phone's audio jack \nLogin via MPOS account provided",
  "titleConnectDevice": "Connect a Reader",
  "titleLoginNotDevice": "Login without your Reader",
  "titleCallSupport": "Call support",
  "titleBackToSelectReader": "Back to select a Reader",
  "titleReSelectReader": "Reselect",
  "titleForgetPassword": "Forgot password?",
  "titleForgetPassword2": "Forgot password level 2?",
  "titleForgetPasswordDialog": "Forgot password level 2",
  "hintForgetPasswordDialog":
      "Enter the email you registered with mPos when activating the ATM360 service wallet to retrieve your level 2 password.",
  "labelForgetPasswordDialog": "Recovery email",
  "paymentStateSuccess": "Payment Successful",
  "paymentStateFail": "Your payment failed",
  "paymentStateReview": "Payment pending",
  "titleNhanTienNhanh": "Quick withdrawal to bank account",
  "titleTaoGiaoDichKhac": "New Sale",
  "titleTaoGiaoDichKhac2": "New Sale",
  "titleTaoLaiGiaoDich": "Recreate Sale",
  "titleGuiBienNhan": "Send Receipt ",
  "titleHotlineHoTro": "Support Hotline",
  "hello": "Hi,",
  "login": "Login",
  "deviceId": "Connected device: ",
  "account": "Account",
  "password": "Password",
  "passwordOld": "Old Password",
  "passwordNew": "New Password",
  "passwordReNew": "Confirm new password",
  "passwordOld2": "Old Password level 2",
  "passwordNew2": "New Password level 2",
  "passwordReNew2": "Confirm new password level 2",
  "service": "Services",
  "transactionHistory": "Transaction History",
  "installmentVaymuon": "Installment Vaymuon",
  "networkError": "Could not connect to server, please check your Internet or try again in a few minutes ",
  "timeoutError": "The connection has timed out, please try again in a few minutes",
  "noDataError": "Data is empty please try again after a few minutes",
  "errorCallApiTitle": "Connection error",
  "errorCallApiResponseTitle": "Notifications",
  "titleBillPayment": "Bill payments",
  "consumerLoan": "Consumer loans",
  "findSupplier": "Find suppliers",
  "payConsumerLoan": "Consumer loan payment",
  "confirmPay": "Confirm Payment",
  "name": "Full name",
  "identity": "ID card",
  "identityPlaceHolder": "Enter ID card number",
  "description": "Description",
  "descriptionPlaceHolder": "Enter description",
  "expireDate": "Expiration date",
  "amountPay": "Amount",
  "amountTopup": "Topup Amount",
  "amountCardValue": "Scratch card value",
  "reciveCodeCard": "Get PIN code",
  "reciveInvoice": "Get e-Receipt",
  "infoHolderCard": "Cardholder information",
  "infoProduct": "Product information",
  "phone": "Mobile",
  "email": "Email",
  "amountCashMinusInCard": "Your card will be charged for",
  "atmCard": "Domestic ATM card",
  "internationalCard": "International Card",
  "pay": "Payment",
  "contentPromotion1": "The cardholder receives",
  "contentPromotion2": "immediately when registering and linking the Card / Bank Account into VIMO wallet",
  "topupPhone": "Mobile Topup",
  "topupPhonePrep": "Topup Pre-paid",
  "topupPhonePost": "Topup Postpaid",
  "topupPhoneCard": "Telco Card",
  "topupPhonePrepLabel": "Topup Pre-paid phone",
  "topupPhonePostLabel": "Topup Postpaid phone ",
  "topupPhoneCardLabel": "Mobile number to receive PIN code ",
  "prepaid": "Prepaid",
  "postpaid": "Postpaid",
  "phoneCard": "Telco card",
  "topupForPhone": "Mobile Topup ",
  "cardValue": "Topup Amount",
  "numberPhoneReciveCard": "Phone number receiving card code",
  "enterPhone": "Mobile number",
  "installmentLinkTitle": "Installment payment link",
  "installmentListBankTitle": "Select Your Instalment Bank",
  "installmentListBankSearch": "Enter bank name",
  "installmentPaymentTitle": "Instalment Payment Plan",
  "installmentListBankCheckTitle": "Validate credit card number",
  "installmentListBankCheckCard6Char": "Enter first %s digits",
  "installmentListBankContinue": "Skip checking",
  "installmentInfoAmountTitle": "Amount",
  "installmentInfoBankTitle": "Bank",
  "installmentInfoPeriodTitle": "Instalment Period",
  "installmentInfoFeeScanCard": "Add Card Payment Fees",
  "installmentInfoFeeCreateLink": "Add Card Payment Fees",
  "installmentInfoFeeEnterCard": "Add Card Payment Fees",
  "installmentInfoFeeInstallmentCard": "Add Instalment Fees",
  "totalAmount": "Total Amount",
  "installmentConfirmMonth": "%1s Months Instalment",
  "installmentConfirmMonthMoney": "Equated Monthly Instalments",
  "installmentConfirmLinkTitle": "Share Payment Link To Your Customer",
  "installmentConfirmCopyLinkSuccess": "Copy Successful!",
  "installmentInfoAmountLabel": "Instalment Amount",
  "installmentInfoAmountNote": "The Minimum instalment amount: ",
  "installmentButtonContinue": "Continue",
  "installmentInfoErrorAmountEmpty": "Please Enter Payment Amount",
  "installmentInfoErrorAmountMissing": "Vaymuon requires a minimum of amount VND in installments",
  "installmentInfoErrorAmountMissingMax": "Vaymuon requires a maximum of amount VND in installments",
  "installmentInfoErrorCardEmpty": "Please Select Card Type",
  "installmentInfoErrorPeriodEmpty": "Please Select Instalment Period",
  "installmentInfoErrorAmountMin": "The Minimum Instalment Amount Of %1s Is %2s",
  "installmentInfoErrorAmountMax": "The Maximum Instalment Amount Of %1s Is %2s",
  "installmentSwipeCard": "Swipe card",
  "installmentCreateLink": "Create link",
  "installmentSwipeCardButton": "Swipe card",
  "installmentCreateLinkButton": "Create link",
  "installmentContent": "Please copy the payment links or share for your customers to complete payment",
  "installmentContentExpire": "The Payment link will expire in ",
  "installmentLinkCloseAlert": "Please make sure you copy or share the payment link before closing",
  "installmentSelectTypeCardLabel": "Select Card Type",
  "currencyVND": "VND",
  "currencyVNDShort": "d",
  "month": "Month(s)",
  "doNotSupportDebitCard": "(Debit card not supported)",
  "installmentByVaymuon": "Vaymuon Installment payment",
  "installmentAssistanceUnit": "Supported by",
  "selectInstallmentPeriod": "Select installment plan",
  "orPayoffEndPeriod": "Or pay full in",
  "supportQRCodeInstallment": "(Support QR-Code)",
  "noteVaymuonInstallment":
      "Ex: If you purchase 3.000.000 on 1st of Sep you will have to pay back full amount on 1st of Oct to Vaymuon",
  "day": "day(s)",
  "generateQRCode": "QR-Code",
  "payQRCodeVaymuon": "Payment QR-code Vay mượn",
  "noteQRPayment": "Use Lendmo app to scan QR code under the installment payment",
  "remainTimePayment": "Remain time payment",
  "notpaid": "Not paid",
  "close": "Close",
  "titleInstallmentDay": "Pay full in",
  "titleInstallmentMonth": "Installment plan",
  "notePromotionInstallVaymuon": "Reward information",
  "content": "Content",
  "placeHolderContent": "Product Name",
  "errorQRVaymuon": "Connection temporarily interrupted please try again",
  "createNewTransaction": "New Sale",
  "backHome": "Back Home",
  "backHome2": "Back To Home",
  "completeTransaction": "Done ",
  "success": "Success",
  "failure": "Failure",
  "waitingTransaction": "Waiting to confirm transaction",
  "noteWaiting1":
      "We need at least 05 minutes to validate the transaction from the buyer. Seller DO NOT DELIVERY upon seeing this notice",
  "noteFailure": "Your transaction has not success",
  "customerInfo": "Customer information / details",
  "contentWarningBackVM": "Are you sure you want to exit the order information screen?",
  "continueText": "Continue",
  "ok": "Ok",
  "enterCustomerCode": "Enter customer code",
  "enterCode": "Enter code",
  "check": "Check",
  "or": "or",
  "scanCard": "Swipe Card",
  "Swipe Card": "Swipe Card",
  "Quẹt thẻ": "Swipe Card",
  "createLink": "Create Payment Link",
  "copyLink": "Copy link",
  "shareLink": "Share link",
  "errorTitle": "Error",
  "cancel": "Cancel",
  "skip": "Skip",
  "viewDetailHere": "See More",
  "emptyPhone": "The Phone number field is required",
  "emptyPhoneInstallment": "Please enter the cardholder's phone number to verify the transaction",
  "emptyDescription": "The Description field is required",
  "emptyIdentity": "The Identity field is required",
  "emptyPhoneAndEmail": "The Phone numbers and Email are required",
  "emptyEmail": "The Email field is required",
  "invalidPhone": "Invalid Phone Number",
  "invalidEmail": "Invalid Email Address",
  "invalidIdentity": "Invalid Identity",
  "emptyTopupItem": "Please select a network and amount",
  "emptyTopupPhone": "The Phone number is required",
  "invalidTopupPhone": "Invalid Phone Number",
  "invalidReceivePhone": "Invalid bonus phone number",
  "descriptionTooShort": "The Description field is too short",
  "error_1": "This Card is not Active, please contact the issuing bank",
  "error_2": "This card is not accepted, please use a different card",
  "error_3": "Invalid Merchant, please contact Hotline: 1900-63-64-88 for assistance.",
  "error_4": "The card is reported stolen or expired, please Return it to the issuing bank",
  "error_5": "Card declined by the issuing bank - Contact card issuer to determine reason",
  "error_6": "Card declined by the issuing bank - Contact card issuer to determine reason",
  "error_7": "Card deactived by the issuing bank - Please use a different card",
  "error_8":
      "There is no response from the issuing bank, please contact Hotline 1900-63-64-88 for assistance if the cardholder has been charged.",
  "error_9": "Request denied by the issuing bank. Contact card issuer or Use a different card",
  "error_10": "Approved for partial amount",
  "error_11": "Approved (VIP)",
  "error_12": "Invalid transaction - Please contact Hotline: 1900-63-64-88 for assistance",
  "error_13": "Invalid amount - Please Try again ",
  "error_14": "Invalid card number, please use a different card",
  "error_15": "Issuing bank does not exist, please use a different card",
  "error_16": "Approved update track 3",
  "error_17": "Customer cancellation",
  "error_18": "Customer dispute",
  "error_19": "Re enter transaction",
  "error_20": "Invalid response",
  "error_21": "Request denied by the issuing bank. Contact card issuer or Use a different card",
  "error_22": "Suspected malfunction",
  "error_23": "Unacceptable transaction fee",
  "error_24": "File update not supported by receiver",
  "error_25": "Unable to locate record in file or account number is missing from the inquiry.",
  "error_26": "Duplicate file update record old record replaced",
  "error_27": "File update field edit error",
  "error_28": "File update file locked out",
  "error_29": "File update not successful contact acquirer",
  "error_30": "Format error",
  "error_31": "Bank not supported by switch",
  "error_32": "Completed partially",
  "error_33": "Expired card - Please user a different card",
  "error_34": "Suspected fraud - Please user a different card",
  "error_35": "Card acceptor contact acquirer",
  "error_36": "Restricted card",
  "error_37": "Card acceptor call acquirer security",
  "error_38": "Allowable PIN tries exceeded",
  "error_39": "No credit account",
  "error_40": "Requested function not supported",
  "error_41": "Lost card",
  "error_42": "No universal account",
  "error_43": "Stolen card pick-up",
  "error_44": "No investment account",
  "error_45": "Reserved for ISO use",
  "error_46": "Reserved for ISO use",
  "error_47": "Reserved for ISO use",
  "error_48": "Reserved for ISO use",
  "error_49": "Reserved for ISO use",
  "error_50": "Reserved for ISO use",
  "error_51": "Insufficient funds",
  "error_52": "No chequing account",
  "error_53": "No savings account",
  "error_54": "Expired card",
  "error_55": "Incorrect PIN",
  "error_56": "No card record",
  "error_57": "Transaction not permitted to cardholder",
  "error_58": "Transaction not permitted to terminal",
  "error_59": "Suspected fraud",
  "error_60": "Card acceptor contact acquirer",
  "error_61": "Credit limit exceeded - Please use a different card ",
  "error_62": "Restricted card",
  "error_63": "Security violation",
  "error_64": "Original amount incorrect",
  "error_65": "Your card reached the maximum number of payment",
  "error_66": "Card acceptor call acquirer's security department",
  "error_67": "Hard capture (requires that card be picked up at ATM)",
  "error_68": "Response received too late",
  "error_69": "Reserved for private use",
  "error_70": "Reserved for private use",
  "error_71": "Reserved for private use",
  "error_72": "Reserved for private use",
  "error_73": "Reserved for private use",
  "error_74": "Reserved for private use",
  "error_75": "Allowable number of PIN tried exceeded",
  "error_76": "Reserved for private use",
  "error_89": "Error",
  "error_90": "Cutoff is in process (switch ending a day's business and starting the next transaction can be sent ag",
  "error_91":
      "Issuer or switch is inoperative, please call MPOS Customer Support hotline 1900-63-64-88 for further assistance.",
  "error_92":
      "Financial institution or intermediate network facility cannot be found for routing, please call MPOS Customer Support hotline 1900-63-64-88 for further assistance.",
  "error_93":
      "Transaction cannot be completed. Violation of law, please call MPOS Customer Support hotline 1900-63-64-88 for further assistance.",
  "error_94": "Duplicate transmission, please call MPOS Customer Support hotline 1900-63-64-88 for further assistance.",
  "error_95": "Reconcile error, please call MPOS Customer Support hotline 1900-63-64-88 for further assistance.",
  "error_96": "System error, please call MPOS Customer Support hotline 1900-63-64-88 for further assistance.",
  "error_97":
      "Reserved for national use, please call MPOS Customer Support hotline 1900-63-64-88 for further assistance.",
  "error_99":
      "Reserved for national use, please call MPOS Customer Support hotline 1900-63-64-88 for further assistance.",
  "error_1004":
      "Service is unavailable, please call MPOS Customer Support hotline 1900-63-64-88 for further assistance.",
  "error_1009":
      "Can not check this order, please try another order or call MPOS Customer Support hotline 1900-63-64-88 for further assistance.",
  "error_1008":
      "Can not check this order, please try another order or call MPOS Customer Support hotline 1900-63-64-88 for further assistance.",
  "error_1011": "Mobile invalid please check again.",
  "error_1013": "Request timeout, please try again later.",
  "error_1014": "Amount invalid please check input amount",
  "error_2001": "Initialization error",
  "error_2002": "Session is expired",
  "error_3000": "Mobile user not existed",
  "error_3011":
      "You have exceeded a maximum number of three (3) attempts, please contact our Merchant Hotline to reinstate your account.",
  "error_3012": "You do not have permission to void or settle transaction",
  "error_3020": "Please activate account using another phone /device",
  "error_3021": "Please login using a registered reader",
  "error_3030": "Reader is not linked to the current merchant",
  "error_3031": "Reader is inactive or suspended, please use another reader",
  "error_3032": "Reader is malfunction, please contact help desk for a reader replacement",
  "error_3040": "TID is suspended or not linked to Mobile User",
  "error_3041": "Merchant not found, please call MPOS Customer Support hotline 1900-63-64-88 for further assistance.",
  "error_3043": "Wrong password, please input your password then Login again.",
  "error_3100": "Mpos user's transaction not found",
  "error_3200": "Merchant not found, please call MPOS Customer Support hotline 1900-63-64-88 for further assistance.",
  "error_3300":
      "MPOS Merchant is not active, please call MPOS Customer Support hotline 1900-63-64-88 for further assistance.",
  "error_3301":
      "Password incorrect, please try again or call MPOS Customer Support hotline 1900-63-64-88 for further assistance.",
  "error_4002": "Service is currently unavailable, please try again later",
  "error_4106": "Reader is not linked to the current merchant.",
  "error_5010": "Invalid login or Mobile User /Merchant has been suspended",
  "error_5011": "PIN has to be a 6 numeric characters",
  "error_5012": "Please do not reuse old password",
  "error_5013": "Invalid user ID PIN or activation code",
  "error_5014":
      "Please ensure User ID and User PIN are valid. This will be your last attempt before your account is suspended.",
  "error_5020": "Mobile application is outdated, please update",
  "error_5110": "Unable to continue transaction, please try again later",
  "error_5111": "You have exceeded your maximum monthly transaction limit, please call help desk",
  "error_5112": "You have exceeded your maximum per transaction limit, please call help desk",
  "error_5114": "Invalid email address",
  "error_5115": "Email /SMS service is currently not available, please call Help Desk",
  "error_5120":
      "Unable to process payment, please try again later. If problem still persist after several retries please contact help desk",
  "error_5131": "You do not have the required permissions to perform key injection",
  "error_5132": "You do not have the required permissions to verify reader",
  "error_5555": "System is currently not available, please try again later",
  "error_7000":
      "Merchant Integrated config not found, please call MPOS Customer Support hotline 1900-63-64-88 for further assistance.",
  "error_7001":
      "Merchant Integrated not active to check order, please call MPOS Customer Support hotline 1900-63-64-88 for further assistance.",
  "error_7003":
      "Merchant Integrated not active to check order, please call MPOS Customer Support hotline 1900-63-64-88 for further assistance.",
  "error_7005":
      "Can not check this order, please try another order or call MPOS Customer Support hotline 1900-63-64-88 for further assistance",
  "error_7006":
      "Can not check this order or invalid order amount, please call MPOS Customer Support hotline 1900-63-64-88 for further assistance.",
  "error_7007":
      "Can not check transaction information, please call MPOS Customer Support hotline 1900-63-64-88 for further assistance.",
  "error_7008":
      "Merchant Integrated not active to check order, please call MPOS Customer Support hotline 1900-63-64-88 for further assistance.",
  "error_8090": "Host error, please call Help Desk",
  "error_8091": "Service is unavailable, please try again later",
  "error_8092": "Service is unavailable, please try again later",
  "error_8093": "Batch Upload failed, please call MPOS Customer Support hotline 1900-63-64-88 for further assistance.",
  "error_8101": "Transaction amount cannot be null",
  "error_8102": "Magstripe data invalid",
  "error_8103": "Magstripe not permitted please use card's chip data.",
  "error_8104": "KSN provided is invalid",
  "error_9001": "Error found while processing card",
  "error_9010": "Invalid service name /version",
  "error_9011": "Method invocation error",
  "error_9012": "No application is selected",
  "error_9013": "Transaction amount cannot be null",
  "error_10001": "Service is currently unavailable, please try again later",
  "error_10006":
      "Service is currently unavailable, please call hotline 1900-63-64-88 mPoS.vn Customer Support team will be able to assign you",
  "error_10007": "Invalid card data, please try again or use other card.",
  "error_10004":
      "Settlement processing, please wait or call hotline 1900-63-64-88 mPoS.vn Customer Support team will be able to assign you",
  "error_13000":
      "Merchant Infor not found, please call hotline 1900-63-64-88 mPoS.vn Customer Support team will be able to assign you",
  "error_14000": "Can't connect to merchant system please contact with technical to get support.",
  "error_14001": "Connection timed out",
  "error_14002": "Session is expired. Relogin please",
  "error_14003": "Session is expired. Relogin please",
  "error_14004": "Session is expired. Relogin please",
  "error_16002": "Pin server timeout, please try again",
  "error_17000": "You have exceeded your maximum per batch limit, please do settlement",
  "error_19000": "Wrong app id",
  "error_20000":
      "Card declined, please use other card or call MPOS Customer Support hotline 1900-63-64-88 for further assistance.",
  "error_30001":
      "Can not void service transaction, please call MPOS Customer Support hotline 1900-63-64-88 for further assistance.",
  "error_60000": "Temporary code not found",
  "error_55000":
      "Connect to payment partner interrupted create QR code unsuccessful, please come back in a few minutes.",
  "error_999": "DO_CREATE_RECEIPT_TAXI_SUCCESS",
  "error_1000": "DO_SERVICE_SUCCESS",
  "error_1001": "DO_SERVICE_UNSUCESS",
  "error_1002": "Invalid client",
  "error_1003": "Session time out",
  "error_1005": "Service not found",
  "error_1006": "Login token could not be created",
  "error_1007": "Illegal argument",
  "error_1010": "Cannot instantiate class",
  "error_1012": "Response message from api",
  "error_1015": "FILE_NOT_FOUND",
  "error_1016": "INVALID_STATUS",
  "error_1017": "Invalid Config",
  "error_2000": "Read config error",
  "error_3001": "Mpos user's password not matched",
  "error_3002": "Mpos user's mobile not correct",
  "error_3003": "Mpos user's existed",
  "error_3004": "Mpos user's email not correct",
  "error_3005": "Mpos user not found",
  "error_3042": "Account does not exist or has been locked please contact the hotline for assistance.",
  "error_3044": "Unable to find a merchant, please contact the hotline for assistance.",
  "error_3045": "The merchant has not activated, please contact the hotline for support.",
  "error_3101": "Transaction duplicate",
  "error_3102": "Transaction is not success",
  "error_3201": "Merchant bank api not found",
  "error_4000": "GATEWAY_TELCO_NOTFOUND",
  "error_4001": "GATEWAY_SERVICE_NOTFOUND",
  "error_4003": "GATEWAY_MAPPING_NOTFOUND",
  "error_4004": "GATEWAY_TOKEN_NOTFOUND",
  "error_4005": "Transaction already existed",
  "error_4006": "Transaction not found",
  "error_4007": "Transaction is processing",
  "error_5000": "Invalid transaction's type",
  "error_5001": "Invalid transaction's amount",
  "error_5002": "Invalid transaction's status",
  "error_5003": "Notfound bank transaction",
  "error_5004": "UDID not matched",
  "error_5005": "Txid not matched",
  "error_5006": "BANK_API_INVALID_INTERNATIONAL_CARD",
  "error_6000": "Issuer not found",
  "error_7002": "PTI GCN not found",
  "error_7004": "Order is not found",
  "error_7009": "Order Invalid (Amount Status)",
  "error_8001": "Installment sale out rate not found",
  "error_8002": "Udid not found",
  "error_8003": "Udid already used",
  "error_8004": "Cashback Program not found",
  "error_8005": "Cashback Program invalid",
  "error_9000": "Merchant is not allowed",
  "error_10000": "File upload currupted",
  "error_11000": "Oauth authentication failed",
  "error_11001": "Oauth consumer key not found",
  "error_11002": "Oauth decrypt message failed",
  "error_11003": "Oauth token expired",
  "error_11004": "Oauth token secret not matched",
  "error_12000": "Customer code / contract code fail",
  "error_12002": "Phone number is empty",
  "error_12100": "Customer code / contract code was paymented",
  "error_12200": "Product not config channel yet",
  "error_12300": "Product not exist or locked",
  "error_8400": "Call gateway time out",
  "error_8500": "Transaction not exist",
  "error_8600": "Check Api don't support",
  "error_45000": "Bank installment not found",
  "error_45001": "Bin code length invalid",
  "error_45002": "Bin code not found",
  "error_45003": "Day invalid",
  "error_110001": "Reader ID in session and request dont match",
  "error_110002": "Reader ID does not exist in the concurrent map",
  "error_12001": "Connection between client and host expired due to cancellation or timeout",
  "error_12003": "Thread interrupted in long poller probably triggered by a forced destroy",
  "error_50001": "Connection error integrate code",
  "error_50002": "Connection error encrypt / decrypt",
  "error_50003": "Connection encrypt key expired",
  "error_50004": "Connection key fail",
  "error_50005": "Exchange not config",
  "error_50006": "Customer code / contract code fail",
  "error_50007": "Customer code / contract code is empty",
  "error_50008": "Phone number is required",
  "transactions": "Transactions",
  "reports": "Reports",
  "useTips": "User guide",
  "useRules": "Terms of use",
  "titlePaymentSwipeCard": "Card swipe payment",
  "labelPaymentInfo": "Payment information",
  "labelCustomerInfo": "Customer information",
  "labelDescription": "Description",
  "labelPhone": "Phone",
  "labelEmail": "Email",
  "hintEnterAmount": "Enter Amount",
  "hintEnterDescription": "Description of order",
  "hintEnterPhone": "Enter phone number",
  "hintEnterEmail": "Enter email",
  "titleUserInfo": "Account Info",
  "labelTabHome": "Home",
  "labelTabHistory": "History",
  "labelTabWallet": "ATM360",
  "labelTabSetting": "Settings",
  "labelTabAccount": "Account",
  "labelHello": "Hello",
  "labelServiceTitle": "ATM360",
  "labelServiceHistory": "Giao dịch tiện ích",
  "titlePaymentInit": "Pay Now",
  "labelScanCard": "Card Swipe Payment",
  "labelEnterCard": "Enter Card Payment",
  "labelCreateLink": "Payment links",
  "Payment links": "Payment links",
  "Tạo link": "Payment links",
  "labelBnpl": "Buy first pay later",
  "Mua trước trả sau": "Buy first pay later",
  "Buy first pay later": "Buy first pay later",
  "errorAmountEmpty": "Enter Amount",
  "errorAmountMin": "Amount not less than  %sđ",
  "titlePaymentEnterInfo": "Confirm Payment ",
  "labelBillAmount": "Bill Amount",
  "labelBillDescription": "Add notes",
  "labelCustomerPhone": "Customer's phone number",
  "requireCustomerPhone": "*Please enter correct phone number of cardholder",
  "labelCustomerEmail": "Customer's Email",
  "labelScanQR": "QR-Pay",
  "connectingDevice": "Connecting to a Reader",
  "labelChangePassword": "Change Password",
  "labelChangePassword2": "Change Password level 2",
  "buttonChangePassword": "Change Password",
  "buttonSummary": "Transactions summary",
  "labelAccountProtect": "Set up account protection",
  "labelAccountSetting": "Account",
  "labelNotice": "Notice / News",
  "labelCallSupport": "Get Support",
  "labelSupportCenter": "Help center",
  "labelReferralReceiveReward": "Share to get special offers",
  "labelUserInfo": "Account Info",
  "titleEnterPaymentInfo": "Create transactions",
  "buttonConfirm": "Confirm",
  "errorInvalidDescriptionLength": "Description must be 5 -255 characters long",
  "titleEnterCard": "Enter Card Payment",
  "requireCustomerInfo": "Customer information is required",
  "errorOpenUrl": "The link is not valid",
  "copyUrlSuccessful": "Copy Successful",
  "qrListSourceSearch": "Enter the application name",
  "qrListSourceLabel": "List of accepted applications",
  "errorQrCodeEmpty": "Unable to generate this QRcode, please try again or choose another.",
  "otherTransaction": "New Sale ",
  "errorCheckTransactionQr":
      "The transaction has still not paid or in processing, please try again later or call %1s to get help.",
  "labelTelcomProvider": "Select Mobile Network",
  "labelTelcomAmount": "Select Amount",
  "labelTelcomCashback": "Commission received\n%1s",
  "labelAmountPay": "Amount",
  "labelAmountPayColon": "Amount:",
  "labelMoneySource": "Funding Sources",
  "labelBalanceVasWallet": "Account Balance",
  "labelScanCardContent": "ATM/Visa/Master",
  "labelTransactionFee": "Transaction Fees",
  "notLeftMoney": "Recharge account balance to continue paying",
  "labelPasswordLogin": "Password",
  "labelPasswordLogin2": "Password level 2",
  "labelDiscount": "Discount",
  "messageCardUnavailable":
      "You have not registered payment service with mPos card reader, please call %1s to get help.",
  "messageCannotNormalPayment": "You have not permission payment, please call %1s to get help.",
  "messageQRUnavailable": "You have not registered for QR-Code payment service, please call %1s to get help.",
  "vasListProductSearch": "Bill Service Provider",
  "vasListProductLabel": "Service Provider List",
  "vasListCustomerSearch": "Customer Id",
  "earnMoney": "Earn money",
  "titleHistoryTransaction": "Recent transactions",
  "labelAmountPayment": "Amount of payment received",
  "labelFastWithdraw": "Fast Withdrawl",
  "settlement": "Settle",
  "transaction": "Transaction",
  "seeAllTransaction": "All Transaction",
  "authCode": "Approval Code",
  "rrnNo": "Refer Number",
  "canceled": "Voided",
  "settled": "Settled",
  "refunded": " Refunded",
  "processing": "Processing",
  "reversal": "Reversal",
  "titleConfirmPassword": "Confirm Password",
  "labelInputPassword": "Payment Password",
  "noteConfirmPassword":
      "For security reasons to ensure the security of your transaction, please re-enter your login password to continue view transaction history",
  "titleConfirmSettle": "Confirm Settlement ",
  "desConfirmSettle": "Do you want to settle {x} transactions?",
  "titleAllTransaction": "All Transaction",
  "labelSelectAll": "Select all",
  "titleConfirmFastWithdraw": "Quick withdrawal request",
  "desConfirmFastWithdraw": "You will receive money quickly to your bank account after",
  "labelFeeTransaction": "Fee on 1 transaction",
  "labelTariffFastWithdraw": "Quick withdrawal fee table:",
  "labelAmountWithdraw": "Withdraw Amount",
  "noteConfirmFastWithdraw":
      "Please note that all transactions that have NOT been finalized will be closed after the Confirmation of quick withdrawal",
  "labelRequestWithdraw": "Withdrawal Request",
  "labelCancel": "Cancel",
  "labelCustomerCode": "Customer Code",
  "errorUnknown": "Unknown error",
  "contentChangePassword": "Password to secure your account, view history or cancel transactions.",
  "contentChangePasswordNote": "Note: This password is not a transaction authentication password",
  "contentChangePasswordNote2": "Lưu ý: Mật khẩu này không phải mật khẩu đăng nhập.",
  "errorOldPasswordEmpty": "The Password is Required",
  "errorNewPasswordEmpty": "The New Password is Required",
  "errorNewPasswordLength": "Password is at least 6 characters",
  "errorReNewPasswordEmpty": "Re-enter new Password ",
  "errorReNewPasswordMatch": "The Password confirmation does not match",
  "messageChangePasswordSuccess": "Password change successfully",
  "messageChangePasswordSuccess2": "Successfully changed the level 2 password",
  "titleInstallmentTransaction": "Instalment transaction ",
  "labelPaymentMethod": "Payment Method",
  "labelStatus": "Status",
  "labelCardHolderName": "Card Holder Name",
  "labelCardType": "Card Type",
  "labelCardNumber": "Card Number",
  "labelTransactionId": "Transaction ID",
  "labelTransactionIdColon": "Transaction ID:",
  "labelTransactionId2": "Transaction ID",
  "labelDeviceId": "Terminal ID",
  "labelDVCNTId": "Merchant ID",
  "labelTransactionTime": "Time",
  "labelTransactionTimeColon": "Time:",
  "labelBtnReceiveFastMoney": "Quick Withdrawal",
  "labelBtnCancelTransaction": "Cancel Transaction",
  "titleDetailTransaction": "Detail",
  "titleCardTransaction": "Swipe payment card",
  "labelQRPay": "QR-Pay",
  "QR-Pay": "QR-Pay",
  "titleQrCodeTransaction": "QR Transaction ",
  "labelEnterCardPayment": "Enter Card Payment",
  "labelErrCode": "Error Code ",
  "labelEmptyPassword": "The Password is required",
  "labelBatchNumber": "Batch Number",
  "messageExit": "Click the Back button to exit",
  "messageConfirmLogout": "Are you sure you want to logout?",
  "labelStoreName": "Store Name",
  "labelAuthorizedName": "Store rep",
  "labelAuthorizedPhone": "Mobile Number ",
  "labelMerchantCode": "Merchant ID",
  "messageInvalidService": "The service has not been configured",
  "messagePendingTransaction": "Currently your transaction needs to be verified, please do not deliver to the buyer.",
  "labelButtonSendEmail": "Re-send Receipt",
  "labelSendEmail": "Email Receipt",
  "titleAtm360Service": "ATM360",
  "titleServiceCommissions": "Service Commission",
  "labelRechargeMoney": "Add Fund",
  "labelWithdraw": "Withdraw",
  "labelAllPayment": "All Payment ",
  "labelRechargeWithdraw": "Add fund/Withdraw/Receive money",
  "titleRechargeTransaction": "Add Fund ",
  "titlePaymentTransaction": "Transaction ",
  "labelTypePayment": "Method",
  "labelSourceRecharge": "Funding Sources",
  "labelAmountAfter": "Balance After Recharge",
  "labelPaymentService": "Payment Service",
  "labelSourcePayment": "Funding Sources",
  "labelStatusService": "Service Status",
  "labelStatusPayment": "Payment Status",
  "labelAmountCommission": "Commission received",
  "labelSeeBill": "View Receipt ",
  "titleDetailBillWebview": "Receipt Detail",
  "titleSendBill": "Send Receipt",
  "titleSendBillToEmail": "Receipt Send to Email",
  "titleEmailReceiveBill": "Email Address",
  "errorInstallmentNotSupportCard":
      "- Transaction of %1s is not supported by the issuing bank with 0% installment payment.\n\n- Please access the transaction history to CANCEL this transaction then Make another Paynow transaction or Contact the Hotline to convert to Paynow transaction.",
  "errorInstallmentTitle": "Installment payment failed",
  "statusACTIVE": "ACTIVE",
  "statusDENIED": "DENIED",
  "statusPROCESSING": "PROCESSING",
  "statusSUSPENDED": "SUSPENDED",
  "Status": "Status",
  "messageOrderCodeInvalid": "Order code invalid, please input again.",
  "messageOrderCodeExist": "Order code existed, please input again.",
  "messageOrderCodeInput": "Order code can not empty",
  "OrderId": "OrderId",
  "Description": "Description",
  "titleOrderDetail": "Order Detail",
  "BackToAppMerchant": "Back to App Merchant",
  "messageRequireLoginToScanCard": "Stripe card to do transaction you need have card reader please login again",
  "labelAmount": "Amount",
  "swipeCardUseMposReader": "Swipe card by MPOS reader",
  "scanQRCode": "Scan QR code",
  "messageQuickMoneyMarkSuccess": "Your transaction received your transaction was received to success system",
  "titleUpdateApp": "Update App",
  "titleUpdateAppForce": "Update App (force)",
  "titleUpdate": "Update",
  "titleRechargeToWallet": "Add money to ATM360 Account",
  "labelAmountWantRecharge": "Amount",
  "labelRechargeNow": "Topup",
  "titleErrorConfirmRecharge": "Your Balance is not enough",
  "contentErrorConfirmRecharge": "Make a deposit of less than %amount",
  "labelErrorAmount": "Amount is Required",
  "errorConnectReaderPR02":
      "Please power on the MPOS PR02 device to automatically connect the application.\nNote: If unable to connect please follow these steps:\n1. Turn off Bluetooth on your phone then turn it back on\n2. Power off the MPOS PR02 device then turn it on\n3. Go to mPoS.vn and select the payment method Bluetooth Reader (PR02) to reconnect.",
  "errorConnectReaderPR01":
      "Please Pair device on Setting > Bluetooth > Select MPOS device, please close popup message if use other device\nNOTE: If accessory no connect PR01 , please flow step:\n - Go to Bluetooth Setting on your phone > Forget this MPOS device.\n -  On MPOS Device press Yellow button arrow > select Setting > select Bluetooth > select MASTER RESET > Press C to go mPoS.vn home screen.\n - Pair MPOS device into Bluetooth setting on your phone. \n - Into mPoS.vn application select PR01 to connect.",
  "errorConnectReaderAR01": "Please connect device via audio port",
  "titleDetailNotify": "More detail",
  "labelVasBalance": "Account Balance",
  "label360Transaction": "ATM360 Transaction",
  "errorCameraPermissionDeny": "Please grant Camera access in the Phone Settings to continue.",
  "contentErrServiceList": "Under Construction",
  "titleConfirmRecharge": "Confirm Add Funds",
  "titleConfirmWithdraw": "Confirm Withdrawal",
  "titleConnectReaderFail": "Connect Reader Fail",
  "titleConfirmCancel": "Are you sure you want to Cancel this transaction",
  "titleBannerActiveAtm360": "Activate service wallet ATM360",
  "contentBannerActiveAtm360": "Payment services ATM360 by service wallet with more benefit.",
  "titleInputWithdraw": "Enter the amount to withdraw",
  "labelAccountReceiveMoney": "Money receiving account",
  "messagePaymentNotSuccess": "You have transaction not complete! Please complete this transaction",
  "labelErrorAmountPayment": "The payment amount is not valid",
  "titleAtm360Active": "Activate service wallet",
  "labelAtm360Active": "Information required to activate",
  "hintAtm360Active":
      "Email registration must match the email when signing the contract and will be generated a level 2 password to pay",
  "buttonSendInfo": "Send information",
  "buttonSend": "Send",
  "buttonActiveNow": "Activate now",
  "contentActiveNow": "Please activate the service wallet to use",
  "titleSuccessRequest": "Request sent successfully",
  "messageRequestActiveAtm360Success":
      "Your information has been submitted. Operation will verify your information or contact customer service",
  "messageSuccessEmailRecovery": "Successful password recovery request level 2, please check email.",
  "messageNeedActiveAtm360": "To use the function, you must activate the ATM360 payment service wallet.",
  "labelUpdating": "Updating...",
  "labelBackToAppMerchant": "Back to App Merchant",
  "noteWaiting2": "You can review it in the transaction history menu",
  "qrListSourceTitle": "Select QRPay",
  "labelPendingSignature": "Unsign",
  "labelCancelTransaction": "Cancel trans",
  "labelConfirmCancelTransaction": "Confirm cancel transaction",
  "labelPaymentPeriod": "Payment period",
  "labelVoidReason": "Void reason",
  "labelStatusWaitExplain": "Money has not been recorded for the seller, please do not ship!",
  "labelStatusKtExplain": "Note: this transaction supports CANCEL only 60 minutes after payment",
  "labelWaitForDisbursement": "Waiting for disbursement",
  "labelReversal": "Reversal",
  "labelCustomer": "Customer Name",
  "labelApprovalCode": "Approval Code",
  "labelInvoiceCode": "Invoice Code",
  "labelRefNo": "Ref No",
  "errorParamsNotValid": "Params call App mPos is not valid",
  "labelFeeFastReceive": "Additional fees for this service:",
  "labelTransactionAmount": "Transaction amount",
  "titleInstallmentGuide": "To complete the installment transaction of {bankName}, you need to",
  "titleServiceTransaction": "Service Transaction",
  "titleCommissionTransaction": "Receive service commission",
  "messagePermissionNotification":
      "Notification of the application is turned off. Please turn it on so that the app can automatically notify you when the QRCode payment is successful.",
  "setting": "Setting",
  "titleLoginByEmail": "By email address",
  "titlePopupConfirmPayment": "Verify ATM360 transactions",
  "labelNotePopupConfirmPayment": "Please enter ATM360 wallet Password to Authenticate the transaction",
  "transactionVT": "Trans",
  "titlePopupConfirmPass": "Security authentication",
  "labelNotePopupConfirmPass":
      "Please re-enter your login password to: view Transaction history, settlement, cancel transaction ...",
  "enterOrderCode": "Enter order code",
  "errorNotEnoughMoneyWarning":
      "Note: The amount charged to the service wallet does not include the amount accepted to pay by international card issued abroad",
  "descriptionEmpty": "Product description cannot be left blank",
  "slPullUp": "Pull up load more",
  "slFailed": "Load failed, click retry!",
  "slCan": "Release to load more",
  "slEmpty": "No more data",
  "connecting": "Connecting to a device ...",
  "logout": "Logout",
  "buttonPrint": "Print bill",
  "buttonDetail": "Detail",
  "buttonRetry": "Retry",
  "unSignPaymentResult": "Transaction without signature",
  "stillClose": "Transaction unsuccessful, still close this screen?",
  "installment": "Installment",
  "Installment": "Installment",
  "Trả góp": "Installment",
  "moto": "Moto",
  "Moto": "Moto",
  "timeStart": "Start time",
  "timeEnd": "End time",
  "errorTimeRange": "The End Time must be greater than the Start Time",
  "errorTimeRangeInvalid": "Invalid period of time",
  "errorTimeRangeInvalid7": "The time period must not exceed 7 days",
  "titleSummary": "Would you like to summarize for\nthis session?",
  "summaryType": "Trans type",
  "summaryTotalTrans": "Total trans",
  "summaryTotalAmount": "Total amount",
  "summaryTransCard": "Card",
  "summaryTransQR": "QR",
  "summaryTransCb": "Refund/cancel/reverse",
  "summaryTotal": "Total",
  "summaryNote": "Note: Summary data recorded according to transaction history",
  "errorSummaryPrint": "No invoice information available",
  "errorLoginDioCode_7": "Network connection is refuse. Please turn off/on network connection and try again.",
  "outageMessage": "The system is temporarily interrupted. Please try again later or Call hotline to get help.",
  "error_sdk_count_down_call_api": "Please wait %s before making the next request.",

  // mpos-lite
  "errorPassNotMatch": "Password is invalid",
  "warningMaUnSign": "You have got an unfinished transaction. Please open history and complete the transaction.",
  "warningMacqSkipSignature": "Notice: This transaction don't required signature.",
  "titleQRPaymentEnterInfo": "Payment Information",
  "feeForCustomer": "Buyer bear the fee",
  "viewHint": "See the instructions",
  "onlyFor": "Only for",
  "oneCustomerOnePayment": "01 customer paying 01 time",
  "titleNoteQr": "Important note",
  "qrNote1": "This is an order QRcode only for 01 customer paying 01 time only.",
  "qrNote2":
      "If the customer pays many times, it will cause a system error, affecting the seller's time to receive money.",
  "qrNote3": "Your customer will be charged without the transaction being recorded.",
  "addFeeQR": "Extra QRcode fee",

  // splash screen
  "splash_slogan": "Comprehensive payment solution",
  "hotline": "Hotline:",

  // hello screen
  "thank_for_using": "Thank you for your trust in using mPOS payment solution. The application is ready to create the transaction.",
  "start_payment": "Start transaction",

  // main screen
  "create_new_trans": "Create new transaction",
  "menu_settings": "Settings",
  "menu_loyal": "Loyal Card",
  "menu_guide": "User manual",
  "User manual": "User manual",
  "Hướng dẫn sử dụng": "User manual",
  "menu_upgrade": "Upgrade configurations",
  "menu_contact": "Contact",
  "confirm_exit_app": "Press back again to exit",

  "select_type_pay": "Select payment",
  "setting_select_type_pay": "Setting",
  "hint_setting_select_type_pay": "Long press and drag to reorder item",

  "setting_type_pay":"Setup payment method",
  "setting_type_pay_desc":"Arrange payment method location",
  "setting_print":"Print receipt",
  "setting_print_desc":"Setup auto print",

  "setting_auto_print_none":"Printer does not print automatically",
  "setting_auto_print_1":"Printer auto print 1 receipt",
  "setting_auto_print_2":"Printer auto print 2 receipt",
  "setting_auto_print_3":"Printer auto print 3 receipt",

  "languages": "Languages",
  "receipt": " receipt",
  "guild_auto_print": "Receipt is print automatically%s. In case not auto, please press",

  //plural
  "plural_one_second": "%s second",
  "plural_other_second": "%s seconds",

  //p12
  "tv_scan_qr_gg_apple": "QR Apple/Google Pay",
  "tv_scan_qr": "Scan QR Code",
  "tv_pay_success": "Payment Success",
  "msg_close_qr_screen": "If the customer has already paid, you can review it in GD History or activate the recheck order button.",
  "msg_oneCustomerOnePayment": "(Note: 01 customer paying 01 time)",
  "tv_create_qr_again": "Create again",
  "tv_creating_qr": "Đang tạo mã QR",
  "tv_detail_qr_vietqr": "1K - xxx Million",
  "tv_detail_qr_vnpay": "1K - 100 Million",
  "tv_detail_qr_mvisa_master": "xxđ - xx Million",
  "tv_detail_qr_wallet": "1K - 200 Million",
  "tv_name_group_qr_bank": "QR - Bank",
  "tv_name_group_qr_vietqr": "Viet QR",
  "tv_name_group_qr_mvisamaster": "mVisa, MasterPass",
  "tv_name_group_qr_wallet": "Wallet",
  "tv_name_group_qr_default": "Selece Qr",
  "tv_detail_bank_number": "Reference number",
  "tv_push_payment": "Point of sale",
  "msg_noti_payment_success": "Thanh toán thành công %s đồng",
  "msg_err_fetch_config_qr": "Fetch QR config failed. Please contact the hotline for support.",
  "tv_guide_p12_qr": "Give this QR code to Customer for payment via banking application.",
  "tv_guide_p12": "Money will be credited to mPos account as Card transaction.",
  "tv_know_guide": "I know",
  "msg_warning_not_support_pushpayment": "Merchant is not yet supported to pay point of sale, Please contact the hotline for support.",
  "tv_des_disable_display_ag_qr": "Hide the Apple Pay/Google Pay QR code on the homepage & TingBox screen",
  "tv_des_enable_display_ag_qr": "Show the Apple Pay/Google Pay QR code by default on the homepage & TingBox screen.",
  "tv_disable_display_ag_qr": "Disable Default Display",
  "tv_enable_display_ag_qr": "Enable Default Display",
  "tv_title_apple_google_qr": "QR Apple/Google Pay",
  "msg_deception_static_qr": "Customers use the application of more than 40 scanning banks to make payments. Money will be credited to the mPOS account like a swiped GD card. Download static QR, please login with your account email on website mpos.vn",
  "msg_intro_static_qr_ag": "Open your phone camera to scan the QR and pay with Apple Pay or Google Pay",
  "msg_deception_static_qr_gg_apple": "Give this QR code to customers to scan with their phone camera. They will be directed to NextPay’s secure payment page, where they can enter the amount and a description (if needed), then choose to pay quickly using Apple Pay or Google Pay",
  "tv_setting_default_ag_qr": "Default QR for Apple/Google Pay on the homepage",
  "tv_des_setting_default_ag_qr": "If selected, VietQR will no longer be displayed by default on the homepage and TingBox devices",
  "msg_warning_get_token_firebase": "Failed to configure the QR notification receiver code. You may not receive a success message when scanning a static QR. Please change your network connection and log out/log in again.",
  "msg_add_deception": "Add order description",
  "tv_add_deception": "Order description",
  "tv_title_vietQR": "VietQR",
  "VietQR": "VietQR",
  "tv_close_qr": "Close QR?",
  "loading": "Loading...",
  "msgCheckTransactionQrNotPay": "The order has not been paid by the customer yet",
  "tv_no_signature": "Transactions with this notice do not require the cardholder to sign confirmation on the mPOS application.",
  "tv_create_new_order": "Create new order",
  "msg_order_expired": "The order has expired & has not yet been paid by the customer",
  "tv_other_type_qr": "Choose another method",
  "title_qr_wallet": "QR Wallet",
  "QR Wallet": "QR Wallet",
  "QR ví điện tử": "QR Wallet",
  "tv_trans_no_signature": "TRANSACTIONS DO NOT NEED A SIGNATURE",
  "tv_app_version": "Version",
  "tv_guide_scan_qr_link": "Use your phone camera to scan the QR to get the payment link",
  "tv_confirm_void_trans": "Are you want to void transaction %s.",
  "title_link_card": "Payment link",
  "content_note_quick_withdraw1": "Transactions < 4 million = 33,000 VND",
  "content_note_quick_withdraw2": "Transactions >= 4 million = 0.5% * withdrawal amount",
  "title_cost_for_service": "The cost you pay extra for this service:",
  "des_quick_draw": "You will receive quick money on your bank account after click the button",
  "tv_quick_money_mark_success": "Your transaction received your transaction was received to success system",
  "tv_disable_status_bar": "Disable status bar",
  "tv_disable_nav_bar": "Disable navigation bar",
  "tv_setting_auto_dismiss_dlg": "Auto dismiss popup warning, error",
  "title_wait_tcp_screen": "Show waiting transaction screen",
  "title_no": "No",
  "tv_setting_hold_screen": "Keep the screen on",
  "tv_des_setting_hold_screen": "Enable this option to keep the device screen on while the app is running.",

};
