import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/data/model/type_payment.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';
import 'package:mposxs/app/ui/widget/base_bottom_sheet.dart';
import 'package:mposxs/app/ui/widget/common_button.dart';

// SettingOrderPayment

class SettingOrderPayment extends StatefulWidget {
  const SettingOrderPayment({Key? key}) : super(key: key);

  @override
  State<SettingOrderPayment> createState() => _SettingOrderPaymentState();
}

class _SettingOrderPaymentState extends State<SettingOrderPayment> {
  final MyAppController _appController = Get.find<MyAppController>();

  late List<TypePayment> listTypePayment;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    listTypePayment = [];
    listTypePayment.addAll(_appController.listTypePayment);
  }

  @override
  Widget build(BuildContext context) {
    return BaseBottomSheet(
      onPressClose: () => Get.back(),
      title: AppStrings.getString(AppStrings.settingSelectTypePay) ?? '',
      child: Column(
        children: [
          Container(
            height: 260,
            child: ReorderableListView(
              shrinkWrap: true,
              header: Container(
                  padding: const EdgeInsets.all(AppDimens.spaceMedium),
                  color: AppColors.yellow1,
                  child: Text(AppStrings.getString(AppStrings.hintSettingSelectTypePay) ?? '')
              ),
              onReorder: (int oldIndex, int newIndex) {
                setState(() {
                  if (newIndex > oldIndex) {
                    newIndex -= 1;
                  }
                  final element = listTypePayment.removeAt(oldIndex);
                  listTypePayment.insert(newIndex, element);
                });
              },
              children: listTypePayment.map((item) => buildItem(item)).toList(),
            ),
          ),
          Container(
            padding: EdgeInsets.all(AppDimens.spaceMedium),
            child: CommonButton(
              onPressed: () {
                handlePressConfirm();
                Get.back();
              },
              title: AppStrings.getString(AppStrings.buttonConfirm) ?? '',
            ),
          )
        ],
      ),
    );
  }

  void handlePressConfirm() {
    _appController.saveCachePayments(listTypePayment);
  }

  Container buildItem(TypePayment item) {
    return Container(
      key: ValueKey(item),
      padding: EdgeInsets.only(left: AppDimens.spaceMedium),
      child: Container(
        decoration: BoxDecoration(
            // color: Colors.greenAccent,
            border: Border(bottom: BorderSide(width: 1, color: AppColors.pink))
        ),
        child: ListTile(
          contentPadding: EdgeInsets.only(right: AppDimens.spaceMedium),
          // leading: ,
          title: Text(
            '${AppStrings.getString(item.title!)}',
            style: const TextStyle(fontSize: AppDimens.textSizeMedium),
          ),
          trailing: Image.asset(
            AppImages.ic_reorder,
            width: AppDimens.icon24,
          ),
        ),
      ),
    );
  }
}
