import 'package:cashiermodule/constants/style.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/controller/settings_controller.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';
import 'package:mposxs/app/ui/widget/common_header.dart';
import 'package:mposxs/app/ui/widget/common_screen.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';

import '../../../controller/main_controller.dart';

class SettingsScreen extends GetView<SettingsController> {
  final MyAppController _appController = Get.find<MyAppController>();

  final MaterialStateProperty<Icon?> thumbIcon =
  MaterialStateProperty.resolveWith<Icon?>(
        (Set<MaterialState> states) {
      if (states.contains(MaterialState.selected)) {
        return const Icon(Icons.check);
      }
      return const Icon(Icons.close);
    },
  );

  @override
  Widget build(BuildContext context) {
    controller.context = context;
    return CommonScreen(
        header: CommonHeader(
          title: AppStrings.getString(AppStrings.setting),
          headerBackgroundColor: AppColors.white,
          titleColor: AppColors.black,
        ),
        child: SingleChildScrollView(
          scrollDirection: Axis.vertical,
          child: Column(
            children: [
              SizedBox(height: AppDimens.spaceXSmall6,),
              // => select type pay
              MyAppController.isKozenP12orN4() ? SizedBox.shrink() :
              _buildRowItemDouble(icon: AppImages.ic_more_pay,
                  title: AppStrings.getString(AppStrings.setting_type_pay) ?? '',
                  desc: AppStrings.getString(AppStrings.setting_type_pay_desc) ?? '',
                onPress: controller.onPressOrderTypePay
              ),

              // => print
              MyAppController.isKozenP12orN4() ? SizedBox.shrink() :
              _buildRowItemDouble(icon: AppImages.ic_printer_black,
                  title: AppStrings.getString(AppStrings.setting_print) ?? '',
                  desc: AppStrings.getString(AppStrings.setting_print_desc) ?? '',
                  onPress: controller.onPressPrinter
              ),

              // => change pass
              _buildRowItemDouble(icon: AppImages.icPass,
                  // title: AppStrings.labelChangePassword.tr,
                  // desc: AppStrings.labelChangePassword.tr,
                  title: AppStrings.getString(AppStrings.buttonChangePassword) ?? '',
                  desc: AppStrings.getString(AppStrings.contentChangePassword) ?? '',
                  onPress: controller.onPressAccountProtect
              ),
              // => QR apple/google pay
              MyAppController.isKozenP12orN4() ? SizedBox(height: AppDimens.spaceXSmall6,):
                  SizedBox.shrink() ,
              MyAppController.isKozenP12orN4() ? Obx(() => _buildRowItemSingle(
                  icon: AppImages.ic_qrcode,
                  title: '${AppStrings.getString(AppStrings.tv_setting_default_ag_qr)}',
                  widgetRight: Image.asset(
                      controller.showDefaultVietQr.value
                          ? AppImages.ic_toggle_off
                          : AppImages.ic_toggle_on,
                      width: 50),
                  bodyDes: Text('${AppStrings.getString(AppStrings.tv_des_setting_default_ag_qr)}', style: style_S14_W400_HintColor,),
                  onPress: controller.onPressSelectDefautQr))
                  : SizedBox.shrink(),

              SizedBox(height: AppDimens.spaceXSmall6,),
              MyAppController.isKozenP12orN4() ? Obx(() => _buildRowItemSingle(
                  icon: AppImages.icShareMenu,
                  title: '${AppStrings.getString(AppStrings.tv_setting_hold_screen)}',
                  widgetRight: Image.asset(
                      controller.isHoldScreenQr.value
                          ? AppImages.ic_toggle_on
                          : AppImages.ic_toggle_off,
                      width: 50),
                  bodyDes: Text('${AppStrings.getString(AppStrings.tv_des_setting_hold_screen)}', style: style_S14_W400_HintColor,),
                  onPress: controller.onPressChangeHoldLightScreen))
                  : SizedBox.shrink(),
              SizedBox(height: AppDimens.spaceXSmall6,),

              // => advanced settings
              (_appController.isShowAdvancedSettings) ? _buildAdvancedSetting() : SizedBox.shrink(),
              // => language
              _buildRowItemSingle(icon: AppImages.icLanguageBlack,
                  title: AppStrings.getString(AppStrings.languages) ?? '',
                  widgetRight: Image.asset(
                      Get.locale!.languageCode == AppStrings.language_code_vi ? AppImages.ic_language_vi : AppImages.ic_language_en,
                      width: 50),
                  onPress: controller.onPressLanguage
              ),
              SizedBox(height: AppDimens.spaceXSmall6,),
              // => tingbox settings
              (_appController.mqttModel!=null) ? _buildRowItemSingle(
                  icon: AppImages.ic_selected,
                  title: "Sound TingBox",
                  onPress: controller.onPressSoundTingbox,
                  widgetRight: SizedBox.shrink()) : SizedBox.shrink(),
              (_appController.mqttModel!=null) ?SizedBox(height: AppDimens.spaceXSmall6,): SizedBox.shrink(),

              // => logout
              _buildRowItemSingle(icon: AppImages.icLogoutBlack,
                  title: AppStrings.getString(AppStrings.logout) ?? '',
                  widgetRight: Obx(() => Text('Ver: '+controller.appVer.value, style: TextStyle(color: AppColors.gray1),)),
                  onPress: controller.onPressLogout
              ),
              SizedBox(height: AppDimens.spaceXSmall6,)
            ],
          ),
        ));
  }

  _buildRowItemSingleSwitch({required String icon, required String title, required Widget widgetRight}) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: AppDimens.spaceMedium, vertical: AppDimens.spaceXSmall8),
      decoration: BoxDecoration(color: AppColors.white),
      child: Row(
        children: [
          Image.asset(
            icon,
            width: AppDimens.icon20,
            height: AppDimens.icon20,
            fit: BoxFit.contain,
          ),
          SizedBox(width: AppDimens.spaceMedium),
          Expanded(
            flex: 1,
            child: Container(
              child: Text(
                title,
                  style: TextStyle(
                      fontSize: 15,
                      color: AppColors.black,
                      fontFamily: AppFonts.robotoMedium),
              ),
            ),
          ),
          widgetRight
        ],
      ),
    );
  }

  _buildRowItemSingle({required String icon, required String title, onPress, required Widget widgetRight, Widget? bodyDes}) {
    return TouchableWidget(
      onPressed: onPress,
      padding: EdgeInsets.symmetric(horizontal: AppDimens.spaceMedium, vertical: AppDimens.spaceMedium),
      decoration: BoxDecoration(color: AppColors.white),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Image.asset(
            icon,
            width: AppDimens.icon20,
            height: AppDimens.icon20,
            fit: BoxFit.fill,
          ),
          SizedBox(width: AppDimens.spaceMedium),
          Expanded(
            flex: 1,
            child: Container(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                        fontSize: 15,
                        color: AppColors.black,
                        fontFamily: AppFonts.robotoMedium),
                  ),
                  SizedBox(height: 5,),
                  (bodyDes != null) ? bodyDes : SizedBox.shrink()
                ],
              ),
            ),
          ),
          widgetRight
        ],
      ),
    );
  }

  _buildRowItemDouble({required String icon, required String title, required String desc, onPress}) {
    return TouchableWidget(
      onPressed: onPress,
      padding: EdgeInsets.only(left: AppDimens.spaceMedium),
      decoration: BoxDecoration(color: AppColors.white),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: EdgeInsets.only(top: AppDimens.spaceMedium),
            alignment: Alignment.topCenter,
            child: Image.asset(
              icon,
              width: AppDimens.icon20,
              height: AppDimens.icon20,
              fit: BoxFit.contain,
            ),
          ),
          SizedBox(width: AppDimens.spaceMedium),
          Expanded(
            flex: 1,
            child: Container(
                decoration: BoxDecoration(border: Border(bottom: BorderSide(color: AppColors.gray3))),
                padding: EdgeInsets.only(top: AppDimens.spaceMedium, bottom:  AppDimens.spaceMedium, right:  AppDimens.spaceMedium),
                child: Row(
                  children: [
                    Expanded(
                        child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: TextStyle(fontSize: AppDimens.textSizeMedium, color: AppColors.black, fontFamily: AppFonts.robotoMedium),
                        ),
                        SizedBox(height: AppDimens.spaceXSmall6,),
                        Text(
                          desc,
                          style: TextStyle(fontSize: AppDimens.textSizeSmall, color: AppColors.gray1, fontFamily: AppFonts.robotoLight),
                        ),
                      ],
                    )),
                    Image.asset(
                      AppImages.icArrowRight,
                      width: 8,
                      height: 16,
                    )
                  ],
                )),
          ),
        ],
      ),
    );
  }

  Widget _buildAdvancedSetting() {
    return Column(
      children: [
        _buildRowItemSingleSwitch(icon: AppImages.ic_setting_status_bar,
          title: AppStrings.getString(AppStrings.tv_disable_status_bar) ?? '',
          widgetRight: Obx(
                () => Switch(
              thumbIcon: thumbIcon,
              value: controller.isDisableStatusBar.value,
              onChanged: (bool value) {
                controller.onPressSettingDisableNavigationBar(disableStatusBar: value);
              },
            ),
          ),
        ),
        SizedBox(height: AppDimens.spaceXSmall6),
        _buildRowItemSingleSwitch(icon: AppImages.ic_setting_navbar,
          title: AppStrings.getString(AppStrings.tv_disable_nav_bar) ?? '',
          widgetRight: Obx(
                () => Switch(
              thumbIcon: thumbIcon,
              value: controller.isDisableNavBar.value,
              onChanged: (bool value) {
                controller.onPressSettingDisableNavigationBar(disableNavbar: value);
              },
            ),
          ),
        ),
        SizedBox(height: AppDimens.spaceXSmall6),
        _buildRowItemSingleSwitch(icon: AppImages.icTabSetting,
          title: AppStrings.getString(AppStrings.tv_setting_auto_dismiss_dlg) ?? '',
          widgetRight: Obx(
                () => Switch(
              thumbIcon: thumbIcon,
              value: controller.isAutoDismissDlg.value,
              onChanged: (bool value) {
                controller.onPressAutoDismissDlg(value);
              },
            ),
          ),
        ),
        SizedBox(height: AppDimens.spaceXSmall6,),
        (_appController.userInfo!.config?.connectType == 4) ? Column(
          children: [
            _buildRowItemSingleSwitch(icon: AppImages.icTabSetting,
              title: AppStrings.getString(AppStrings.title_wait_tcp_screen) ?? '',
              widgetRight: Obx(
                    () => Switch(
                  thumbIcon: thumbIcon,
                  value: Get.find<MainController>().isShowWaitingTcpScreen.value,
                  onChanged: (bool value) {
                    controller.onPressShowWaitTransScreen();
                  },
                ),
              ),
            ),
            SizedBox(height: AppDimens.spaceXSmall6,),
          ],
        ) : SizedBox.shrink()
      ],
    );
  }
}
