import 'package:cashiermodule/widget_custom/common_button.dart';
import 'package:flutter/material.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/screen/settings/bottomsheet_setting_language.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';
import 'package:mposxs/app/ui/widget/base_bottom_sheet.dart';

class SettingSoundTingbox extends StatefulWidget {
  final List<ObjSettingSelect>? listSetting;
  final ObjSettingSelect? valueSelected;
  final Function? onSelectItem;
  final Function onPressConfirm;
  final String? titleScreen;
  const SettingSoundTingbox({Key? key, this.titleScreen, this.listSetting,
    this.onSelectItem,
    required this.onPressConfirm,
    this.valueSelected})
: super(key: key);

  @override
  State<SettingSoundTingbox> createState() => _SettingSoundTingboxState();
}

class _SettingSoundTingboxState extends State<SettingSoundTingbox> {
  @override
  Widget build(BuildContext context) {
    return BaseBottomSheet(
      title: widget.titleScreen??AppStrings.getString(AppStrings.languages) ?? '',
      child: Column(
        children: [
          ListView.builder(
            itemCount: widget.listSetting!.length,
            shrinkWrap: true,
            itemBuilder: (context, index) {
            return ListTile(
              title: Text(widget.listSetting![index].title),
              trailing: widget.listSetting![index].value==widget.valueSelected!.value?
                  Image.asset(AppImages.ic_selected, width: AppDimens.icon20, height: AppDimens.icon20):
                  SizedBox.shrink()
              ,
              onTap: (){
                widget.onSelectItem!(widget.listSetting![index]);
                setState(() {
                  widget.valueSelected!.value = widget.listSetting![index].value;
                });
              },
            );
          },),
          CommonButton(
            onPressed: () {
              widget.onPressConfirm();
            },
            color: AppColors.primary,
            textColor: AppColors.white,
            textSize: 18,
            title: AppStrings.getString(AppStrings.buttonConfirm),
            minWidth: MediaQuery.of(context).size.width - 30,
            height: 50,
          ),
          // CommonButton(
          //   onPressed: () {
          //     NPTingTingSpeakerHandler.receivedCurrencyAmountSound(123456, hasMerge: false);
          //   },
          //   color: AppColors.primary,
          //   textColor: AppColors.white,
          //   textSize: 18,
          //   title: "test sound",
          //   minWidth: MediaQuery.of(context).size.width - 30,
          //   height: 50,
          // ),
          SizedBox(height: 10,)
        ],
      ),
    );
  }
}
//
// class ObjSettingSelect{
//   String title;
//   var value;
//
//   ObjSettingSelect(this.title, this.value);
// }
