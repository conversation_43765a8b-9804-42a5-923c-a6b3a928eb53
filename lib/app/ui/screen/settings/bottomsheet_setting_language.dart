import 'package:flutter/material.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';
import 'package:mposxs/app/ui/widget/base_bottom_sheet.dart';

class SettingLanguage extends StatefulWidget {
  final List<ObjSettingSelect>? listSetting;
  final ObjSettingSelect? valueSelected;
  final Function? onSelectItem;
  final String? titleScreen;
  const SettingLanguage({Key? key, this.titleScreen, this.listSetting, this.onSelectItem, this.valueSelected}) : super(key: key);

  @override
  State<SettingLanguage> createState() => _SettingLanguageState();
}

class _SettingLanguageState extends State<SettingLanguage> {
  @override
  Widget build(BuildContext context) {
    return BaseBottomSheet(
      title: widget.titleScreen??AppStrings.getString(AppStrings.languages) ?? '',
      child: ListView.builder(
        itemCount: widget.listSetting!.length,
        shrinkWrap: true,
        itemBuilder: (context, index) {
        return ListTile(
          title: Text(widget.listSetting![index].title),
          trailing: widget.listSetting![index].value==widget.valueSelected!.value?
              Image.asset(AppImages.ic_selected, width: AppDimens.icon20, height: AppDimens.icon20):
              SizedBox.shrink()
          ,
          onTap: (){
            widget.onSelectItem!(widget.listSetting![index]);
          },
        );
      },),
    );
  }
}

class ObjSettingSelect{
  String title;
  var value;

  ObjSettingSelect(this.title, this.value);
}
