import 'package:cashiermodule/constants/style.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/controller/payment_init_controller.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/screen/payment/widget/normal_input_amount_keyboard.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_common_style.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';
import 'package:mposxs/app/ui/widget/common_header.dart';
import 'package:mposxs/app/ui/widget/common_screen.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/app_validation.dart';
import 'package:mposxs/app/util/number_to_string.dart';

class PaymentInitScreen extends GetView<PaymentInitController> {
  final bool? hideHeader;
  final bool? isP12;
  final MyCallbackAmount? callback;
  final String? ipAddress;

  PaymentInitScreen({this.callback, this.isP12, this.hideHeader, this.ipAddress});

  Widget buildInitPaymentP12() {
    controller.setCallback(callback);

    return Column(
      children: <Widget>[
        InkWell(
          onTap: () => controller.onPressDes(),
          child: Container(
            padding: EdgeInsets.all(20),
            margin: EdgeInsets.symmetric(horizontal: 20),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Image.asset(AppImages.ic_edit_primary),
                SizedBox(
                  width: 5,
                ),

                Flexible(
                    child: Obx(
                          () => RichText(
                        overflow: TextOverflow.ellipsis,
                        text: TextSpan(
                            style: style_S14_W400_BlueColor,
                            text: !isNullEmpty(controller.textDes.value) ? controller.textDes.value : AppStrings.getString(AppStrings.msg_add_deception)
                        ),

                      ),
                    )
                ),
              ],
            ),
          ),
        ),
        // => amount suggest
        Obx(
          () => (controller.isShowSuggest.value == true)
              ? Container(
                  height: 56,
                  padding: EdgeInsets.only(left: 10, right: 10),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: (controller.listSuggest ?? [])
                        .map((element) => Expanded(
                            flex: 1,
                            child: TouchableWidget(
                              padding: EdgeInsets.only(left: 5, right: 5),
                              margin: EdgeInsets.only(left: 5, right: 5),
                              height: 34,
                              decoration: BoxDecoration(
                                color: AppColors.black.withOpacity(0.05),
                                borderRadius: BorderRadius.circular(17),
                              ),
                              child: FittedBox(
                                fit: BoxFit.scaleDown,
                                child: Text(
                                  AppUtils.formatCurrency(element) + 'đ',
                                  style: TextStyle(
                                      fontSize: 16,
                                      color: AppColors.blackBlueText,
                                      fontFamily: AppFonts.robotoMedium),
                                ),
                              ),
                              onPressed: () =>
                                  controller.onPressSuggest(element),
                            )))
                        .toList(),
                  ))
              : SizedBox.shrink(),
        ),
        Expanded(
          child: Obx(
            () => Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(
                  flex: 10,
                  child: NormalInputAmountKeyboard(
                    onPressKey: controller.onPressChar,
                    onLongPressKey: controller.onLongPressChar,
                  ),
                ),
                //type payment
                Expanded(
                  flex: 4,
                  child: Container(
                    padding: EdgeInsets.fromLTRB(10, 0, 0, 5),
                    child: IntrinsicWidth(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: <Widget>[
                          (controller.getListBtn().length > 0
                              ? Expanded(
                                  flex: 3,
                                  child: TouchableWidget(
                                    onPressed: () {
                                      controller.handleSelectTypePay(
                                          controller.getListBtn()[0].typePay);
                                    },
                                    decoration: BoxDecoration(
                                      color: controller.getListBtn()[0].bgColor,
                                      borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(10),
                                          bottomLeft: Radius.circular(10)),
                                    ),
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          '${AppStrings.getString(controller.getListBtn()[0].title)}',
                                          style: style_S18_W600_WhiteColor,
                                          textAlign: TextAlign.center,
                                        ),
                                        (controller.getListBtn()[0].sub_name == null || controller.getListBtn()[0].sub_nameEn == null) ? SizedBox.shrink() :
                                        Text(
                                          '${(Get.locale.toString() == 'vi_VN') ? controller.getListBtn()[0].sub_name : controller.getListBtn()[0].sub_nameEn}',
                                          style: style_S12_W400_WhiteColor,
                                          textAlign: TextAlign.center,
                                        ),
                                      ],
                                    ),
                                  ),
                                )
                              : SizedBox.shrink()),
                          SizedBox(height: 3),
                          (controller.getListBtn().length > 1
                              ? Expanded(
                                  flex: 3,
                                  child: TouchableWidget(
                                    onPressed: () {
                                      controller.handleSelectTypePay(
                                          controller.getListBtn()[1].typePay);
                                    },
                                    decoration: BoxDecoration(
                                      color: controller.getListBtn()[1].bgColor,
                                      borderRadius: BorderRadius.only(
                                          topLeft: Radius.circular(10),
                                          bottomLeft: Radius.circular(10)),
                                    ),
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      children: [
                                        Text(
                                          '${AppStrings.getString(controller.getListBtn()[1].title)}',
                                          style: style_S18_W600_WhiteColor,
                                          textAlign: TextAlign.center,
                                        ),
                                        (controller.getListBtn()[1].sub_name == null || controller.getListBtn()[1].sub_nameEn == null) ? SizedBox.shrink() :
                                        Text(
                                          '${(Get.locale.toString() == 'vi_VN') ? controller.getListBtn()[1].sub_name : controller.getListBtn()[1].sub_nameEn}',
                                          style: style_S12_W400_WhiteColor,
                                          textAlign: TextAlign.center,
                                        ),
                                      ],
                                    ),
                                  ),
                                )
                              : SizedBox.shrink()),
                          SizedBox(
                            height: 3,
                          ),
                          Expanded(
                            flex: 2,
                            child: TouchableWidget(
                              padding: EdgeInsets.symmetric(horizontal: 20),
                              height: 50,
                              decoration: BoxDecoration(
                                color: AppColors.bgButton,
                                borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(10),
                                    bottomLeft: Radius.circular(10)),
                              ),
                              child: Image.asset(
                                AppImages.ic_more_pay_white,
                                width: 20,
                                height: 20,
                              ),
                              onPressed: () {
                                controller.onPressSelectTypePay();
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    controller.context = context;
    return isP12! ? buildInitPaymentP12() : buildInitPaymentNormal(context);
  }

  buildInitPaymentNormal(BuildContext context) {
    return CommonScreen(
      mainBackgroundColor: AppColors.white,
      header: hideHeader == true
          ? SizedBox.shrink()
          : CommonHeader(
              title: AppStrings.getString(AppStrings.createNewTrans) ?? '',
            ),
      child: Column(
        children: <Widget>[
          // => amount
          Container(
            height: MyAppController.isKozenP8() ? 140 : 100,
            margin: const EdgeInsets.all(5),
            child: Obx(
              () => Column(
                children: <Widget>[
                  // amount
                  Expanded(
                    flex: 1,
                    child: Container(
                      width: MediaQuery.of(context).size.width,
                      child: Row(
                        children: [
                          Container(width: 25, height: 25),
                          Expanded(
                            flex: 1,
                            child: FittedBox(
                              fit: BoxFit.scaleDown,
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: <Widget>[
                                  Text(
                                    controller.valueInput.value != null &&
                                            controller.valueInput.value.length >
                                                0
                                        ? AppUtils.formatCurrency(int.parse(
                                            controller.valueInput.value))
                                        : '',
                                    style: TextStyle(
                                        fontSize:
                                            MyAppController.isKozenP8() ? 56 : 46,
                                        color: AppColors.primary,
                                        fontFamily: AppFonts.robotoMedium),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.only(
                                        bottom: 20, left: 2),
                                    child: Text(
                                      'đ',
                                      style: TextStyle(
                                          fontSize: 20,
                                          color: AppColors.primary
                                              .withOpacity(0.81),
                                          decoration: TextDecoration.underline),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          !isNullEmpty(controller.valueInput.value) &&
                                  controller.valueInput.value != '0'
                              ? TouchableWidget(
                                  width: 35,
                                  height: 35,
                                  padding: EdgeInsets.all(0),
                                  margin: EdgeInsets.only(left: 10),
                                  child: Image.asset(
                                    AppImages.icClearText,
                                    width: 20,
                                    height: 20,
                                  ),
                                  onPressed: controller.onPressClearValue,
                                )
                              : Container(width: 25, height: 25),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(
                      height: (!isNullEmpty(controller.valueInput.value) &&
                              controller.valueInput.value != '0')
                          ? 5
                          : 0),
                  // => amount text
                  Center(
                    child: Text(
                      (!isNullEmpty(controller.valueInput.value) &&
                              controller.valueInput.value != '0')
                          ? Get.locale!.languageCode != 'vi'
                              ? NumberToString().readNumberEn(
                                  int.parse(controller.valueInput.value))
                              : NumberToString().readNumber(
                                  int.parse(controller.valueInput.value))
                          : '',
                      textAlign: TextAlign.center,
                      style: TextStyle(
                          fontSize: MyAppController.isKozenP8() ? 18 : 15,
                          fontFamily: AppFonts.robotoItalic,
                          color: AppColors.blackText),
                    ),
                  ),
                ],
              ),
            ),
          ),
          // => amount suggest
          Container(
            height: 56,
            padding: EdgeInsets.only(left: 10, right: 10),
            child: Obx(() => Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: (controller.listSuggest ?? [])
                      .map((element) => Expanded(
                          flex: 1,
                          child: TouchableWidget(
                            padding: EdgeInsets.only(left: 5, right: 5),
                            margin: EdgeInsets.only(left: 5, right: 5),
                            height: 34,
                            decoration: BoxDecoration(
                              color: AppColors.black.withOpacity(0.05),
                              borderRadius: BorderRadius.circular(17),
                            ),
                            child: FittedBox(
                              fit: BoxFit.scaleDown,
                              child: Text(
                                AppUtils.formatCurrency(element) + 'đ',
                                style: TextStyle(
                                    fontSize: 16,
                                    color: AppColors.blackBlueText,
                                    fontFamily: AppFonts.robotoMedium),
                              ),
                            ),
                            onPressed: () => controller.onPressSuggest(element),
                          )))
                      .toList(),
                )),
          ),
          // => description
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(width: 0.5, color: AppColors.grayBorder),
              ),
            ),
            child: Obx(() => Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Image.asset(AppImages.ic_edit, width: 18, height: 18),
                    Expanded(
                      flex: 1,
                      child: TouchableWidget(
                        height: MyAppController.isKozenP8() ? 56 : 46,
                        onPressed: controller.onPressDes,
                        child: Align(
                          alignment: Alignment.centerLeft,
                          child: Text(
                            isNullEmpty(controller.textDes.value)
                                ? AppStrings.getString(AppStrings.labelBillDescription) ?? ''
                                : controller.textDes.value,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              fontSize: isNullEmpty(controller.textDes.value)
                                  ? 14
                                  : 16,
                              fontFamily: isNullEmpty(controller.textDes.value)
                                  ? AppFonts.robotoRegular
                                  : AppFonts.robotoMedium,
                              color: isNullEmpty(controller.textDes.value)
                                  ? AppColors.greyText
                                  : AppColors.blackText,
                            ),
                          ),
                        ),
                      ),
                    ),
                    (!isNullEmpty(controller.textDes.value)
                        ? TouchableWidget(
                            width: 20,
                            height: 20,
                            padding: EdgeInsets.all(0),
                            child: Image.asset(
                              AppImages.icClearText2,
                              width: 15,
                              height: 15,
                            ),
                            onPressed: controller.onPressClearDes,
                          )
                        : SizedBox.shrink()),
                  ],
                )),
          ),
          Expanded(
            flex: 1,
            child: NormalInputAmountKeyboard(
              onPressKey: controller.onPressChar,
            ),
          ),
          Container(
            color: AppColors.pink,
            // margin: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom + (AppController.isKozenP8()?20:10)),
            padding: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
            child: Obx(() => Row(
                  children: <Widget>[
                    (controller.getListBtn().length > 0 &&
                            controller.getListBtn()[0] != null
                        ? Expanded(
                            flex: 1,
                            child: TouchableWidget(
                              height: AppDimens.heightButton,
                              onPressed: () {
                                controller.handleSelectTypePay(
                                    controller.getListBtn()[0].typePay);
                              },
                              decoration: BoxDecoration(
                                color: controller.getListBtn()[0].bgColor,
                                borderRadius: BorderRadius.circular(6),
                              ),
                              margin: EdgeInsets.only(right: 5),
                              child: Text(
                                '${AppStrings.getString(controller.getListBtn()[0].title)}',
                                // controller.getListBtn()[0].title,
                                style: buttonTextStyle(),
                              ),
                            ),
                          )
                        : SizedBox.shrink()),
                    (controller.getListBtn().length > 1 &&
                            controller.getListBtn()[1] != null
                        ? Expanded(
                            flex: 1,
                            child: TouchableWidget(
                              height: AppDimens.heightButton,
                              onPressed: () {
                                controller.handleSelectTypePay(
                                    controller.getListBtn()[1].typePay);
                              },
                              decoration: BoxDecoration(
                                color: controller.getListBtn()[1].bgColor,
                                borderRadius: BorderRadius.circular(6),
                              ),
                              margin: EdgeInsets.only(left: 5),
                              child: Text(
                                // controller.getListBtn()[1].title,
                                '${AppStrings.getString(controller.getListBtn()[1].title)}',
                                style: buttonTextStyle(),
                              ),
                            ),
                          )
                        : SizedBox.shrink()),
                    TouchableWidget(
                      width: 50,
                      height: AppDimens.heightButton,
                      padding: EdgeInsets.all(0),
                      decoration: BoxDecoration(
                        color: AppColors.orange1,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Image.asset(
                        AppImages.ic_more_pay,
                        width: 20,
                        height: 20,
                      ),
                      margin: EdgeInsets.only(left: 10),
                      onPressed: () {
                        controller.onPressSelectTypePay();
                      },
                    )
                  ],
                )),
          ),

          (ipAddress != null && ipAddress!.isNotEmpty) ? Container(
            width: MediaQuery.of(context).size.width,
            padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text('${AppStrings.getString(AppStrings.waitingTransTCP)}', style: style_S14_W600_BlackColor,),
                Text('Ip: $ipAddress', style: style_S14_W600_BlackColor,),
              ],
            ),
          ) :SizedBox.shrink(),

        ],
      ),
    );
  }
}

typedef MyCallbackAmount = void Function(String data);
