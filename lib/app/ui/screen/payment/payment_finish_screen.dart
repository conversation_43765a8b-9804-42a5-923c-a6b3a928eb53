import 'dart:developer' as dev;

import 'package:cashiermodule/constants/style.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/controller/payment_finish_controller.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/screen/payment/widget/payment_finish_p12.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';
import 'package:mposxs/app/ui/widget/common_button.dart';
import 'package:mposxs/app/ui/widget/common_screen.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/app_validation.dart';
import 'package:mposxs/app/util/mpos_constant.dart';

class PaymentFinishScreen extends GetView<PaymentFinishController> {
  Future<bool> onWillPop() {
    controller.onPressNewTransaction();
    return Future.value(false);
  }

  @override
  Widget build(BuildContext context) {
    controller.context = context;
    return WillPopScope(
      onWillPop: onWillPop,
      child: CommonScreen(
        mainBackgroundColor: AppColors.white,
        child: Stack(
          children: [
            Obx(() => controller.tempPrinterWidget.value),
            Container(
              color: AppColors.white,
              child: buildScreen(context),
            ),
          ],
        ),
      ),
    );
  }

  buildScreen(BuildContext context) {
    dev.log('build screen Payment finish');
    return MyAppController.isKozenP12orN4()
        ? Obx (
      () => PaymentFinishP12(
              countdown: controller.countDownAutoBack.value,
              paymentResult: controller.paymentResult.value,
              onPressHome: controller.onPressGoHome,
              onPressNewTrans: controller.onPressNewTransaction,
              onPressQuickDraw: controller.onPressQuickDraw,
              allowQuickDraw: controller.allowQuickDraw.value,
              wgSkipSignature: Obx(() => _buildViewSkipSignature()),
              onPressPrint: controller.printTransactionReceiptLocal,
              isPaymentInstallmentErr: controller.isPaymentInstallmentErr.value,
            ),
        )
        : Column(
            children: [
              // => amount + image|text result
              // Expanded(flex: 2, child: buildTopScreen(context)),
              buildTopScreen(context),
              // => info trans
              Expanded(
                // flex: 5,
                child: controller.statePayment == MposConstant.PAYMENT_STATE_SUCCESS
                    ? buildCenterScreenSuccess(context)
                    : buildCenterScreenFail(context),
              ),
              Container(
                width: Get.width,
                color: AppColors.white,
                padding: EdgeInsets.all(AppDimens.spaceMedium),
                child: CommonButton(
                  minWidth: Get.width - AppDimens.spaceLarge32,
                  onPressed: controller.onPressNewTransaction,
                  title: AppStrings.getString(AppStrings.titleTaoGiaoDichKhac) ?? '',
                  color: AppColors.bgButton1,
                  textColor: AppColors.bgButton,
                  elevation: 0,
                  height: 50,
                ),
              )
            ],
          );
  }

/*  Column buildOldScreen(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: <Widget>[
        Expanded(
          flex: 1,
          child: SingleChildScrollView(
            child: Column(
              children: <Widget>[
                Obx(() => Container(
                      height: 80,
                      child: controller.showAnimation.value
                          ? Lottie.asset(
                              controller.iconLottieName.value,
                              fit: BoxFit.contain,
                              repeat: false,
                            )
                          : SizedBox.shrink(),
                    )),
                Padding(
                  padding: const EdgeInsets.only(top: 15),
                  child: buildAmountWidget(),
                ),
                (!isNullEmpty(controller.notePayment)
                    ? Container(
                        margin: EdgeInsets.only(top: 10, left: 20, right: 20),
                        child: Text(
                          controller.notePayment,
                          style: TextStyle(color: controller.statusColor, fontSize: 16, fontFamily: AppFonts.robotoRegular),
                          textAlign: TextAlign.center,
                        ),
                      )
                    : SizedBox.shrink()),
                (!isNullEmpty(controller.transactionId)
                    ? buildDetailTransOld(context)
                    : SizedBox.shrink()),
                Container(
                  padding: EdgeInsets.only(top: 15, left: 30, right: 30),
                  child: CommonButton(
                    minWidth: Get.width - 60,
                    onPressed: controller.onPressNewTransaction,
                    title: AppStrings.titleTaoGiaoDichKhac2.tr,
                    color: AppColors.primary,
                    textColor: AppColors.white,
                    elevation: 0,
                    height: 50,
                  ),
                ),
                TouchableWidget(
                  onPressed: AppUtils.openCallPhoneSupport,
                  margin: EdgeInsets.only(top: 15, bottom: 5, left: 30, right: 30),
                  child: RichText(
                    textAlign: TextAlign.center,
                    text: TextSpan(
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.greyTextContent,
                      ),
                      children: <TextSpan>[
                        TextSpan(text: '${AppStrings.titleHotlineHoTro.tr}: '),
                        TextSpan(text: MposConstant.SUPPORT_PHONE, style: TextStyle(color: AppColors.primary)),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        AppController.isSupportPrinter() && controller.statePayment == MposConstant.PAYMENT_STATE_SUCCESS
            ? Container(
                margin: EdgeInsets.symmetric(vertical: 15, horizontal: 30),
                child: TouchableWidget(
                  padding: const EdgeInsets.all(0),
                  decoration: BoxDecoration(color: AppColors.mainBackground, borderRadius: BorderRadius.circular(6)),
                  onPressed: controller.inputData.serviceType == MposConstant.QR_PAYMENT
                      ? controller.printTransactionReceiptLocalQR
                      : controller.printTransactionReceiptLocal,
                  child: DottedBorder(
                    borderType: BorderType.RRect,
                    dashPattern: [3, 3],
                    radius: Radius.circular(6),
                    color: AppColors.dash2,
                    child: Container(
                      height: 50,
                      width: MediaQuery.of(context).size.width - 60,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Image.asset(AppImages.ic_print, width: 25, height: 25),
                          SizedBox(width: 10),
                          Text(
                            AppStrings.buttonPrint.tr,
                            style: buttonTextStyle(color: AppColors.blackText2),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              )
            : SizedBox.shrink(),
      ],
    );
  }*/

  Row buildAmountWidget() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          '${AppUtils.formatCurrency(controller.amountPayment)}',
          style: TextStyle(color: AppColors.white, fontSize: AppDimens.textSizeLarge26, fontFamily: AppFonts.robotoMedium),
        ),
        Padding(
          padding: const EdgeInsets.only(bottom: 10),
          child: Text(
            'đ',
            style: TextStyle(
                fontSize: 18, color: AppColors.white, fontFamily: AppFonts.robotoMedium, decoration: TextDecoration.underline),
          ),
        ),
      ],
    );
  }

  buildCenterScreenSuccess(BuildContext context) {
    return SingleChildScrollView(
      child: Container(
        color: AppColors.white,
        child: Column(
          children: [
            // => success: trId + btn detail
            (!isNullEmpty(controller.transactionId)
                ? buildDetailTransNew(context)
                : SizedBox.shrink()),
            // SizedBox(
            //   height: AppDimens.spaceLarge,
            // ),
            // => text note: trans skip signature
            Obx(() => _buildViewSkipSignature()),
            // => btn print
            Obx(() => buildViewPrint()),

            // => send receipt to email
            // GestureDetector(
            //   onTap: (){
            //     dev.log('click send email');
            //   },
            //   child: Row(
            //     mainAxisAlignment: MainAxisAlignment.center,
            //     children: [
            //       Image.asset(
            //         AppImages.ic_send_email,
            //         width: AppDimens.icon28,
            //       ),
            //       SizedBox(
            //         width: AppDimens.spaceXSmall6,
            //       ),
            //       Text(
            //         AppStrings.labelButtonSendEmail.tr,
            //         textAlign: TextAlign.center,
            //       )
            //     ],
            //   ),
            // )
          ],
        ),
      ),
    );
  }

  buildViewPrint() {
    String textGuildAutoPrint = controller.numAutoPrint.value>0?
        AppStrings.getString(AppStrings.guild_auto_print)!.replaceFirst("%s", ' (${controller.numAutoPrint.toString()} ${AppStrings.getString(AppStrings.receipt)} )')
        :'';
    dev.log('buildViewPrint: numAutoPrint=${controller.numAutoPrint} text=$textGuildAutoPrint');
    return MyAppController.isSupportPrinter()
        ?
        Padding(
          padding: EdgeInsets.symmetric(horizontal: AppDimens.spaceLarge, vertical: AppDimens.spaceMedium),
          child: RichText(
            textAlign: TextAlign.center,
            text: TextSpan(
              style: TextStyle(
                fontSize: 14,
                color: AppColors.greyTextContent,
              ),
              children: <TextSpan>[
                controller.numAutoPrint.value>0?
                TextSpan(
                    text: '$textGuildAutoPrint: ',
                    // text: '${AppStrings.guild_auto_print.tr}: ',
                    style: TextStyle(
                      // fontSize: 16,
                      color: Colors.transparent,
                      shadows: [Shadow(offset: Offset(0, -4), color: AppColors.greyTextContent)],
                    )
                ): TextSpan(),
                TextSpan(
                  text: AppStrings.getString(AppStrings.buttonPrint) ?? '',
                  style: TextStyle(
                    // fontSize: 16,
                    color: Colors.transparent, // Step 2 SEE HERE
                    shadows: [Shadow(offset: Offset(0, -4), color: AppColors.primary)], // Step 3 SEE HERE
                    decoration: TextDecoration.underline,
                    decorationStyle: TextDecorationStyle.solid,
                    decorationColor: AppColors.primary,
                  ),
                  recognizer: TapGestureRecognizer()..onTap = () {
                    // dev.log('-->click richtext');
                    controller.onPressPrint();
                  },
                ),
              ],
            ),
          ),
        )
        : SizedBox.shrink();
  }

  Container buildDetailTransNew(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(AppDimens.spaceMedium),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(AppDimens.radiusMedium)),
        color: AppColors.success,
      ),
      child: Column(
        children: [
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(AppDimens.radiusMedium)),
              color: AppColors.white,
            ),
            margin: EdgeInsets.all(1),
            padding: EdgeInsets.all(AppDimens.spaceMedium),
            child: Table(
              defaultColumnWidth: IntrinsicColumnWidth(),
              children: [
                TableRow( children: [
                  Text(AppStrings.getString(AppStrings.labelAmountPayColon) ?? '', style: TextStyle(color: AppColors.greyTextContent),),
                  Container(
                    alignment: Alignment.centerRight,
                    child: Text('${AppUtils.formatCurrency(controller.amountPayment)} VNĐ',
                      style: TextStyle(fontSize: AppDimens.textSizeMedium, fontFamily: AppFonts.robotoBold),
                      )),
                ]),
                TableRow( children: [
                  Container(
                    margin: EdgeInsets.symmetric(vertical: AppDimens.spaceXSmall6),
                    child: Text(AppStrings.getString(AppStrings.labelTransactionIdColon) ?? '', style: TextStyle(color: AppColors.greyTextContent))),
                  Container(
                    margin: EdgeInsets.symmetric(vertical: AppDimens.spaceXSmall6),
                    alignment: Alignment.centerRight,
                    child: Text(controller.transactionId!)),
                ]),
                TableRow( children: [
                  Text(AppStrings.getString(AppStrings.labelTransactionTimeColon) ?? '', style: TextStyle(color: AppColors.greyTextContent)),
                  Container(
                      alignment: Alignment.centerRight,
                      child: Text(controller.transactionTime)),
                ]),
              ],
            ),
          ),
          TouchableWidget(
            onPressed: controller.onPressTransactionId,
            padding: EdgeInsets.symmetric(vertical: 6),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                // fake for center
                SizedBox(),
                SizedBox(width: AppDimens.spaceMedium,),
                Text(
                  AppStrings.getString(AppStrings.buttonDetail) ?? '',
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.end,
                  style: TextStyle(
                    fontSize: 16,
                    fontFamily: AppFonts.robotoMedium,
                    color: AppColors.white,
                  ),
                ),
                SizedBox(width: AppDimens.spaceMedium,),
                Icon(Icons.arrow_forward_ios, size: 14, color: AppColors.white),
              ],
            ),
          )
        ],
      ),
    );
  }

/*  Container buildDetailTransOld(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 30),
      margin: EdgeInsets.only(top: 15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Row(
            children: <Widget>[
              Expanded(
                flex: 1,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${AppStrings.labelTransactionId.tr}:',
                      style: TextStyle(fontSize: 14, color: AppColors.greyTextContent, fontFamily: AppFonts.robotoRegular),
                    ),
                    Text(
                      controller.transactionId,
                      style: TextStyle(fontSize: 16, color: AppColors.blackText, fontFamily: AppFonts.robotoMedium),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              TouchableWidget(
                onPressed: controller.onPressTransactionId,
                padding: EdgeInsets.symmetric(vertical: 10),
                child: Row(
                  children: <Widget>[
                    Text(
                      AppStrings.buttonDetail.tr,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      textAlign: TextAlign.end,
                      style: TextStyle(
                        fontSize: 16,
                        fontFamily: AppFonts.robotoMedium,
                        color: AppColors.primary,
                      ),
                    ),
                    Icon(Icons.arrow_forward_ios, size: 14, color: AppColors.primary),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: 8),
          Dash(
            length: MediaQuery.of(context).size.width - 60,
            direction: Axis.horizontal,
            dashLength: 3.5,
            dashGap: 3.5,
            dashColor: AppColors.dash,
            dashThickness: 1,
          ),
        ],
      ),
    );
  }*/

  buildCenterScreenFail(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.only(
            topLeft: Radius.circular(AppDimens.radiusLarge), topRight: Radius.circular(AppDimens.radiusLarge)),
        color: Colors.white,
      ),
      child: Column(
        children: [
          SizedBox(width: double.infinity, height: AppDimens.spaceMedium,),
          // => fail: error msg
          (!isNullEmpty(controller.notePayment)
              ? Container(
                  margin: EdgeInsets.only(top: 10, left: 20, right: 20),
                  child: Text(
                    controller.notePayment!,
                    style: TextStyle(
                        color: AppColors.redText3,
                        fontSize: AppDimens.textSizeMedium,
                        fontFamily: AppFonts.robotoRegular,
                        height: 1.3
                    ),
                    textAlign: TextAlign.center,
                  ),
                )
              : SizedBox.shrink()),
          // SizedBox(height: AppDimens.spaceMedium,),
          // => btn re-pay
          // CommonButton(
          //   minWidth: Get.width/2,
          //   onPressed: controller.onPressNewTransaction,
          //   title: AppStrings.titleTaoLaiGiaoDich.tr,
          //   color: AppColors.primary,
          //   textColor: AppColors.white,
          //   elevation: 0,
          //   height: 50,
          // )
        ],
      ),
    );
  }

  buildTopScreen(BuildContext context) {
    return Container(
      width: Get.width,
      color: AppColors.white,
      padding: EdgeInsets.only(top: AppDimens.spaceLarge32),
      // color: controller.bgTopColor,
      child: Stack(
        children: [
          Container(
            width: Get.width,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                SizedBox(height: AppDimens.spaceMedium,),
                // => animation result
                Obx(() => Container(
                      height: 80,
                      child: controller.showAnimation.value
                          ? Lottie.asset(
                              controller.iconLottieName.value,
                              fit: BoxFit.contain,
                              repeat: false,
                            )
                          : SizedBox.shrink(),
                    )),
                SizedBox(height: AppDimens.spaceXSmall6,),
                // => text result
                Text(
                  controller.titlePaymentText,
                  style: TextStyle(color: controller.statusColor,
                      fontSize: AppDimens.textSizeLarge24,
                      fontFamily: AppFonts.robotoBold),
                )
              ],
            ),
          ),
          GestureDetector(
              onTap: () => controller.onPressNewTransaction(),
              child: Image.asset(AppImages.ic_home_menu, alignment: Alignment.centerLeft,)),
        ]
      ),
    );
  }

  _buildViewSkipSignature() {
    // controller.isTransSkipSignature.value = true;
    return controller.isTransSkipSignature.value
        ? DottedBorder(
            color: AppColors.primary,
            strokeWidth: 1,
            radius: Radius.circular(10),
            borderType: BorderType.RRect,
            dashPattern: [3, 3],
            child: ClipRRect(
              borderRadius: BorderRadius.all(Radius.circular(4)),
              child: Container(
                decoration: BoxDecoration(boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.2),
                    blurRadius: 2,
                  ),
                ]),
                padding: EdgeInsets.all(15),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Image.asset(AppImages.ic_notify),
                    SizedBox(width: 5,),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(AppStrings.getString(AppStrings.tv_trans_no_signature)!.toUpperCase(), style: style_S14_W600_BlackColor,),
                          SizedBox(height: 5,),
                          Text(
                              AppStrings.getString(AppStrings.tv_no_signature) ?? '',
                              style: style_S14_W400_BlackColor
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ),
          )
        : SizedBox.shrink();

    // return controller.isTransSkipSignature.value
    //     ? Text(
    //         AppStrings.warningMacqSkipSignature.tr,
    //         style: TextStyle(
    //             color: AppColors.red, fontSize: AppDimens.textSizeMedium),
    //       )
    //     : SizedBox.shrink();
  }
}

