import 'package:cashiermodule/constants/style.dart';
import 'package:flutter/widgets.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';

class NormalInputAmountKeyboard extends StatelessWidget {
  final Function? onPressKey;
  final Function? onLongPressKey;

  const NormalInputAmountKeyboard({Key? key, this.onPressKey, this.onLongPressKey}) : super(key: key);

  Widget buildSingleChar(String char, bool top, bool bottom, bool left, bool right) {
    return Expanded(
      flex: 1,
      child: TouchableWidget(
        onLongPressed: () => onLongPressKey!(char),
        onPressed: () => onPressKey!(char),
        borderRadiusEffect: BorderRadius.all(Radius.circular(0)),
        decoration: BoxDecoration(
            border: Border(
                top: BorderSide(width: top ? 0.5 : 0.0, color: AppColors.grayBorder),
                bottom: BorderSide(width: bottom ? 0.5 : 0.0, color: AppColors.grayBorder),
                left: BorderSide(width: left ? 0.5 : 0.0, color: AppColors.grayBorder),
                right: BorderSide(width: right ? 0.5 : 0.0, color: AppColors.grayBorder))),
        child: char != 'd'
            ? Text(
                char,
                style: TextStyle(
                  fontSize: 26,
                  color: AppColors.blackText,
                  fontWeight: FontWeight.w500,
                  fontFamily: kFontFamilyBeVietnamPro
                ),
              )
            : Image.asset(
                AppImages.icDeleteChar,
                width: 35,
                height: 22,
              ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: <Widget>[
        Expanded(
          flex: 1,
          child: Row(
            children: <Widget>[
              buildSingleChar('1', true, false, false, true),
              buildSingleChar('2', true, false, false, true),
              buildSingleChar('3', true, false, false, true),
              buildSingleChar('d', true, false, false, false),
            ],
          ),
        ),
        Expanded(
          flex: 1,
          child: Row(
            children: <Widget>[
              buildSingleChar('4', true, false, false, true),
              buildSingleChar('5', true, false, false, true),
              buildSingleChar('6', true, false, false, true),
              buildSingleChar('000', true, false, false, false),
            ],
          ),
        ),
        Expanded(
          flex: 1,
          child: Row(
            children: <Widget>[
              buildSingleChar('7', true, true, false, true),
              buildSingleChar('8', true, true, false, true),
              buildSingleChar('9', true, true, false, true),
              buildSingleChar('0', true, true, false, false),
            ],
          ),
        ),
      ],
    );
  }
}
