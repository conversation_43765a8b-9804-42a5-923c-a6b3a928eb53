import 'package:cashiermodule/constants/style.dart';
import 'package:flutter/widgets.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';
import 'package:mposxs/app/ui/widget/base_bottom_sheet.dart';
import 'package:mposxs/app/ui/widget/common_button.dart';
import 'package:mposxs/app/ui/widget/common_text_field.dart';
import 'package:mposxs/app/util/app_validation.dart';

class BottomSheetDes extends StatefulWidget {
  final Function? onPressClose;
  final Function? onPressConfirm;
  final String? value;

  const BottomSheetDes({Key? key, this.onPressClose, this.onPressConfirm, this.value}) : super(key: key);

  @override
  _BottomSheetDesState createState() => _BottomSheetDesState();
}

class _BottomSheetDesState extends State<BottomSheetDes> {
  TextEditingController _textDesController = new TextEditingController();
  String? _errMessageDes;

  @override
  void initState() {
    _textDesController.text = widget.value ?? '';
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return BaseBottomSheet(
      onPressClose: () {
        if (widget.onPressClose != null) {
          widget.onPressClose!();
        } else {
          Get.back();
        }
      },
        titleAlign: MyAppController.isKozenP12orN4() ? TextAlign.start : TextAlign.center,
      title: MyAppController.isKozenP12orN4() ? AppStrings.getString(AppStrings.tv_add_deception) : AppStrings.getString(AppStrings.labelBillDescription),
      child: MyAppController.isKozenP12orN4() ? buildWidgetInputDesP12() : Container(
        padding: EdgeInsets.symmetric(vertical: 15, horizontal: 15),
        child: Column(
          children: <Widget>[
            CommonTextField(
              fontSize: 20,
              fontFamily: AppFonts.robotoMedium,
              controller: _textDesController,
              hintText: AppStrings.getString(AppStrings.content),
              hintTextFontSize: 16,
              errorText: _errMessageDes,
              autoFocus: true,
              keyboardType: TextInputType.text,
            ),
            SizedBox(
              height: 10,
            ),
            CommonButton(
              onPressed: () {
                if (widget.onPressConfirm != null) {
                  String des = _textDesController.text;
                  if (!isNullEmpty(des) && des.length < 5) {
                    setState(() {
                      _errMessageDes = AppStrings.getString(AppStrings.errorInvalidDescriptionLength);
                    });
                  } else {
                    Get.back();
                    widget.onPressConfirm!(_textDesController.text);
                  }
                }
              },
              color: AppColors.primary,
              textColor: AppColors.white,
              textSize: 18,
              title: AppStrings.getString(AppStrings.buttonConfirm),
              minWidth: MediaQuery.of(context).size.width - 30,
              height: 50,
            ),
          ],
        ),
      ),
    );
  }

  Widget buildWidgetInputDesP12() {
    return Container(
      height: 90,
      padding: EdgeInsets.symmetric(vertical: 5, horizontal: 15),
      child: Row(
        children: <Widget>[
          Expanded(
            flex: 5,
            child: Container(
              height: 65,
              child: CommonTextField(
                fontSize: 20,
                fontFamily: kFontFamilyBeVietnamPro,
                controller: _textDesController,
                hintText: AppStrings.getString(AppStrings.content),
                hintTextFontSize: 16,
                hintTextFontFamily: kFontFamilyBeVietnamPro,
                autoFocus: true,
                keyboardType: TextInputType.text,
                isBorder: true,
                hideUnderBorderLine: true,
                height: 36.0,
                contentPadding: EdgeInsets.symmetric(horizontal: 10, vertical: 5),
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.symmetric(horizontal: 10),
            height: 65,
            width: 80,
            child: CommonButton(
              onPressed: () {
                if (widget.onPressConfirm != null) {
                  String des = _textDesController.text;
                  if (!isNullEmpty(des) && des.length < 5) {
                    Fluttertoast.showToast(
                        msg: AppStrings.getString(AppStrings.errorInvalidDescriptionLength)!,
                        toastLength: Toast.LENGTH_SHORT,
                        gravity: ToastGravity.CENTER,
                        timeInSecForIosWeb: 1,
                        backgroundColor: AppColors.gray1,
                        textColor: AppColors.white,
                        fontSize: AppDimens.textSizeMedium);
                    // setState(() {
                    //   _errMessageDes = AppStrings.getString(AppStrings.errorInvalidDescriptionLength);
                    // });
                  } else {
                    Get.back();
                    widget.onPressConfirm!(_textDesController.text);
                  }
                }
              },
              color: AppColors.redButton,
              textColor: AppColors.white,
              textSize: 20,
              title: AppStrings.getString(AppStrings.ok),
              minWidth: MediaQuery.of(context).size.width - 30,
              height: 50,
              fontFamily: kFontFamilyBeVietnamPro,
            ),
          ),
        ],
      ),
    );
  }
}
