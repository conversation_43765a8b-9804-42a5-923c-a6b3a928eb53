import 'package:cashiermodule/constants/style.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';
import 'package:mposxs/app/ui/widget/common_button.dart';
import 'package:mposxs/app/ui/widget/header_base_infor.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/mpos_constant.dart';

class PaymentFinishP12 extends StatelessWidget {
  final PaymentResult? paymentResult;
  final Function? onPressHome;
  final Function? onPressPrint;
  final Function? onPressNewTrans;
  final Function? onPressQuickDraw;
  final Widget? wgSkipSignature;
  final int? countdown;
  final bool? allowQuickDraw;
  final bool? isPaymentInstallmentErr;

  const PaymentFinishP12(
      {Key? key,
      this.paymentResult,
      this.onPressHome,
      this.onPressPrint,
      this.onPressNewTrans,
      this.onPressQuickDraw,
      this.wgSkipSignature,
      this.countdown,
      this.allowQuickDraw,
      this.isPaymentInstallmentErr,})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      //==> background success screen
      (paymentResult!.statePayment == MposConstant.PAYMENT_STATE_SUCCESS
          ? Image.asset(
              AppImages.p12_ic_bg_result,
              height: 480,
              width: 480,
              fit: BoxFit.fill,
            )
          : SizedBox.shrink()),
      Container(
        padding: EdgeInsets.only(
            top: AppDimens.spaceLarge32,
            left: AppDimens.spaceLarge24,
            right: AppDimens.spaceLarge24),
        child: Column(
          children: [
            //==> icon + text result
            buildIconTextResult(),
            //==> info result trans
            Expanded(child: buildInfoResultTrans()),
            SizedBox(
              height: AppDimens.spaceXSmall6,
            ),
            //==> btn: home + new trans
            buildBottomBtn(),
            SizedBox(
              height: AppDimens.spaceLarge24,
            ),
            (paymentResult!.statePayment == MposConstant.PAYMENT_STATE_SUCCESS) ? SizedBox.shrink() : HeaderBaseInfor()
          ],
        ),
      )
    ]);
  }

  Row buildIconTextResult() {
    return Row(
      children: [
        Image.asset(
            (paymentResult!.statePayment == MposConstant.PAYMENT_STATE_SUCCESS)
                ? AppImages.p12_ic_success
                : AppImages.p12_ic_fail),
        SizedBox(
          width: AppDimens.spaceLarge,
        ),
        Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            paymentResult!.statePayment == MposConstant.PAYMENT_STATE_SUCCESS
                ? Text(AppStrings.getString(AppStrings.paymentStateSuccess) ?? '',
                    style: TextStyle(
                        color: AppColors.green2,
                        fontWeight: FontWeight.w500,
                        fontSize: AppDimens.textSizeLarge24,
                        fontFamily: kFontFamilyBeVietnamPro))
                : Text((isPaymentInstallmentErr == true) ? AppStrings.getString(AppStrings.errorInstallmentTitle)! : AppStrings.getString(AppStrings.paymentStateFail)!,
                    style: TextStyle(
                        color: AppColors.red,
                        fontWeight: FontWeight.w500,
                        fontSize: AppDimens.textSizeLarge24,
                        fontFamily: AppFonts.robotoBold)),
            SizedBox(
              height: 5,
            ),
            Row(
              children: [
                Text('${AppUtils.formatCurrency(paymentResult!.amountPayment)}',
                    style: style_S30_W600_BlackColor),
                Padding(
                  padding: const EdgeInsets.only(bottom: 15, left: 5),
                  child: Text(
                    'đ',
                    style: TextStyle(
                        fontSize: 20,
                        color: AppColors.blackText,
                        decoration: TextDecoration.underline),
                  ),
                ),
              ],
            ),
          ],
        ),
      ],
    );
  }

  buildInfoResultTrans() {
    return (paymentResult?.statePayment == MposConstant.PAYMENT_STATE_SUCCESS)
        ? buildSuccessInfor()
        : Container(
            padding: EdgeInsets.only(top: AppDimens.spaceMedium),
            child: Text((isPaymentInstallmentErr == true) ? AppStrings.getString(AppStrings.errorInstallmentNotSupportCard)!.replaceFirst('%1s', paymentResult?.pan ?? '') : paymentResult?.notePayment ?? '',
                style: TextStyle(
                    color: AppColors.red,
                    fontSize: AppDimens.textSizeLarge,
                    fontFamily: AppFonts.robotoRegular)),
          );
  }

  buildBottomBtn() {
    return Row(
      children: [
        (allowQuickDraw == true) ? SizedBox() : IconButton(
            onPressed: onPressHome as void Function()?,
            icon: Image.asset(
              AppImages.p12_ic_home,
              alignment: Alignment.center,
              width: AppDimens.icon60,
              height: AppDimens.icon60,
            )),
        (allowQuickDraw == true) ? SizedBox.shrink() : SizedBox(width: AppDimens.spaceXSmall8),

        MyAppController.isSupportPrinter() ? IconButton(
            onPressed: onPressPrint as void Function()?,
            icon: Image.asset(
              AppImages.ic_print,
              width: AppDimens.icon60,
              height: AppDimens.icon60,
              fit: BoxFit.contain,
            )) : SizedBox.shrink(),

        (MyAppController.isSupportPrinter()) ? SizedBox(width: AppDimens.spaceSmall) : SizedBox.shrink(),
        Expanded(
          child: CommonButton(
            minWidth: Get.width - AppDimens.spaceLarge32,
            onPressed: onPressNewTrans as void Function()?,
            title: AppStrings.getString(AppStrings.titleTaoGiaoDichKhac)! +
                " (" +
                countdown.toString() +
                ")",
            color: AppColors.bgButton,
            textColor: AppColors.white,
            elevation: 0,
            height: 50,
            borderCircular: 20,
          ),
        ),
        (allowQuickDraw == true) ? SizedBox(width: AppDimens.spaceXSmall10,) : SizedBox.shrink(),
        (allowQuickDraw == true) ? Expanded(
          child: CommonButton(
            padding: EdgeInsets.symmetric(horizontal: 15),
            minWidth: 20,
            onPressed: onPressQuickDraw as void Function()?,
            title: AppStrings.getString(AppStrings.labelFastWithdraw)!,
            color: AppColors.warning,
            textColor: AppColors.white,
            textSize: AppDimens.textSizeMedium,
            elevation: 0,
            height: 50,
            borderCircular: 20,
          ),
        ) : SizedBox.shrink()
      ],
    );
  }

  Widget buildSuccessInfor() {
    return Container(
      padding: EdgeInsets.only(top: AppDimens.spaceXSmall10),
      width: double.infinity,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: AppDimens.spaceXSmall,),
          widgetItemInfor('Khách hàng:', paymentResult?.holderName ?? ''),
          SizedBox(height: 10,),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Flexible(
                flex: 1,
                  child: widgetItemInfor('Thời gian:', paymentResult?.transactionTime ?? '')),
              Flexible(
                flex: 1,
                  child: widgetItemInfor('Mã giao dịch:', paymentResult?.transId ?? '')),
            ],
          ),
          SizedBox(
            height: AppDimens.spaceXSmall10,
          ),
          wgSkipSignature!
        ],
      ),
    );
  }
}

Widget widgetItemInfor(String title, String body) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(title, style: TextStyle(
        color: AppColors.greyText,
        fontFamily: kFontFamilyBeVietnamPro,
        fontSize: AppDimens.textSizeMedium
      ),),
      Text(body, style: TextStyle(
          color: AppColors.blackText,
          fontFamily: kFontFamilyBeVietnamPro,
          fontSize: AppDimens.textSizeMedium
      ),)
    ],
  );
}

class PaymentResult {
  int? statePayment;
  String? holderName;
  String? pan;
  int? amountPayment;
  String? transId;
  String? transactionTime;
  String? notePayment;

  PaymentResult({this.statePayment, this.holderName, this.pan,
    this.amountPayment, this.transactionTime, this.transId, this.notePayment});
}
