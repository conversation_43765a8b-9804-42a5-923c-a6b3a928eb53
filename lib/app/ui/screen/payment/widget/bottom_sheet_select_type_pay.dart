import 'package:cashiermodule/constants/style.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/data/model/type_payment.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';
import 'package:mposxs/app/ui/widget/base_bottom_sheet.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';

class SelectTypePay extends StatelessWidget {
  final Function? onSelect;
  final Function? onSelectInstallment;
  final Function? onPressSetting;
  final List<TypePayment>? listTypePayment;
  final bool? isTypeInstallment;

  const SelectTypePay({Key? key, this.listTypePayment, this.onSelect, this.onSelectInstallment, this.onPressSetting, this.isTypeInstallment}) : super(key: key);

  Widget _buildItemTypePayment(TypePayment item){
    return TouchableWidget(
      width: 60,
      height: AppDimens.heightButton,
      padding: EdgeInsets.only(left: 10, right: 10),
      decoration: BoxDecoration(
        color: item.bgColor,
        borderRadius: BorderRadius.circular(AppDimens.radiusSmall),
        border: Border.all(color: AppColors.dash, width: 0.5)
      ),
      child: (isTypeInstallment == true)
          ? Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          (item.logo != null) ? ((item.logo!.contains('http')) ? Image.network(item.logo ?? '', width: 25, height: 20, fit: BoxFit.cover,) : Image.asset(item.logo ?? '', width: 25, height: 20,)) : SizedBox.shrink(),
          (item.logo != null) ? SizedBox(width: 10,) : SizedBox.shrink(),
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('${item.title!}', style: item.textStyle ?? TextStyle(
                fontSize: AppDimens.textSizeLarge20,
                fontWeight: FontWeight.w500,
                fontFamily: kFontFamilyBeVietnamPro,
                color: AppColors.white,
              )),
              _buildSub_name(item.sub_name, item.sub_nameEn),
            ],
          )
        ],
      )
          : Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('${AppStrings.getString(item.title!)}', style: item.textStyle ?? TextStyle(
            fontSize: AppDimens.textSizeLarge20,
            fontWeight: FontWeight.w500,
            fontFamily: kFontFamilyBeVietnamPro,
            color: AppColors.white,
          ), textAlign: TextAlign.center,),
          _buildSub_name(item.sub_name, item.sub_nameEn),
        ],
      ),
      onPressed: (){
        (isTypeInstallment == true) ? onSelectInstallment!(item.typePay, item.codeBNPLItem, item.amountMin) : onSelect!(item.typePay);
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return BaseBottomSheet(
      onPressClose: () {
        Get.back();
      },
      title: (isTypeInstallment == true) ? AppStrings.getString('Trả góp') : AppStrings.getString(AppStrings.selectTypePay) ?? '',
      titleAlign: TextAlign.center,
      leftWidget: (onPressSetting != null) ? TouchableWidget(
        child: Image.asset(AppImages.ic_setting_blue, width: AppDimens.icon60,),
        onPressed: onPressSetting,
        padding: EdgeInsets.only(left: 10),
      ) : SizedBox.shrink(),
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.only(bottom: 10),
            height: (MyAppController.isKozenP12orN4() && listTypePayment!.length > 8) ? MediaQuery.of(context).size.height : null,
            child: GridView.count(
              scrollDirection: Axis.vertical,
              crossAxisCount: 2,
              padding: EdgeInsets.only(top: AppDimens.spaceXSmall, left: AppDimens.spaceXSmall, right: AppDimens.spaceXSmall),
              mainAxisSpacing: AppDimens.spaceXSmall,
              crossAxisSpacing: AppDimens.spaceXSmall,
              childAspectRatio: MyAppController.isKozenP12orN4() ? 3.4 : 2.7,
              shrinkWrap: true,
              // physics: NeverScrollableScrollPhysics(),
              children: (listTypePayment!.map((e) => _buildItemTypePayment(e)).toList()),
            ),
          ),
          // Container(
          //   padding: EdgeInsets.symmetric(vertical: AppDimens.spaceXSmall10),
          //   child: TouchableWidget(
          //     onPressed: onPressSetting,
          //     child: Row(
          //       mainAxisAlignment: MainAxisAlignment.center,
          //       children: [
          //         Image.asset(AppImages.ic_setting_blue, width: AppDimens.icon20,),
          //         SizedBox(width: AppDimens.spaceXSmall8,),
          //         Text(
          //           AppStrings.getString(AppStrings.settingSelectTypePay) ?? '',
          //           style: TextStyle(color: AppColors.primary),
          //         )
          //       ],
          //     ),
          //   ),
          // )
        ],
      ),
    );
  }

  Widget _buildSub_name(String? subName, String? subNameEn) {
    return Get.locale.toString() == 'vi_VN'
        ? ((subName == null || subName.isEmpty)
            ? SizedBox.shrink()
            : Text(
                '${subName}',
                style: TextStyle(
                  fontSize: AppDimens.textSizeXSmall,
                  fontWeight: FontWeight.w400,
                  fontFamily: kFontFamilyBeVietnamPro,
                  color: (isTypeInstallment == true) ? AppColors.black : AppColors.white,
                ),
              ))
        : (subNameEn == null || subNameEn.isEmpty)
            ? SizedBox.shrink()
            : Text(
                '${subNameEn}',
                style: TextStyle(
                  fontSize: AppDimens.textSizeXSmall,
                  fontWeight: FontWeight.w400,
                  fontFamily: kFontFamilyBeVietnamPro,
                  color: (isTypeInstallment == true) ? AppColors.black : AppColors.white,
                ),
              );
  }
}
