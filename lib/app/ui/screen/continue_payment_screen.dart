import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/controller/continue_payment_controller.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_common_style.dart';
import 'package:mposxs/app/ui/widget/common_header.dart';
import 'package:mposxs/app/ui/widget/common_screen.dart';
import 'package:mposxs/app/ui/widget/header_button.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';

class ContinuePaymentScreen extends GetView<ContinuePaymentController> {
  Widget _buildRowItem(String label, String value) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 5),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: <Widget>[
          Text(
            '$label:',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.greyTextContent,
            ),
          ),
          Text(
            value,
            style: TextStyle(
              fontSize: 16,
              color: AppColors.darkGrayText,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    controller.context = context;
    MyAppController _appController = Get.find<MyAppController>();
    return WillPopScope(
      onWillPop: () async {
        return false;
      },
      child: CommonScreen(
        header: CommonHeader(
          title: AppStrings.getString(AppStrings.titleDetailTransaction) ?? '',
          leftWidget: _appController.userInfo!.isMacqFlow == 1
              ? HeaderButton(
                  icon: AppImages.icBack,
                  iconWidth: 18,
                  iconHeight: 18,
                  onPressed: () => Get.back(),
                  iconColor: AppColors.blackText,
                )
              : SizedBox.shrink(),
          rightWidget: SizedBox.shrink(),
        ),
        child: Container(
          child: Column(
            children: <Widget>[
              Expanded(
                flex: 1,
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(0),
                  child: Column(
                    children: <Widget>[
                      Container(
                        margin: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
                        child: Text(
                          AppStrings.getString(AppStrings.messagePaymentNotSuccess) ?? '',
                          textAlign: TextAlign.center,
                          style: TextStyle(
                            fontSize: 16,
                            color: AppColors.warning,
                          ),
                        ),
                      ),
                      Container(
                        margin: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
                        padding: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
                        decoration: bodyDecoration(),
                        child: Column(
                          children: <Widget>[
                            Obx(() => _buildRowItem(AppStrings.getString(AppStrings.labelAmount) ?? '', controller.amount.value)),
                            Obx(() => _buildRowItem(AppStrings.getString(AppStrings.labelCardNumber) ?? '', controller.cardNumber.value)),
                            Obx(() =>
                                _buildRowItem(AppStrings.getString(AppStrings.labelCardHolderName) ?? '', controller.cardholderName.value)),
                            Obx(() =>
                                _buildRowItem(AppStrings.getString(AppStrings.labelTransactionTime) ?? '', controller.transactionDate.value)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Container(
                padding:
                    EdgeInsets.only(top: 15, left: 15, right: 15, bottom: 15 + MediaQuery.of(context).padding.bottom),
                color: AppColors.transparent,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    TouchableWidget(
                        height: 50,
                        decoration: BoxDecoration(color: AppColors.blue, borderRadius: BorderRadius.circular(6)),
                        onPressed: controller.onPressConfirm,
                        child: Text(
                          AppStrings.getString(AppStrings.continueText) ?? '',
                          style: buttonTextStyle(),
                        )),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
