import 'package:flutter/widgets.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/widget/base_bottom_sheet.dart';
import 'package:mposxs/app/ui/widget/common_button.dart';
import 'package:mposxs/app/ui/widget/common_text_field.dart';
import 'package:mposxs/app/util/app_validation.dart';

class BottomSheetConfirmPassword extends StatefulWidget {
  final Function? onPressConfirm;
  final bool? hideCloseButton;
  final bool? isHorizonBtnTextField;

  const BottomSheetConfirmPassword({Key? key, this.onPressConfirm, this.hideCloseButton, this.isHorizonBtnTextField = false})
      : super(key: key);

  @override
  _BottomSheetConfirmPasswordState createState() => _BottomSheetConfirmPasswordState();
}

class _BottomSheetConfirmPasswordState extends State<BottomSheetConfirmPassword> {
  TextEditingController _textPasswordController = TextEditingController();
  String? _errMessageConfirmPassword;

  _onPressCloseConfirm() {
    Navigator.pop(context);
  }

  _onPressConfirmPassword() {
    if (isNullEmpty(_textPasswordController.text)) {
      setState(() {
        _errMessageConfirmPassword = AppStrings.getString(AppStrings.labelEmptyPassword);
      });
      return;
    }
    if (widget.onPressConfirm != null) {
      widget.onPressConfirm!(_textPasswordController.text);
    }
    _onPressCloseConfirm();
  }

  @override
  Widget build(BuildContext context) {
    return BaseBottomSheet(
        onPressClose: _onPressCloseConfirm,
        title: AppStrings.getString(AppStrings.titleConfirmPassword),
        hideCloseButton: widget.hideCloseButton,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 15, horizontal: 15),
          child: Column(
            children: <Widget>[
              //==> note warning
              Container(
                // margin: EdgeInsets.only(bottom: 15),
                padding: EdgeInsets.symmetric(
                  vertical: 10,
                  horizontal: 15,
                ),
                decoration: BoxDecoration(
                    color: AppColors.orangeDark.withOpacity(0.11),
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      width: 1,
                      color: AppColors.orangeDark.withOpacity(0.17),
                    )),
                child: Text(
                  AppStrings.getString(AppStrings.noteConfirmPassword)!,
                  style: TextStyle(
                    color: AppColors.orangeDark,
                    fontSize: 13,
                  ),
                ),
              ),
              widget.isHorizonBtnTextField!?_buildHorizontalComponent():_buildVerticalComponent()
            ],
          ),
        ));
  }

  _buildVerticalComponent() {
    return Column(
      children: [
        //==> input password
        CommonTextField(
          fontSize: 20,
          fontFamily: AppFonts.robotoMedium,
          controller: _textPasswordController,
          hintText: AppStrings.getString(AppStrings.labelInputPassword),
          hintTextFontSize: 16,
          obscureText: true,
          errorText: _errMessageConfirmPassword,
          keyboardType: TextInputType.number,
          autoFocus: true,
        ),
        SizedBox(
          height: 10,
        ),
        //==> btn confirm
        CommonButton(
          onPressed: _onPressConfirmPassword,
          color: AppColors.primary,
          textColor: AppColors.white,
          textSize: 18,
          title: AppStrings.getString(AppStrings.buttonConfirm),
          minWidth: MediaQuery.of(context).size.width - 30,
          elevation: 0,
          height: 50,
          fontFamily: AppFonts.robotoMedium,
        )
      ],
    );
  }

  _buildHorizontalComponent() {
    return Row(
      children: [
        //==> input password
        Expanded(
          flex: 1,
          child: CommonTextField(
            fontSize: 20,
            fontFamily: AppFonts.robotoMedium,
            controller: _textPasswordController,
            hintText: AppStrings.getString(AppStrings.labelInputPassword),
            hintTextFontSize: 16,
            obscureText: true,
            errorText: _errMessageConfirmPassword,
            keyboardType: TextInputType.number,
            autoFocus: true,

          ),
        ),
        SizedBox(
          width: 10,
        ),
        //==> btn confirm
        Expanded(
          flex: 1,
          child: CommonButton(
            onPressed: _onPressConfirmPassword,
            color: AppColors.primary,
            textColor: AppColors.white,
            textSize: 18,
            title: AppStrings.getString(AppStrings.buttonConfirm),
            minWidth: MediaQuery.of(context).size.width - 30,
            elevation: 0,
            height: 50,
            fontFamily: AppFonts.robotoMedium,
          ),
        )
      ],
    );
  }
}
