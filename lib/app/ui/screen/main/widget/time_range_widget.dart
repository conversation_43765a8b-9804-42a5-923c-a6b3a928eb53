import 'package:flutter/widgets.dart';
import 'package:flutter_datetime_picker/flutter_datetime_picker.dart';
import 'package:intl/intl.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/widget/common_button.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';
import 'package:mposxs/app/util/app_validation.dart';

import '../../../widget/base_bottom_sheet.dart';

class TimeRangeWidget extends StatefulWidget {
  final Function? onPressConfirm;
  final DateTime? currentStartTime;
  final DateTime? currentEndTime;
  final Duration? maxRange;
  final String? errorRange;

  const TimeRangeWidget(
      {Key? key, this.onPressConfirm, this.currentStartTime, this.currentEndTime, this.maxRange, this.errorRange})
      : super(key: key);

  @override
  _TimeRangeWidgetState createState() => _TimeRangeWidgetState();
}

class _TimeRangeWidgetState extends State<TimeRangeWidget> {
  DateTime? _summaryTimeStart = DateTime(
    DateTime.now().year,
    DateTime.now().month,
    DateTime.now().day,
  );
  DateTime? _summaryTimeEnd = DateTime.now();
  String? _errDate = '';

  @override
  void initState() {
    super.initState();
    if (widget.currentStartTime != null) {
      _summaryTimeStart = widget.currentStartTime;
    }
    if (widget.currentEndTime != null) {
      _summaryTimeEnd = widget.currentEndTime;
    }
  }

  _onPressTimeStart() {
    DatePicker.showDateTimePicker(
      context,
      showTitleActions: true,
      onConfirm: (date) {
        setState(() {
          _summaryTimeStart = date;
          _errDate = '';
        });
      },
      currentTime: _summaryTimeStart,
      locale: LocaleType.vi,
      maxTime: DateTime.now(),
      minTime: DateTime(
        DateTime.now().year,
        DateTime.now().month,
        DateTime.now().day - 7,
      ),
    );
  }

  _onPressTimeEnd() {
    DatePicker.showDateTimePicker(
      context,
      showTitleActions: true,
      onConfirm: (date) {
        setState(() {
          _summaryTimeEnd = date;
          _errDate = '';
        });
      },
      currentTime: _summaryTimeEnd,
      locale: LocaleType.vi,
      maxTime: DateTime(
        DateTime.now().year,
        DateTime.now().month,
        DateTime.now().day+2
      ),
      minTime: DateTime(
        DateTime.now().year,
        DateTime.now().month,
        DateTime.now().day - 7,
      ),
    );
  }

  _onPressClose() {
    Navigator.of(context).pop();
  }

  _onPressConfirm() {
    if (_summaryTimeStart!.compareTo(_summaryTimeEnd!) >= 0) {
      setState(() {
        _errDate = AppStrings.getString(AppStrings.errorTimeRange);
      });
      return;
    }
    if (widget.maxRange != null) {
      Duration currentDuration = _summaryTimeEnd!.difference(_summaryTimeStart!);
      if (currentDuration.inDays > widget.maxRange!.inDays) {
        setState(() {
          _errDate = widget.errorRange ?? AppStrings.getString(AppStrings.errorTimeRangeInvalid);
        });
        return;
      }
    }
    Navigator.of(context).pop();
    if (widget.onPressConfirm != null) {
      widget.onPressConfirm!(_summaryTimeStart, _summaryTimeEnd);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BaseBottomSheet(
        onPressClose: _onPressClose,
        title: AppStrings.getString(AppStrings.buttonSummary),
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 15, horizontal: 15),
          child: Column(
            children: <Widget>[
              Row(
                children: [
                  Expanded(
                    flex: 1,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppStrings.getString(AppStrings.timeStart)!,
                          style: TextStyle(
                            fontSize: 13,
                            color: AppColors.blackText,
                          ),
                        ),
                        TouchableWidget(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          onPressed: _onPressTimeStart,
                          decoration: BoxDecoration(
                              border: Border(
                                  bottom: BorderSide(
                            color: AppColors.gray3,
                            width: 1,
                          ))),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                flex: 1,
                                child: Text(
                                  DateFormat('HH:mm - dd/MM/yyyy', 'vi_VN').format(_summaryTimeStart!),
                                  style: TextStyle(
                                    fontSize: 15,
                                    color: AppColors.blackText,
                                    fontFamily: AppFonts.robotoBold,
                                  ),
                                ),
                              ),
                              Image.asset(
                                AppImages.ic_calendar,
                                width: 15,
                                height: 15,
                                fit: BoxFit.contain,
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 20,
                  ),
                  Expanded(
                    flex: 1,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          AppStrings.getString(AppStrings.timeEnd)!,
                          style: TextStyle(
                            fontSize: 13,
                            color: AppColors.blackText,
                          ),
                        ),
                        TouchableWidget(
                          padding: const EdgeInsets.symmetric(vertical: 4),
                          onPressed: _onPressTimeEnd,
                          decoration: BoxDecoration(
                              border: Border(
                                  bottom: BorderSide(
                            color: AppColors.gray3,
                            width: 1,
                          ))),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                flex: 1,
                                child: Text(
                                  DateFormat('HH:mm - dd/MM/yyyy', 'vi_VN').format(_summaryTimeEnd!),
                                  style: TextStyle(
                                    fontSize: 15,
                                    color: AppColors.blackText,
                                    fontFamily: AppFonts.robotoBold,
                                  ),
                                ),
                              ),
                              Image.asset(
                                AppImages.ic_calendar,
                                width: 15,
                                height: 15,
                                fit: BoxFit.contain,
                              )
                            ],
                          ),
                        )
                      ],
                    ),
                  )
                ],
              ),
              isNullEmpty(_errDate)
                  ? SizedBox.shrink()
                  : Column(
                      children: [
                        SizedBox(
                          height: 10,
                        ),
                        Text(
                          _errDate!,
                          style: TextStyle(
                            fontSize: 13,
                            fontFamily: AppFonts.robotoItalic,
                            color: AppColors.redText,
                          ),
                        ),
                      ],
                    ),
              SizedBox(
                height: 16,
              ),
              CommonButton(
                onPressed: _onPressConfirm,
                color: AppColors.primary,
                textColor: AppColors.white,
                textSize: 18,
                title: AppStrings.getString(AppStrings.ok)!.toUpperCase(),
                minWidth: MediaQuery.of(context).size.width - 30,
                elevation: 0,
                height: 50,
                fontFamily: AppFonts.robotoMedium,
              )
            ],
          ),
        ));
  }
}
