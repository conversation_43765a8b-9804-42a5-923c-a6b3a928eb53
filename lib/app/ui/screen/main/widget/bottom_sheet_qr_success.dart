import 'package:flutter/material.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';
import 'package:mposxs/app/ui/widget/base_bottom_sheet.dart';
import 'package:mposxs/app/ui/widget/common_button.dart';
import 'package:mposxs/app/util/app_utils.dart';

class BottomSheetQrSuccess extends StatefulWidget {

  final String? amount;
  final String? date;
  final Function? onPressed;

  const BottomSheetQrSuccess({Key? key, this.amount, this.date, this.onPressed}) : super(key: key);

  @override
  _BottomSheetQrSuccessState createState() => _BottomSheetQrSuccessState();
}

class _BottomSheetQrSuccessState extends State<BottomSheetQrSuccess> {
  @override
  Widget build(BuildContext context) {
    return BaseBottomSheet(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
        ),
        padding: EdgeInsets.fromLTRB(30, 30, 30, 15),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Image.asset(
                    AppImages.p12_ic_success,
                    alignment: Alignment.center,
                    width: AppDimens.icon60,
                    height: AppDimens.icon60,
                    fit: BoxFit.contain,
                  ),
                  SizedBox(width: 10,),
                  Text(
                    AppStrings.getString(AppStrings.tvPaySuccess)!.toUpperCase(),
                    style: TextStyle(
                      fontSize: AppDimens.spaceLarge24,
                      color: AppColors.success,
                      fontFamily: AppFonts.robotoMedium,
                    ),
                  )
                ],
              ),
            ),

            SizedBox(height: 20,),

            FittedBox(
              fit: BoxFit.scaleDown,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  Text(
                    widget.amount != null && widget.amount!.length > 0
                        ? AppUtils.formatCurrency(int.parse(widget.amount!))
                        : '',
                    style: TextStyle(
                        fontSize: AppDimens.spaceLarge48,
                        color: AppColors.blackText,
                        fontFamily: AppFonts.robotoMedium),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(bottom: 20, left: 2),
                    child: Text(
                      'đ',
                      style: TextStyle(
                          fontSize: 20,
                          color: AppColors.blackText.withOpacity(0.81),
                          decoration: TextDecoration.underline),
                    ),
                  ),
                ],
              ),
            ),

            SizedBox(height: 10,),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                widget.date!.isNotEmpty ? Container(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(AppStrings.getString(AppStrings.labelTransactionTimeColon) ?? '', style: TextStyle(
                        fontSize: AppDimens.textSizeMedium,
                        color: AppColors.blackDarkText,
                      ),),

                      Text(widget.date!, style: TextStyle(
                        fontSize: AppDimens.textSizeMedium,
                        color: AppColors.blackDarkText,
                      ),)
                    ],
                  ),
                ) : SizedBox.shrink(),

              Container(
                width: 110,
                height: 50,
                child: CommonButton(
                    onPressed: () async {
                      // delay for show effect press to button
                      await Future.delayed(Duration(milliseconds: 100));
                      widget.onPressed!();
                    },
                    textColor: AppColors.white,
                    padding:
                        EdgeInsets.symmetric(vertical: 10, horizontal: 30),
                    color: AppColors.primary,
                    textSize: 18,
                    title: AppStrings.getString(AppStrings.ok) ?? '',
                    borderCircular: 25,
              ),
              )
              ],
            )
          ],
        ),
      ),
      hideCloseButton: true,
      isCloseHeader: true,
    );
  }

}
