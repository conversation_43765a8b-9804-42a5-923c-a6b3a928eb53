import 'dart:convert';
import 'dart:developer' as dev;

import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/data/provider/local_storage.dart';
import 'package:mposxs/app/data/provider/native_bridge.dart';
import 'package:mposxs/app/route/app_pages.dart';
import 'package:mposxs/app/ui/widget/loading_widget.dart';

class RootScreen extends GetView {
  final Widget? child;
  final MyAppController _appController = Get.find<MyAppController>();

  RootScreen(this.child);

  @override
  Widget build(BuildContext context) {

    initFireBaseMessasing(context);

    return Obx(() {
      return Stack(
        children: [
          child!,
          _appController.loading.value ? LoadingWidget(message: _appController.messageLoading.value,) : SizedBox(),
        ],
      );
    });
  }

  void initFireBaseMessasing(BuildContext context) async {
    DateTime now = DateTime.now();
    String lastTimePaid = await LocalStorage().getLastTimeCacheQrPaid();
    if (lastTimePaid.isNotEmpty) {
      DateTime dateTime = DateFormat('dd/MM/yy').parse(lastTimePaid);
      if (now.day == dateTime.day && now.month == dateTime.month && now.year == dateTime.year) {
        _appController.listOrderPaid = await LocalStorage().getListOrderPaid();
      }
    }

    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      dev.log('RootScreen a message in the foreground!');
      String currentRoute = Get.currentRoute;
      if ((currentRoute == AppRoute.push_payment) || (currentRoute == AppRoute.listOrderPage)) {
        dev.log('RootScreen data: ${message.data}');
        String? orderCode = message.data['orderCode'];
        if (_appController.listOrderPaid.contains(orderCode)) {
          return;
        }

        // _appController.listOrderPaid.add(orderCode!);
        String jsonData = jsonEncode(message.data);
        _putNotiSuccessToCashier(jsonData);
      }else {
        dev.log('current screen in mpos lite, not put data cashier');
      }
    });
  }

  _putNotiSuccessToCashier(String messageData) async {
    dev.log('_putNotiSuccessToCashier: $messageData');
    NativeResponseModel nativeResponseModel = await NativeBridge.getInstance().putSuccessQrNotification(messageData);
    _appController.hideLoading();
    if (nativeResponseModel.isSuccess) {
      dev.log('_putNotiSuccessToCashier: success');
    }
  }
}
