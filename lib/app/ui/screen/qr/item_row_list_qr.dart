import 'package:cashiermodule/constants/style.dart';
import 'package:cashiermodule/widget_custom/touchable_widget.dart';
import 'package:flutter/material.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/util/constants.dart';

class ItemRowListQr extends StatelessWidget{
  final String? nameQr;
  final String? icon;
  final String? detail;
  final TypeQr? typeQr;
  final Color? color;
  final Function? onPressed;

  const ItemRowListQr(
      {Key? key,
        this.nameQr,
        this.icon,
        this.detail,
        this.typeQr,
        this.onPressed, this.color,})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 10),
      decoration: BoxDecoration(
        color: color ?? AppColors.bgButton,
          borderRadius: BorderRadius.all(Radius.circular(10)),
      ),
      child: TouchableWidget(
        onPressed: () async{
          // delay for show effect press to button
          await Future.delayed(Duration(milliseconds: 100));
          onPressed!();
        },
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(nameQr!, style: style_S16_W600_WhiteColor, textAlign: TextAlign.start,),
            // Expanded(child: (detail != null) ? Text( detail!, style: style_S14_W400_WhiteColor,) : SizedBox.shrink(),)
          ],
        )
      ),
    );
  }
}