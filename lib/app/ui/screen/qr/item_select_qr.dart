import 'package:flutter/material.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';
import 'package:mposxs/app/ui/widget/common_button.dart';

class ItemSelectQr extends StatelessWidget{
  final String? icon;
  final String? name;
  final bool isSelect;
  final double borderCircular;
  final Function? onPressed;

  const ItemSelectQr(
      {Key? key,
        this.isSelect = false,
        this.icon,
        this.name,
        this.borderCircular = 0,
        this.onPressed,
      })
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: isSelect ? AppColors.primary : AppColors.grayBorder,
          width: 1.0,
        ),
        color: Colors.white,
        borderRadius: BorderRadius.circular(borderCircular??25),
      ),
      child: CommonButton(
        padding: EdgeInsets.symmetric(vertical: 5, horizontal: 10),
        onPressed: () async{
          // delay for show effect press to button
          await Future.delayed(Duration(milliseconds: 100));
          onPressed!();
        },
        color: AppColors.transparent,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            (icon?.isNotEmpty == true)
                ? Image.network(
              icon??'',
              alignment: Alignment.center,
              width: AppDimens.icon60,
              height: AppDimens.icon60,
              fit: BoxFit.contain,
            ) : SizedBox.shrink(),
            SizedBox(height: 5,),
            Text(name!, style: TextStyle(fontFamily: AppFonts.robotoBold, fontSize: AppDimens.textSizeXSmall),),
          ],
        ),
      ),
    );
  }
}