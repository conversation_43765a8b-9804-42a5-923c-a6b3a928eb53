import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/qr_list_source_controller.dart';
import 'package:mposxs/app/data/model/mp_data_login_model.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/widget/common_header.dart';
import 'package:mposxs/app/ui/widget/common_image_network.dart';
import 'package:mposxs/app/ui/widget/common_screen.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';

class QrListSourceScreen extends GetView<QrListSourceController> {
  Widget _buildQrGroup(MPBankQR bankQr) {
    String description = Get.locale.toString() == 'vi_VN' ? bankQr.description! : bankQr.descriptionEn!;
    bool isExpanded = controller.listBankQrExpanded.contains(bankQr);
    double heightChildExpanded = ((bankQr.qrChildren!.length ?? 0) / 4).ceil().toDouble() * 80 +
        (description.length > 100
            ? 45
            : description.length > 50
                ? 30
                : 15) +
        5;
    return Container(
      margin: EdgeInsets.only(bottom: 15),
      decoration: BoxDecoration(color: AppColors.white, borderRadius: BorderRadius.circular(8), boxShadow: [
        BoxShadow(
          blurRadius: 10,
          color: AppColors.black.withOpacity(0.1),
        ),
      ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TouchableWidget(
            onPressed: () => controller.onPressQrGroup(bankQr),
            padding: EdgeInsets.all(15),
            child: Row(
              children: [
                CommonImageNetwork(url: bankQr.logo, width: 40, height: 40),
                SizedBox(width: 15),
                Expanded(
                  child: Text(
                    Get.locale.toString() == 'vi_VN' ? bankQr.longName! : bankQr.longNameEn!,
                    style: TextStyle(
                      fontSize: 16,
                      color: AppColors.blackText,
                      fontFamily: AppFonts.robotoMedium,
                    ),
                  ),
                ),
                Icon(
                  isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                  size: 24,
                  color: AppColors.blackText,
                ),
              ],
            ),
          ),
          AnimatedContainer(
            duration: Duration(milliseconds: 500),
            height: isExpanded ? heightChildExpanded : 0,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 15),
                    child: Text(
                      description,
                      maxLines: 3,
                      overflow: TextOverflow.ellipsis,
                      style: TextStyle(
                        fontSize: 12,
                        color: AppColors.blackBlueText,
                        fontFamily: AppFonts.robotoRegular,
                      ),
                    ),
                  ),
                  Wrap(
                    children: (bankQr?.qrChildren ?? []).map((item) => _buildItemSource(item, bankQr)).toList(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemSource(MPQrChild item, MPBankQR bankQr) {
    return TouchableWidget(
      width: (Get.width - 30) / 4,
      height: 80,
      onPressed: () => controller.onPressItemSource(item, bankQr),
      child: Column(
        children: <Widget>[
          Expanded(
            flex: 3,
            child: CommonImageNetwork(
              url: item?.logoChild,
              errorWidget: Image.asset(AppImages.icLauncher),
            ),
          ),
          SizedBox(height: 4),
          Expanded(
            flex: 1,
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                item.shortNameChild!,
                style: TextStyle(
                  fontSize: 13,
                  color: AppColors.blackBlueText,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    controller.context = context;
    return CommonScreen(
      mainBackgroundColor: AppColors.mainBackground,
      header: CommonHeader(
        title: AppStrings.getString(AppStrings.qrListSourceTitle) ?? '',
      ),
      child: SafeArea(
        top: false,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Expanded(
              child: SingleChildScrollView(
                child: ListView.builder(
                  shrinkWrap: true,
                  physics: new NeverScrollableScrollPhysics(),
                  padding: EdgeInsets.all(15),
                  itemCount: controller.listBankQr.length,
                  itemBuilder: (BuildContext context, int index) {
                    return Obx(() => _buildQrGroup(controller.listBankQr[index]));
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
