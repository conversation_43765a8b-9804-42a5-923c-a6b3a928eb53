import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/controller/qr_enter_info_controller.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_common_style.dart';
import 'package:mposxs/app/ui/widget/common_header.dart';
import 'package:mposxs/app/ui/widget/common_image_network.dart';
import 'package:mposxs/app/ui/widget/common_screen.dart';
import 'package:mposxs/app/ui/widget/common_text_field.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';
import 'package:mposxs/app/util/app_utils.dart';

class QrEnterInfoScreen extends GetView<QrEnterInfoController> {
  final MyAppController _appController = Get.find<MyAppController>();

  @override
  Widget build(BuildContext context) {
    controller.context = context;
    bool enableChangeFee = controller.checkFeeChangeQr == 1;
    return CommonScreen(
      header: CommonHeader(
        title: AppStrings.getString(AppStrings.titleQRPaymentEnterInfo) ?? '',
        titleColor: AppColors.white,
        headerBackgroundHeight: 160,
        headerDecoration: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(22),
            bottomRight: Radius.circular(22),
          ),
        ),
        headerExtend: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              AppStrings.getString(AppStrings.labelBillAmount) ?? '',
              style: TextStyle(fontSize: 14, color: AppColors.white.withOpacity(0.5)),
            ),
            FittedBox(
              fit: BoxFit.contain,
              child: Padding(
                padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: <Widget>[
                    Text(
                      AppUtils.formatCurrency(_appController.paymentInfoSession?.amount ?? 0),
                      style: TextStyle(fontSize: 34, color: AppColors.white),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(bottom: 20, left: 2),
                      child: Text(
                        'đ',
                        style: TextStyle(
                            fontSize: 16,
                            color: AppColors.white.withOpacity(0.66),
                            decoration: TextDecoration.underline),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
      child: Column(
        children: <Widget>[
          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Container(
                    margin: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                    padding: EdgeInsets.all(10),
                    decoration: bodyDecoration(),
                    child: Row(
                      children: <Widget>[
                        Container(
                          width: 50,
                          height: 30,
                          padding: EdgeInsets.all(5),
                          margin: EdgeInsets.only(right: 10),
                          decoration: BoxDecoration(
                            color: AppColors.grayBackground,
                            borderRadius: BorderRadius.all(Radius.circular(6)),
                          ),
                          child: CommonImageNetwork(
                            url: controller.qrSource?.logoChild ?? '',
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Text(
                            (controller.qrSource?.shortNameChild ?? '') +
                                ' - ' +
                                (controller.qrSource?.longNameChild ?? ''),
                            style: TextStyle(
                              fontSize: 14,
                              color: AppColors.darkGrayText,
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                  Container(
                    margin: EdgeInsets.only(left: 15, right: 15, bottom: 20),
                    padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                    decoration: bodyDecoration(),
                    child: Column(
                      children: <Widget>[
                        Obx(() => CommonTextField(
                              controller: controller.textDesController,
                              fontSize: 18,
                              fontFamily: AppFonts.robotoMedium,
                              hintText: AppStrings.getString(AppStrings.descriptionPlaceHolder) ?? '',
                                  // (_appController.userInfo?.requiredInsDescription == true ? '*' : ''),
                              hintTextFontSize: 15,
                              maxLength: 255,
                              errorText: controller.errorTextDes.value,
                              onChanged: controller.onChangedDes,
                            )),
                        Obx(() => CommonTextField(
                              controller: controller.textPhoneController,
                              fontSize: 18,
                              fontFamily: AppFonts.robotoMedium,
                              hintText: AppStrings.getString(AppStrings.labelCustomerPhone) ?? '',
                              hintTextFontSize: 15,
                              maxLength: 12,
                              keyboardType: TextInputType.phone,
                              errorText: controller.errorTextPhone.value,
                              onChanged: controller.onChangedPhone,
                            )),
                        Obx(() => CommonTextField(
                              controller: controller.textEmailController,
                              fontSize: 18,
                              fontFamily: AppFonts.robotoMedium,
                              hintText: AppStrings.getString(AppStrings.labelCustomerEmail) ?? '',
                              hintTextFontSize: 15,
                              maxLength: 250,
                              keyboardType: TextInputType.emailAddress,
                              errorText: controller.errorTextEmail.value,
                              onChanged: controller.onChangedEmail,
                            )),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          controller.keyboardVisible.value
              ? SizedBox.shrink()
              : Container(
                  padding:
                      EdgeInsets.only(top: 15, left: 15, right: 15, bottom: 15 + MediaQuery.of(context).padding.bottom),
                  decoration: bottomDecoration(),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      TouchableWidget(
                        onPressed: enableChangeFee ? controller.onPressChangeChecked : null,
                        padding: EdgeInsets.zero,
                        child: Row(
                          children: <Widget>[
                            Obx(() => Container(
                                  width: 20,
                                  height: 20,
                                  padding: EdgeInsets.all(3),
                                  decoration: BoxDecoration(
                                      color: !controller.checkedFee.value
                                          ? AppColors.transparent
                                          : enableChangeFee
                                              ? AppColors.primary
                                              : AppColors.greyText,
                                      border: Border.all(
                                          color: enableChangeFee ? AppColors.primary : AppColors.greyText, width: 2),
                                      borderRadius: BorderRadius.circular(2)),
                                  child: Image.asset(
                                    AppImages.icCheck,
                                  ),
                                )),
                            SizedBox(width: 10),
                            Text(
                              '${AppStrings.getString(AppStrings.addFeeQR)}:',
                              style: TextStyle(
                                fontSize: 14,
                                color: AppColors.greyText,
                              ),
                            ),
                            Spacer(),
                            Obx(() => Text(
                                  AppUtils.formatCurrency(controller.checkedFee.value ? controller.fee : 0) + 'đ',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontFamily: AppFonts.robotoMedium,
                                    color: AppColors.blackText,
                                  ),
                                )),
                          ],
                        ),
                      ),
                      SizedBox(height: 10),
                      Row(
                        children: <Widget>[
                          Text(
                            '${AppStrings.getString(AppStrings.account)}:',
                            style: TextStyle(
                              fontSize: 14,
                              color: AppColors.greyTextContent,
                            ),
                          ),
                          Spacer(),
                          Obx(() => Text(
                                AppUtils.formatCurrency(
                                        controller.amount + (controller.checkedFee.value ? controller.fee : 0)) +
                                    'đ',
                                style: TextStyle(
                                  fontSize: 20,
                                  fontFamily: AppFonts.robotoMedium,
                                  color: AppColors.warning,
                                ),
                              )),
                        ],
                      ),
                      SizedBox(height: 10),
                      TouchableWidget(
                        height: 50,
                        onPressed: controller.onPressContinue,
                        decoration: BoxDecoration(
                          color: AppColors.primary,
                          borderRadius: BorderRadius.all(Radius.circular(6)),
                        ),
                        child: Text(
                          AppStrings.getString(AppStrings.continueText) ?? '',
                          style: buttonTextStyle(),
                        ),
                      ),
                    ],
                  ),
                ),
        ],
      ),
    );
  }
}
