import 'dart:math' as math;

import 'package:cashiermodule/constants/style.dart';
import 'package:cashiermodule/widget_custom/touchable_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/QrCodeP12Controller.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';
import 'package:mposxs/app/ui/widget/amount_widget.dart';
import 'package:mposxs/app/ui/widget/common_button.dart';
import 'package:mposxs/app/util/constants.dart';
import 'package:qr_flutter/qr_flutter.dart';

class QrCodeP12Screen extends GetView<QrCodeP12Controller> {
  Future<bool> onWillPop() {
    controller.onPressClose();
    return Future.value(false);
  }

  @override
  Widget build(BuildContext context) {
    if (MyAppController.isKozenP12orN4()) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersiveSticky);
    }
    controller.context = context;
    return WillPopScope(
        onWillPop: onWillPop,
        child: Scaffold(
          backgroundColor: Colors.white,
          body: Stack(
            children: [
              // button back
              Container(
                margin: EdgeInsets.symmetric(vertical: AppDimens.spaceLarge24),
                child: CommonButton(
                  padding: EdgeInsets.zero,
                  color: AppColors.transparent,
                  child: Image.asset(
                    AppImages.ic_cancel_qr,
                    alignment: Alignment.centerLeft,
                    width: MediaQuery.of(context).size.width * 0.15,
                    height: MediaQuery.of(context).size.width * 0.12,
                    fit: BoxFit.contain,
                    // height: AppDimens.icon60,
                  ),
                  onPressed: () => controller.onPressClose(),
                ),
              ),

              buildMainQr(context),
            ],
          ),
        ));
  }

  Widget buildMainQr(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: MediaQuery.of(context).size.height,
      margin: EdgeInsets.only(top: 20),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                buildTitle(),
                SizedBox(
                  height: 5,
                ),
                //view qr
                Container(
                  width: (controller.typePay.value == TypePay.QR_PAY) ? MediaQuery.of(context).size.width*3/5 : MediaQuery.of(context).size.width*3/6,
                    padding: EdgeInsets.symmetric(vertical: 5),
                    child: Center(child: buildQRCode())),
                SizedBox(
                  height: 5,
                ),
                Container(
                    child: AmountWidget(
                      amount: controller.currentAmount ?? '',
                      textColor: AppColors.orange1,
                      fontSize: AppDimens.textSizeLarge30,
                    )),
                // Text(
                //   '${(controller.typePay.value == TypePay.QR_PAY) ? AppStrings.getString(AppStrings.msg_oneCustomerOnePayment) : AppStrings.getString(AppStrings.tv_guide_scan_qr_link)}',
                //   style: TextStyle(
                //       fontFamily: AppFonts.robotoItalic,
                //       color: AppColors.greyTextContent),
                // ),
              ],
            ),
          ),

          Obx(() => (controller.typePay.value == TypePay.QR_PAY) ? buildSelectQrWidget() : buildStatusLinkCardWidget(),),
        ],
      ),
    );
  }

  Widget buildQRCode() {
    return Obx(
      () => Stack(
        children: [
          controller.qrCodeData.value.isNotEmpty
              ? Container(
                  decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(10),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.black.withOpacity(0.2),
                          spreadRadius: 1,
                          blurRadius: 2,
                        ),
                      ]),
                  // padding: EdgeInsets.all(10),
                  child: QrImageView(
                    padding: EdgeInsets.all(10),
                    backgroundColor: Colors.transparent,
                    data: controller.qrCodeData.value,
                    version: QrVersions.auto,
                    // embeddedImage: (controller.qrType.value !=
                    //         MposConstant.name_group_qr_VAQR)
                    //     ? NetworkImage('')
                    //     : AssetImage(AppImages.ic_vietqr_mini) as ImageProvider,
                    // embeddedImageStyle:
                    //     QrEmbeddedImageStyle(size: Size(40, 40)),
                  ),
                )
              : LayoutBuilder(builder: (context, constraints) {
                  final containerSize = constraints.biggest.shortestSide;
                  return Container(
                    width: containerSize,
                    height: containerSize,
                    decoration: BoxDecoration(
                        color: AppColors.lightGrayBackground,
                        borderRadius: BorderRadius.circular(10)),
                    padding: EdgeInsets.all(10),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        AnimatedBuilder(
                          animation: controller.animationController,
                          builder: (_, child) {
                            return Transform.rotate(
                              angle: controller.animationController.value *
                                  2 *
                                  math.pi,
                              child: child,
                            );
                          },
                          child: Image.asset(AppImages.ic_loading),
                        ),
                        SizedBox(
                          height: 5,
                        ),
                        Text(
                          AppStrings.getString(AppStrings.tv_creating_qr) ?? '',
                          style: TextStyle(
                              fontFamily: AppFonts.robotoItalic,
                              color: AppColors.lightGreyText),
                        )
                      ],
                    ),
                  );
                }),
          controller.isResetQr.value
              ? LayoutBuilder(builder: (context, constraints) {
                  final containerSize = constraints.biggest.shortestSide;
                  return Container(
                    height: containerSize,
                    width: containerSize,
                    decoration: BoxDecoration(
                      color: AppColors.bg_transparent_qr,
                      borderRadius: new BorderRadius.all(Radius.circular(10)),
                    ),
                    padding: EdgeInsets.all(10),
                    child: Center(
                      child: CommonButton(
                        minWidth: 110,
                        borderCircular: 25,
                        color: AppColors.white,
                        onPressed: () {
                          controller.onPressResetQr();
                        },
                        child: Text(
                          AppStrings.getString(AppStrings.tv_create_qr_again) ?? '',
                          style: TextStyle(color: AppColors.primary),
                        ),
                      ),
                    ),
                  );
                })
              : SizedBox.shrink(),
        ],
      ),
    );
  }

  Widget buildTitle() {
    return (controller.typePay.value != TypePay.QR_PAY)
        ? Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Image.asset(AppImages.ic_qr_link),
              SizedBox(height: 3,),
              Text(
                'Chỉ hỗ trợ thẻ do ngân hàng trong nước phát hành',
                style: style_S14_W400_OrangeColor,
              ),
            ],
          )
        : controller.logoQr.value.isEmpty
            ? Image.asset(AppImages.ic_logo_vietqr)
            : Image.network(
                controller.logoQr.value,
                fit: BoxFit.fill,
                width: 100,
                height: 30,
              );
  }

  Widget buildSelectQrWidget() {
    //view select qr
    return Container(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            AppStrings.getString(AppStrings.msg_oneCustomerOnePayment) ?? '',
            style: TextStyle(
                fontFamily: AppFonts.robotoItalic,
                color: AppColors.greyTextContent),
          ),
          // SizedBox(height: 10,),
          //view select qr
          Obx(
            () => Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(vertical: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  //title qr-pay
                  Flexible(
                    flex: 1,
                    child: controller.isPaymentSocket.value
                        ? Container(
                      color: AppColors.primary,
                    )
                        : Container(
                      margin: EdgeInsets.only(right: 10),
                          child: CommonButton(
                            padding: EdgeInsets.only(right: 20),
                              shape: RoundedRectangleBorder(
                                borderRadius: new BorderRadius.only(
                                    topRight: Radius.circular(25),
                                    bottomRight: Radius.circular(25)),
                              ),
                              color: AppColors.bg_type_qr,
                              minWidth: 70,
                              height: MediaQuery.of(controller.context).size.height * 0.11,
                              onPressed: () => controller.onPressShowMoreQr(),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Image.asset(AppImages.ic_arrow_right,
                                      width: AppDimens.icon28,
                                      height: AppDimens.icon28),
                                  Text(
                                      AppStrings.getString(
                                              AppStrings.labelQRPay) ??
                                          '',
                                      style: TextStyle(
                                          fontFamily: AppFonts.robotoBold,
                                          color: AppColors.white))
                                ],
                              ),
                            ),
                        ),
                  ),
                  Flexible(
                    flex: 2,
                    child: Obx(
                      () => (controller.countCheckStatus.value >= 4)
                          ? Container(
                        margin: EdgeInsets.symmetric(horizontal: 10),
                            child: TouchableWidget(
                              height: MediaQuery.of(controller.context).size.height * 0.11,
                              // height: AppDimens.heightButton,
                                padding: EdgeInsets.symmetric(horizontal: 20),
                                decoration: BoxDecoration(
                                    color: AppColors.success,
                                    borderRadius: BorderRadius.circular(
                                        AppDimens.radiusMedium)),
                                child: Text(
                                  controller.isEnableCheckStatus.value
                                      ? AppStrings.getString(AppStrings.check) ??
                                          ''
                                      : AppStrings.getString(
                                              AppStrings.loading) ??
                                          '',
                                  style: TextStyle(
                                    fontSize: AppDimens.textSizeMedium,
                                    fontWeight: FontWeight.w500,
                                    fontFamily: kFontFamilyBeVietnamPro,
                                    color: AppColors.white,
                                  ),
                                ),
                                // margin: EdgeInsets.only(left: 5, right: 5),
                                onPressed: () {
                                  controller.onPressCheckStatusTrans();
                                },
                              ),
                          )
                          : SizedBox.shrink(),
                    ),
                  ),
                  Flexible(
                    flex: 1,
                    child: controller.liveTimeQr.value.isNotEmpty
                        ? Container(
                      padding: EdgeInsets.only(right: 10),
                      alignment: Alignment.centerRight,
                          child: Text(
                              controller.liveTimeQr.value,
                              style: TextStyle(
                                  fontFamily: AppFonts.robotoBold,
                                  color: AppColors.redText,
                                  fontSize: AppDimens.textSizeLarge),
                            ),
                        )
                        : SizedBox(
                            width: 100,
                          ),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  Widget buildStatusLinkCardWidget() {
    return Container(
      height: 100,
      child: Column(
        children: [
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 20),
              child: Text(
                '${AppStrings.getString(AppStrings.tv_guide_scan_qr_link)}',
                textAlign: TextAlign.center,
                style: style_S14_W400_BlackColor,
              ),
            ),
          ),

          (controller.qrCodeData.value.isNotEmpty) ? buildCheckLinkCardWidget() : SizedBox.shrink(),
        ],
      ),
    );
  }

  Widget buildCheckLinkCardWidget() {
    return Container(
      padding: EdgeInsets.only(bottom: 5, left: 20, right: 20),
      child: Row(
        children: [
          Expanded(
            child: Container(
              height: 50,
              padding: EdgeInsets.all(10),
              decoration: BoxDecoration(color: AppColors.lightYellow, borderRadius: BorderRadius.circular(10), boxShadow: [
                BoxShadow(
                  color:  AppColors.yellow,
                  spreadRadius: 1
                )
              ]),
              child: Align(
                alignment: AlignmentDirectional.centerStart,
                child: Text(
                  controller.qrCodeData.value, style: TextStyle(
                  fontSize: 12,
                  fontFamily: kFontFamilyBeVietnamPro,
                ), maxLines: 2,
                ),
              ),
            ),
          ),
          SizedBox(
            width: 10,
          ),
          TouchableWidget(
            height: AppDimens.heightButton,
            padding: EdgeInsets.symmetric(horizontal: 30),
            decoration: BoxDecoration(
                color: AppColors.success,
                borderRadius:
                BorderRadius.circular(AppDimens.radiusMedium)),
            child: Text(
              controller.isEnableCheckStatus.value
                  ? AppStrings.getString(AppStrings.check) ?? ''
                  : AppStrings.getString(AppStrings.loading) ?? '',
              style: TextStyle(
                fontSize: AppDimens.textSizeMedium,
                fontWeight: FontWeight.w500,
                fontFamily: kFontFamilyBeVietnamPro,
                color: AppColors.white,
              ),
            ),
            // margin: EdgeInsets.only(left: 5, right: 5),
            onPressed: () {
              controller.onPressCheckStatusTrans();
            },
          )
        ],
      ),
    );
  }
}
