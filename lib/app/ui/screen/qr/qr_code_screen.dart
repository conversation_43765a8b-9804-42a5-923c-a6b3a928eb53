import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/controller/qr_code_controller.dart';
import 'package:mposxs/app/controller/webview_info_controller.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/route/app_pages.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_common_style.dart';
import 'package:mposxs/app/ui/widget/common_header.dart';
import 'package:mposxs/app/ui/widget/common_image_network.dart';
import 'package:mposxs/app/ui/widget/common_screen.dart';
import 'package:mposxs/app/ui/widget/header_button.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../../../../build_constants.dart';

class QrCodeScreen extends GetView<QrCodeController> {
  final MyAppController _appController = Get.find<MyAppController>();

  Future<bool> onWillPop() {
    controller.onPressClose();
    return Future.value(false);
  }

  @override
  Widget build(BuildContext context) {
    controller.context = context;
    return WillPopScope(
      onWillPop: onWillPop,
      child: CommonScreen(
        mainBackgroundColor: AppColors.white,
        header: CommonHeader(
          title: AppStrings.getString(AppStrings.labelScanQR) ?? '',
          leftWidget: HeaderButton(
            icon: AppImages.icClose,
            iconWidth: 18,
            iconHeight: 18,
            onPressed: controller.onPressClose,
            iconColor: AppColors.blackText,
          ),
        ),
        child: Column(
          children: <Widget>[
            Expanded(
              flex: 1,
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.all(15),
                  child: Column(
                    children: <Widget>[
                      Text(
                        Get.locale!.languageCode == 'vi'
                            ? (_appController.paymentInfoSession?.selectedQrSource?.description ?? '')
                            : (_appController.paymentInfoSession?.selectedQrSource?.descriptionEn ?? ''),
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 16,
                          fontFamily: AppFonts.robotoMedium,
                          color: AppColors.darkGrayText,
                        ),
                      ),
                      TouchableWidget(
                        onPressed: () {
                          Get.toNamed(AppRoute.webview_info_screen,
                              arguments: WebViewInfoArguments(
                                  '${BuildConstants.serverAPI}${_appController.paymentInfoSession?.selectedQrSource?.userManualLink}',
                                  null));
                        },
                        padding: EdgeInsets.symmetric(vertical: 5),
                        child: Text(
                          '(${AppStrings.getString(AppStrings.viewHint)})',
                          style: TextStyle(
                            fontSize: 14,
                            fontFamily: AppFonts.robotoItalic,
                            color: AppColors.primary,
                          ),
                        ),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        // crossAxisAlignment: CrossAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Container(
                              height: 30,
                              margin: EdgeInsets.all(5),
                              child: CommonImageNetwork(url: controller.qrGroup?.logo ?? '',)),
                          Container(
                            margin: EdgeInsets.only(bottom: 3),
                            constraints: BoxConstraints(maxWidth: Get.width / 3 * 2),
                            child: Text(
                              Get.locale!.languageCode == 'vi'
                                  ? (controller.qrGroup?.longName ?? '')
                                  : (controller.qrGroup?.longNameEn ?? ''),
                              textAlign: TextAlign.center,
                              style: TextStyle(
                                fontSize: 16,
                                fontFamily: AppFonts.robotoMedium,
                                color: AppColors.blueText,
                              ),
                            ),
                          ),
                        ],
                      ),
                      // Container(
                      //     height: 30,
                      //     margin: EdgeInsets.all(5),
                      //     child: CommonImageNetwork(url: controller.qrGroup?.logo ?? '',)),
                      SizedBox(height: 10,),
                      Container(
                        width: 200,
                        height: 200,
                        margin: EdgeInsets.only(bottom: 10),
                        decoration: BoxDecoration(
                            border: Border.all(color: AppColors.grayBorderQr, width: 1),
                            borderRadius: BorderRadius.all(Radius.circular(4))),
                        child: QrImageView(
                          data: _appController.paymentInfoSession?.qrCode ?? "",
                          version: QrVersions.auto,
                          // embeddedImage:
                          //     NetworkImage(_appController.paymentInfoSession?.selectedQrSource?.logoChild ?? ''),
                          embeddedImageStyle: QrEmbeddedImageStyle(size: Size(60, 60)),
                        ),
                      ),
                      TouchableWidget(
                        onPressed: () {
                          AppUtils.showDialogAlert(
                            context,
                            title: AppStrings.getString(AppStrings.titleNoteQr),
                            descriptionTextAlign: TextAlign.left,
                            description:
                                '• ${AppStrings.getString(AppStrings.qrNote1)}\n\n• ${AppStrings.getString(AppStrings.qrNote2)}\n\n• ${AppStrings.getString(AppStrings.qrNote3)}',
                            text1stButton: AppStrings.getString(AppStrings.close) ?? '',
                          );
                        },
                        padding: EdgeInsets.symmetric(vertical: 5),
                        child: RichText(
                          textAlign: TextAlign.center,
                          text: TextSpan(
                              style: TextStyle(
                                fontSize: 14,
                                color: AppColors.warning,
                                fontFamily: AppFonts.robotoRegular,
                              ),
                              children: [
                                TextSpan(text: '(${AppStrings.getString(AppStrings.onlyFor)} '),
                                TextSpan(
                                    text: AppStrings.getString(AppStrings.oneCustomerOnePayment) ?? '',
                                    style: TextStyle(fontFamily: AppFonts.robotoMedium)),
                                TextSpan(text: ')  '),
                                WidgetSpan(
                                  alignment: PlaceholderAlignment.middle,
                                  child: Container(
                                    width: 20,
                                    height: 20,
                                    alignment: Alignment.center,
                                    decoration: BoxDecoration(
                                      color: AppColors.warning.withOpacity(0.5),
                                      borderRadius: BorderRadius.circular(10),
                                    ),
                                    child: Text(
                                      '?',
                                      style: TextStyle(
                                        fontSize: 16,
                                        color: AppColors.orange,
                                        fontFamily: AppFonts.robotoBold,
                                      ),
                                    ),
                                  ),
                                )
                              ]),
                        ),
                      ),
                      Text(
                        AppUtils.formatCurrency(_appController.paymentInfoSession!.amount) + 'đ',
                        style: TextStyle(
                          fontSize: 26,
                          fontFamily: AppFonts.robotoMedium,
                          color: AppColors.primary,
                        ),
                      ),
                      SizedBox(height: 10),
                      Obx(() => Row(
                            children: <Widget>[
                              (controller.enableCheck.value
                                  ? Expanded(
                                      flex: 1,
                                      child: TouchableWidget(
                                          height: 50,
                                          decoration: BoxDecoration(
                                              color: AppColors.primary, borderRadius: BorderRadius.circular(6)),
                                          onPressed: () => controller.onPressCheck(null),
                                          child: Text(
                                            AppStrings.getString(AppStrings.check) ?? '',
                                            style: buttonTextStyle(),
                                          )),
                                    )
                                  : Container()),
                              (controller.enableCheck.value ? Container(width: 10) : SizedBox.shrink()),
                              Expanded(
                                flex: 1,
                                child: TouchableWidget(
                                    height: 50,
                                    decoration: BoxDecoration(
                                        color: AppColors.grayBackgroundStt, borderRadius: BorderRadius.circular(6)),
                                    onPressed: controller.onPressOther,
                                    child: Text(
                                      AppStrings.getString(AppStrings.otherTransaction) ?? '',
                                      style: buttonTextStyle(color: AppColors.primary),
                                    )),
                              ),
                            ],
                          )),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
