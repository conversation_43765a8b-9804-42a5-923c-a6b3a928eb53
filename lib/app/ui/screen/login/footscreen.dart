import 'package:cashiermodule/constants/style.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/widget/common_button.dart';
import 'package:mposxs/app/util/mpos_constant.dart';

import '../../../controller/app_controller.dart';

class FootScreenLogin extends StatelessWidget {
  const FootScreenLogin({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final MyAppController _appController = Get.find<MyAppController>();

    return Container(
      alignment: Alignment.bottomCenter,
      child:
      Column(
        children: [
          Text(
            '${AppStrings.getString(AppStrings.tv_app_version)}: ${_appController.buildNumber}',
            style: style_S14_W400_BlackColor,
          ),
          Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                Container(
                  child: Text(
                    '${AppStrings.getString(AppStrings.hotline)}',
                    style: TextStyle(
                      color: AppColors.black,
                      fontSize: 14,
                    ),
                  ),
                ),
                CommonButton(
                  // onPressed: AppUtils.openCallPhoneSupport,
                  onPressed: () {},
                  padding: EdgeInsets.only(left: 2, right: 2, top: 1),
                  color: AppColors.transparent,
                  textColor: AppColors.primary,
                  textSize: 14,
                  title: '${MposConstant.SUPPORT_PHONE}',
                ),
                Container(
                  child: Text(
                    ' - ${AppStrings.getString(AppStrings.email)}:',
                    style: TextStyle(
                      color: AppColors.black,
                      fontSize: 14,
                    ),
                  ),
                ),
                CommonButton(
                  // onPressed: AppUtils.launchMailHotroMpos,
                  onPressed: () {},
                  padding: EdgeInsets.only(left: 2, right: 2, top: 1),
                  color: AppColors.transparent,
                  textColor: AppColors.primary,
                  textSize: 14,
                  title: '${MposConstant.SUPPORT_EMAIL}',
                ),
              ]),
        ],
      ),
    );
  }
}
