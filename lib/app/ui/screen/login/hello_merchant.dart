import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/data/provider/local_storage.dart';
import 'package:mposxs/app/data/provider/native_bridge.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/route/app_pages.dart';
import 'package:mposxs/app/ui/screen/login/footscreen.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';
import 'package:mposxs/app/ui/widget/common_button.dart';
import 'package:mposxs/app/ui/widget/common_screen.dart';

class HelloMerchant extends StatelessWidget {
  final MyAppController _appController = Get.find<MyAppController>();

  HelloMerchant({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async{
        goBackToLoginScreen();
        return false;
      },
      child: CommonScreen(
        mainBackgroundColor: AppColors.white,
        child: Container(
          padding: EdgeInsets.only(
            top: MediaQuery.of(context).padding.top,
            bottom: MediaQuery.of(context).padding.bottom,
            left: AppDimens.spaceLarge24,
            right: AppDimens.spaceLarge24,
          ),
          child: Column(
            children: [
              Expanded(
                flex: 1,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(
                      height: AppDimens.spaceLarge24,
                    ),
                    GestureDetector(
                      child: Image.asset(AppImages.ic_arrow_left),
                      onTap: () {
                        // _appController.userInfo = null;
                        goBackToLoginScreen();

                      },
                    ),
                    SizedBox(
                      height: AppDimens.spaceLarge24,
                    ),
                    Text(
                      AppStrings.getString(AppStrings.hello) ?? '',
                      style: TextStyle(fontSize: AppDimens.textSizeMedium),
                    ),
                    SizedBox(
                      height: AppDimens.spaceXSmall6,
                    ),
                    // => merchant name
                    Text(
                      _appController.userInfo!.merchantName??'',
                      style: TextStyle(fontSize: AppDimens.textSizeLarge),
                    ),
                    SizedBox(height: AppDimens.spaceXSmall,),
                    //=> merchant address
                    Row(
                      crossAxisAlignment:CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(right: AppDimens.spaceXSmall8),
                          child: Image.asset(AppImages.ic_point_location, width: 15, height: 20,),
                        ),
                        Expanded(
                          child: Text(
                            _appController.userInfo!.businessAddress??'',
                            style: TextStyle(fontSize: AppDimens.textSizeMedium, color: AppColors.gray1),
                          ),
                        ),
                      ],
                    ),
                    SizedBox(
                      height: MyAppController.isKozenP12orN4() ? AppDimens.spaceLarge32 : AppDimens.spaceLarge64,
                    ),
                    Text(AppStrings.getString(AppStrings.thankForUsing) ?? '',
                        style: TextStyle(fontSize: AppDimens.textSizeSmall)),
                    SizedBox(height: AppDimens.spaceLarge24,),
                    CommonButton(
                      onPressed: () {
                        _appController.localStorage.saveData(LocalStorage.KEY_NEED_SAY_HELLO, false);
                        Get.offAndToNamed(AppRoute.main_screen);
                      },
                      title: AppStrings.getString(AppStrings.startPayment) ?? '',
                    ),
                  ],
                ),
              ),
              FootScreenLogin()
            ],
          ),
        ),
      ),
    );
  }

  void goBackToLoginScreen(){
    NativeBridge.getInstance().nativeClearDataAuto();
    LocalStorage().clearAll();
    Get.offAndToNamed(AppRoute.login_screen);
  }
}
