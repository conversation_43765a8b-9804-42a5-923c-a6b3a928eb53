import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/controller/installmert_confirm_controller.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_common_style.dart';
import 'package:mposxs/app/ui/widget/common_header.dart';
import 'package:mposxs/app/ui/widget/common_image_network.dart';
import 'package:mposxs/app/ui/widget/common_screen.dart';
import 'package:mposxs/app/ui/widget/common_text_field.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';
import 'package:mposxs/app/util/app_utils.dart';

class InstallmentConfirmScreen extends GetView<InstallmentConfirmController> {
  final MyAppController _appController = Get.find<MyAppController>();

  @override
  Widget build(BuildContext context) {
    controller.context = context;
    int payPerMonth = ((_appController.installmentInfoSession?.finalAmountPay ?? 0) /
            (int.parse(_appController.installmentInfoSession?.selectedPeriod?.period??'1')))
        .round();
    double feeCard = _appController.installmentInfoSession?.feeCard ?? 0;
    double feeInstallment = _appController.installmentInfoSession?.feeInstallment ?? 0;
    double totalFee = feeCard + feeInstallment;
    return CommonScreen(
      header: CommonHeader(
        title: AppStrings.getString(AppStrings.titlePaymentEnterInfo),
        titleColor: AppColors.white,
        headerBackgroundHeight:  MyAppController.isKozenP12orN4()?110:160,
        headerDecoration: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(22),
            bottomRight: Radius.circular(22),
          ),
        ),
        headerExtend: MyAppController.isKozenP12orN4()?_buildExtendHeaderP12():_buildExtendHeaderNormal(),
      ),
      child: Column(
        children: <Widget>[
          Expanded(
            flex: 1,
            child: SingleChildScrollView(
              child: Column(
                children: <Widget>[
                  //==> info bank, period, amount
                  Container(
                    margin: EdgeInsets.only(left: 15, right: 15, top: 10, bottom: 5),
                    padding: EdgeInsets.all(10),
                    decoration: bodyDecoration(),
                    child: Row(
                      children: <Widget>[
                        Container(
                          width: 74,
                          height: 42,
                          padding: EdgeInsets.all(5),
                          margin: EdgeInsets.only(right: 10),
                          decoration: BoxDecoration(
                            color: AppColors.grayBackground,
                            borderRadius: BorderRadius.all(Radius.circular(6)),
                          ),
                          child: CommonImageNetwork(
                            url: _appController.installmentInfoSession?.selectedBank?.logo ?? '',
                          ),
                        ),
                        Expanded(
                          flex: 1,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: <Widget>[
                              Text(
                                _appController.installmentInfoSession!.selectedCardTypeMap['name'] +
                                    ' - ' +
                                    AppStrings.getString(AppStrings.installmentConfirmMonth)?.replaceFirst(
                                        '%1s', _appController.installmentInfoSession!.selectedPeriod?.period ?? ''),
                                style: TextStyle(
                                  fontSize: 16,
                                  fontFamily: AppFonts.robotoMedium,
                                  color: AppColors.darkGrayText,
                                ),
                              ),
                              Text(
                                AppUtils.formatCurrency(payPerMonth) + 'đ/${AppStrings.getString(AppStrings.month)}',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: AppColors.warning,
                                ),
                              ),
                            ],
                          ),
                        )
                      ],
                    ),
                  ),
                  //==> from user
                  Container(
                    margin: EdgeInsets.only(left: 15, right: 15, bottom: 20),
                    padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                    decoration: bodyDecoration(),
                    child: Column(
                      children: <Widget>[
                        Obx(() => CommonTextField(
                              controller: controller.textDesController,
                              fontSize: 18,
                              fontFamily: AppFonts.robotoMedium,
                              hintText: AppStrings.getString(AppStrings.descriptionPlaceHolder),
                                  // (_appController.userInfo?.configParamRequires?.requiredInsDescription == true ? '*' : ''),
                              hintTextFontSize: 15,
                              maxLength: 255,
                              errorText: controller.errorTextDes.value,
                              onChanged: controller.onChangedDes,
                            )),
                        (_appController.installmentInfoSession?.isPayLink == true
                            ? SizedBox.shrink()
                            : Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: <Widget>[
                                  //==> phone
                                  Obx(() => CommonTextField(
                                        controller: controller.textPhoneController,
                                        fontSize: 18,
                                        fontFamily: AppFonts.robotoMedium,
                                        hintText: '${AppStrings.getString(AppStrings.labelCustomerPhone)}*',
                                        hintTextFontSize: 15,
                                        maxLength: 12,
                                        keyboardType: TextInputType.phone,
                                        errorText: controller.errorTextPhone.value,
                                        onChanged: controller.onChangedPhone,
                                      )),
                                  //==> CMND
                                  Obx(() => CommonTextField(
                                        controller: controller.textIdentityController,
                                        fontSize: 18,
                                        fontFamily: AppFonts.robotoMedium,
                                        hintText: AppStrings.getString(AppStrings.identityPlaceHolder)! +
                                            (_appController.installmentInfoSession!.selectedBank?.idCardNumber ==
                                                    'REQUIRED'
                                                ? '*'
                                                : ''),
                                        hintTextFontSize: 15,
                                        maxLength: 20,
                                        errorText: controller.errorTextIdentity.value,
                                        onChanged: controller.onChangedIdentity,
                                      )),
                                  //==> email
                                  Obx(() => CommonTextField(
                                        controller: controller.textEmailController,
                                        fontSize: 18,
                                        fontFamily: AppFonts.robotoMedium,
                                        hintText: AppStrings.getString(AppStrings.labelCustomerEmail),
                                        hintTextFontSize: 15,
                                        maxLength: 250,
                                        keyboardType: TextInputType.emailAddress,
                                        errorText: controller.errorTextEmail.value,
                                        onChanged: controller.onChangedEmail,
                                      )),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(vertical: 5),
                                    child: Text(
                                      '*${AppStrings.getString(AppStrings.requireCustomerInfo)}',
                                      style: TextStyle(
                                        fontSize: 12,
                                        color: AppColors.primary,
                                      ),
                                    ),
                                  ),
                                ],
                              ))
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          Obx(() => (controller.keyboardVisible.value
              ? SizedBox.shrink()
              : (MyAppController.isKozenP12orN4()?_buildBottomWidgetP12(context, totalFee) :
                                _buildBottomWidgetNormal(context, totalFee))
          )),
        ],
      ),
    );
  }

  _buildExtendHeaderNormal() {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          AppStrings.getString(AppStrings.labelBillAmount)!,
          style: TextStyle(fontSize: 14, color: AppColors.white.withOpacity(0.5)),
        ),
        FittedBox(
          fit: BoxFit.contain,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Text(
                  AppUtils.formatCurrency(_appController.installmentInfoSession?.amount ?? 0),
                  style: TextStyle(fontSize: 34, color: AppColors.white),
                ),
                Padding(
                  padding: const EdgeInsets.only(bottom: 20, left: 2),
                  child: Text(
                    'đ',
                    style: TextStyle(
                        fontSize: 16,
                        color: AppColors.white.withOpacity(0.66),
                        decoration: TextDecoration.underline),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  _buildExtendHeaderP12() {
    return Row(children: [
      Expanded(
        flex: 1,
        child: Container(
          alignment: Alignment.centerRight,
          child: Text(
            AppStrings.getString(AppStrings.labelBillAmount)! + ':',
            style: TextStyle(fontSize: 15, color: Colors.white),
          ),
        ),
      ),
      Expanded(
          flex: 1,
          child: FittedBox(
            fit: BoxFit.contain,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  AppUtils.formatCurrency(_appController.installmentInfoSession?.amount ?? 0),
                  style: TextStyle(fontSize: 34, color: AppColors.white),
                ),
                Padding(
                  padding: const EdgeInsets.only(bottom: 20, left: 2),
                  child: Text(
                    'đ',
                    style: TextStyle(
                        fontSize: 16,
                        color: AppColors.white.withOpacity(0.66),
                        decoration: TextDecoration.underline),
                  ),
                ),
              ],
            ),
          ))
    ],);
  }

  _buildBottomWidgetNormal(context, totalFee){
    return Container(
      padding:
      EdgeInsets.only(top: 15, left: 15, right: 15, bottom: 15 + MediaQuery.of(context).padding.bottom),
      decoration: bottomDecoration(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          (totalFee > 0.0
              ? Padding(
            padding: EdgeInsets.only(bottom: 10),
            child: Row(
              children: <Widget>[
                Text(
                  AppStrings.getString(AppStrings.labelTransactionFee)!,
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.greyTextContent,
                  ),
                ),
                Spacer(),
                Text(
                  AppUtils.formatCurrency(totalFee) + 'đ',
                  style: TextStyle(
                    fontSize: 16,
                    fontFamily: AppFonts.robotoMedium,
                    color: AppColors.darkGrayText,
                  ),
                ),
              ],
            ),
          )
              : Container()),
          Row(
            children: <Widget>[
              Text(
                '${AppStrings.getString(AppStrings.totalAmount)}:',
                style: TextStyle(
                  fontSize: 14,
                  color: AppColors.greyTextContent,
                ),
              ),
              Spacer(),
              Text(
                AppUtils.formatCurrency(_appController.installmentInfoSession?.finalAmountPay ?? 0) + 'đ',
                style: TextStyle(
                  fontSize: 20,
                  fontFamily: AppFonts.robotoMedium,
                  color: AppColors.warning,
                ),
              ),
            ],
          ),
          TouchableWidget(
            height: 50,
            onPressed: _appController.installmentInfoSession?.isPayLink == true
                ? controller.onPressCreateLink
                : controller.onPressScanCard,
            margin: EdgeInsets.only(top: 10),
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.all(Radius.circular(6)),
            ),
            child: Text(
              AppStrings.getString(AppStrings.buttonConfirm)!,
              style: buttonTextStyle(),
            ),
          ),
        ],
      ),
    );
  }

  _buildBottomWidgetP12(context, totalFee){
    return Container(
      padding: EdgeInsets.only(top: 10, left: 10, right: 10, bottom: 10),
      decoration: bottomDecoration(),
      child: Row(
        children: [
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                //==> total fee
                (totalFee > 0.0
                    ? Padding(
                  padding: EdgeInsets.only(bottom: 10),
                  child: Row(
                    children: <Widget>[
                      Text(
                        AppStrings.getString(AppStrings.labelTransactionFee)!,
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.greyTextContent,
                        ),
                      ),
                      Spacer(),
                      Text(
                        AppUtils.formatCurrency(totalFee) + 'đ',
                        style: TextStyle(
                          fontSize: 16,
                          fontFamily: AppFonts.robotoMedium,
                          color: AppColors.darkGrayText,
                        ),
                      ),
                    ],
                  ),
                )
                    : SizedBox.shrink()),
                //==> total amount
                Row(
                  children: <Widget>[
                    Text(
                      '${AppStrings.getString(AppStrings.totalAmount)}:',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.greyTextContent,
                      ),
                    ),
                    Spacer(),
                    Text(
                      AppUtils.formatCurrency(_appController.installmentInfoSession?.finalAmountPay ?? 0) + 'đ',
                      style: TextStyle(
                        fontSize: 20,
                        fontFamily: AppFonts.robotoMedium,
                        color: AppColors.warning,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          SizedBox(width: 15,),
          //==> btn confirm
          TouchableWidget(
            height: 60,
            onPressed: _appController.installmentInfoSession?.isPayLink == true
                ? controller.onPressCreateLink
                : controller.onPressScanCard,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.all(Radius.circular(6)),
            ),
            child: Text(
              AppStrings.getString(AppStrings.buttonConfirm) ?? '',
              style: buttonTextStyle(),
            ),
          ),
        ],
      ),
    );
  }
}
