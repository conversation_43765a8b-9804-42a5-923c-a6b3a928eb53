import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/controller/installment_enter_amount_controller.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/screen/payment/widget/normal_input_amount_keyboard.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_common_style.dart';
import 'package:mposxs/app/ui/widget/common_header.dart';
import 'package:mposxs/app/ui/widget/common_screen.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/app_validation.dart';
import 'package:mposxs/app/util/number_to_string.dart';

class InstallmentEnterAmountScreen extends GetView<InstallmentEnterAmountController> {
  @override
  Widget build(BuildContext context) {
    controller.context = context;
    return MyAppController.isKozenP12orN4() ? _buildInitPaymentP12(context) : _buildInitPaymentNormal(context);
  }

  _buildInitPaymentNormal(BuildContext context) {
    return CommonScreen(
      mainBackgroundColor: AppColors.white,
      header: CommonHeader(
        title: AppStrings.getString(AppStrings.installmentPaymentTitle),
      ),
      child: Column(
        children: <Widget>[
          //==> amount
          Container(
            height: 100,
            margin: EdgeInsets.only(left: 5, right: 5, bottom: 5, top: 5),
            child: Obx(() => Column(
              children: <Widget>[
                Expanded(
                  flex: 1,
                  child: Container(
                    width: MediaQuery.of(context).size.width,
                    child: Row(
                      children: [
                        Container(width: 25, height: 25),
                        Expanded(
                          flex: 1,
                          child: FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: <Widget>[
                                Text(
                                  controller.valueInput.value != null && controller.valueInput.value.length > 0
                                      ? AppUtils.formatCurrency(int.parse(controller.valueInput.value))
                                      : '',
                                  style: TextStyle(
                                      fontSize: 46, color: AppColors.primary, fontFamily: AppFonts.robotoMedium),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(bottom: 20, left: 2),
                                  child: Text(
                                    'đ',
                                    style: TextStyle(
                                        fontSize: 20,
                                        color: AppColors.primary.withOpacity(0.81),
                                        decoration: TextDecoration.underline),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        (!isNullEmpty(controller.valueInput.value) && controller.valueInput.value != '0'
                            ? TouchableWidget(
                          width: 35,
                          height: 35,
                          padding: EdgeInsets.all(0),
                          margin: EdgeInsets.only(left: 10),
                          child: Image.asset(
                            AppImages.icClearText,
                            width: 20,
                            height: 20,
                          ),
                          onPressed: controller.onPressClearValue,
                        )
                            : Container(width: 25, height: 25)),
                      ],
                    ),
                  ),
                ),
                SizedBox(
                    height:
                    (!isNullEmpty(controller.valueInput.value) && controller.valueInput.value != '0') ? 5 : 0),
                Center(
                  child: Text(
                    (!isNullEmpty(controller.valueInput.value) && controller.valueInput.value != '0')
                        ? Get.locale!.languageCode != 'vi'
                        ? NumberToString().readNumberEn(int.parse(controller.valueInput.value))
                        : NumberToString().readNumber(int.parse(controller.valueInput.value))
                        : '',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 15, fontFamily: AppFonts.robotoItalic, color: AppColors.blackText),
                  ),
                ),
              ],
            )),
          ),
          //==> suggest amount
          Container(
            height: 56,
            padding: EdgeInsets.only(left: 10, right: 10),
            child: Obx(() => Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: (controller.listSuggest.value ?? [])
                  .map((element) => Expanded(
                  flex: 1,
                  child: TouchableWidget(
                    padding: EdgeInsets.only(left: 5, right: 5),
                    margin: EdgeInsets.only(left: 5, right: 5),
                    height: 34,
                    decoration: BoxDecoration(
                      color: AppColors.black.withOpacity(0.05),
                      borderRadius: BorderRadius.circular(17),
                    ),
                    child: FittedBox(
                      fit: BoxFit.scaleDown,
                      child: Text(
                        AppUtils.formatCurrency(element) + 'đ',
                        style: TextStyle(
                            fontSize: 16, color: AppColors.blackBlueText, fontFamily: AppFonts.robotoMedium),
                      ),
                    ),
                    onPressed: () => controller.onPressSuggest(element),
                  )))
                  .toList(),
            )),
          ),
          //==> amount text
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10),
            decoration: BoxDecoration(
              border: Border(
                top: BorderSide(width: 0.5, color: AppColors.grayBorder),
              ),
            ),
            child: Obx(() => Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Image.asset(AppImages.ic_edit, width: 18, height: 18),
                Expanded(
                  flex: 1,
                  child: TouchableWidget(
                    height: 46,
                    onPressed: controller.onPressDes,
                    child: Align(
                      alignment: Alignment.centerLeft,
                      child: Text(
                        isNullEmpty(controller.textDes.value)
                            ? AppStrings.getString(AppStrings.labelBillDescription)!
                            : controller.textDes.value,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          fontSize: isNullEmpty(controller.textDes.value) ? 14 : 16,
                          fontFamily: isNullEmpty(controller.textDes.value)
                              ? AppFonts.robotoRegular
                              : AppFonts.robotoMedium,
                          color: isNullEmpty(controller.textDes.value) ? AppColors.greyText : AppColors.blackText,
                        ),
                      ),
                    ),
                  ),
                ),
                (!isNullEmpty(controller.textDes.value)
                    ? TouchableWidget(
                  width: 20,
                  height: 20,
                  padding: EdgeInsets.all(0),
                  child: Image.asset(
                    AppImages.icClearText2,
                    width: 15,
                    height: 15,
                  ),
                  onPressed: controller.onPressClearDes,
                )
                    : SizedBox.shrink()),
              ],
            )),
          ),
          //==> keyboard
          Expanded(
            flex: 1,
            child: NormalInputAmountKeyboard(
              onPressKey: controller.onPressChar,
            ),
          ),
          //==> btn swipe card
          Container(
            margin: EdgeInsets.only(bottom: MediaQuery.of(context).padding.bottom + 10),
            padding: EdgeInsets.symmetric(horizontal: 10),
            child: TouchableWidget(
              height: MediaQuery.of(context).size.width / 6,
              onPressed: controller.onPressContinue,
              decoration: BoxDecoration(
                color: AppColors.primary,
                borderRadius: BorderRadius.circular(6),
              ),
              margin: EdgeInsets.only(top: 10, left: 5, right: 5),
              child: Text(
                AppStrings.getString(AppStrings.installmentSwipeCard)!,
                style: buttonTextStyle(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  _buildInitPaymentP12(context) {
    return CommonScreen(
      mainBackgroundColor: AppColors.white,
      header: CommonHeader(
        title: AppStrings.getString(AppStrings.installmentPaymentTitle)!,
      ),
      child: Column(
        children: <Widget>[
          //==> amount
          Container(
            height: 70,
            margin: EdgeInsets.only(left: 5, right: 5),
            child: Obx(() => Column(
              children: <Widget>[
                Expanded(
                  flex: 1,
                  child: Container(
                    width: MediaQuery.of(context).size.width,
                    child: Row(
                      children: [
                        Container(width: 25, height: 25),
                        Expanded(
                          flex: 1,
                          child: FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: <Widget>[
                                Text(
                                  controller.valueInput.value != null && controller.valueInput.value.length > 0
                                      ? AppUtils.formatCurrency(int.parse(controller.valueInput.value))
                                      : '',
                                  style: TextStyle(
                                      fontSize: 46, color: AppColors.primary, fontFamily: AppFonts.robotoMedium),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(bottom: 20, left: 2),
                                  child: Text(
                                    'đ',
                                    style: TextStyle(
                                        fontSize: 20,
                                        color: AppColors.primary.withOpacity(0.81),
                                        decoration: TextDecoration.underline),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        (!isNullEmpty(controller.valueInput.value) && controller.valueInput.value != '0'
                            ? TouchableWidget(
                          width: 35,
                          height: 35,
                          padding: EdgeInsets.all(0),
                          margin: EdgeInsets.only(left: 10),
                          child: Image.asset(
                            AppImages.icClearText,
                            width: 20,
                            height: 20,
                          ),
                          onPressed: controller.onPressClearValue,
                        )
                            : Container(width: 25, height: 25)),
                      ],
                    ),
                  ),
                ),
                SizedBox(
                    height:
                    (!isNullEmpty(controller.valueInput.value) && controller.valueInput.value != '0') ? 5 : 0),
                Center(
                  child: Text(
                    (!isNullEmpty(controller.valueInput.value) && controller.valueInput.value != '0')
                        ? Get.locale!.languageCode != 'vi'
                        ? NumberToString().readNumberEn(int.parse(controller.valueInput.value))
                        : NumberToString().readNumber(int.parse(controller.valueInput.value))
                        : '',
                    textAlign: TextAlign.center,
                    style: TextStyle(fontSize: 15, fontFamily: AppFonts.robotoItalic, color: AppColors.blackText),
                  ),
                ),
              ],
            )),
          ),
          // => amount suggest
          Container(
              height: 56,
              padding: EdgeInsets.only(left: 10, right: 10),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: (controller.listSuggest)
                    .map((element) => Expanded(
                    flex: 1,
                    child: TouchableWidget(
                      padding: EdgeInsets.only(left: 5, right: 5),
                      margin: EdgeInsets.only(left: 5, right: 5),
                      height: 34,
                      decoration: BoxDecoration(
                        color: AppColors.black.withOpacity(0.05),
                        borderRadius: BorderRadius.circular(17),
                      ),
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        child: Text(
                          AppUtils.formatCurrency(element) + 'đ',
                          style: TextStyle(
                              fontSize: 16,
                              color: AppColors.blackBlueText,
                              fontFamily: AppFonts.robotoMedium),
                        ),
                      ),
                      onPressed: () =>
                          controller.onPressSuggest(element),
                    )))
                    .toList(),
              )),
          //==> keyboard + btn pay
          Expanded(
            child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                mainAxisSize: MainAxisSize.min,
                children: [
                  //==> keyboard
                  Expanded(
                    child: NormalInputAmountKeyboard(
                      onPressKey: controller.onPressChar,
                      // onLongPressKey: controller.onLongPressChar,
                    ),
                  ),
                  //type payment
                  Container(
                    // padding: EdgeInsets.fromLTRB(10, 10, 0, 10),
                    alignment: Alignment.center,
                    child: IntrinsicWidth(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.stretch,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: <Widget>[
                          TouchableWidget(
                            height: 70,
                            onPressed: controller.onPressContinue,
                            decoration: BoxDecoration(
                              color: AppColors.primary,
                              borderRadius: BorderRadius.circular(6),
                            ),
                            margin: EdgeInsets.only(left: 5, right: 5),
                            child: Text(
                              AppStrings.getString(AppStrings.installmentSwipeCardButton)!,
                              style: buttonTextStyle(),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
          ),
        ],
      ),
    );
  }
}
