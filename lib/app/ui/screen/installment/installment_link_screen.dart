import 'package:cashiermodule/constants/style.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/controller/installment_link_controller.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_common_style.dart';
import 'package:mposxs/app/ui/widget/common_screen.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:slide_countdown/slide_countdown.dart';

import '../../theme/app_dimens.dart';
import '../../widget/amount_widget.dart';
import '../../widget/common_button.dart';

class InstallmentLinkScreen extends GetView<InstallmentLinkController> {
  final MyAppController _appController = Get.find<MyAppController>();

  Future<bool> onWillPop() {
    controller.onPressClose();
    return Future.value(false);
  }

  @override
  Widget build(BuildContext context) {
    controller.context = context;
    double heightHeader =
        (Get.height / 3) + (MediaQuery.of(context).padding.top > 24 ? 15 : 0);
    double heightHeaderContent = (Get.width / 15) * 3.2 -
        MediaQuery.of(context).padding.top +
        (MediaQuery.of(context).padding.top > 24 ? 15 : 0);
    return CommonScreen(
      child: MyAppController.isKozenP12orN4()
          ? buildScreenP12()
          : WillPopScope(
              onWillPop: onWillPop,
              child: Stack(
                children: <Widget>[
                  Container(
                    width: MediaQuery.of(context).size.width,
                    height: heightHeader,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: [
                            AppColors.headerGradient1,
                            AppColors.headerGradient2
                          ]),
                      borderRadius: BorderRadius.only(
                        bottomLeft: Radius.circular(30),
                        bottomRight: Radius.circular(30),
                      ),
                    ),
                  ),
                  Column(
                    children: <Widget>[
                      Container(
                        width: MediaQuery.of(context).size.width,
                        height: heightHeaderContent,
                        margin: EdgeInsets.only(
                            top: MediaQuery.of(context).padding.top),
                        child: Center(
                          child: Text(
                            AppStrings.getString(AppStrings.createLink) ?? '',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 20,
                              color: AppColors.white,
                              fontFamily: AppFonts.robotoMedium,
                            ),
                          ),
                        ),
                      ),
                      Expanded(
                        flex: 1,
                        child: SingleChildScrollView(
                          child: Container(
                            margin: EdgeInsets.symmetric(
                                vertical: 10, horizontal: 15),
                            padding: EdgeInsets.symmetric(
                                vertical: 15, horizontal: 10),
                            decoration: bodyDecoration(),
                            child: Column(
                              children: <Widget>[
                                Text(
                                  AppStrings.getString(AppStrings.installmentContent)!,
                                  textAlign: TextAlign.center,
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontFamily: AppFonts.robotoMedium,
                                    color: AppColors.darkGrayText,
                                  ),
                                ),
                                Padding(
                                  padding:
                                      const EdgeInsets.only(top: 15, bottom: 5),
                                  child: Text(
                                    AppStrings.getString(AppStrings.installmentContentExpire)!,
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: AppColors.greyTextContent,
                                    ),
                                  ),
                                ),
                                // todo anhnt edit
                                SlideCountdown(
                                  duration: Duration(
                                      minutes: _appController
                                              .installmentInfoSession
                                              ?.minuteExpired ??
                                          0),
                                  separatorType: SeparatorType.symbol,
                                  slideDirection: SlideDirection.up,
                                  durationTitle: DurationTitle.id(),
                                  separator: ":",
                                  decoration: BoxDecoration(),
                                  textStyle: TextStyle(
                                    fontSize: 15,
                                    fontFamily: AppFonts.robotoBold,
                                    color: AppColors.blackDarkText,
                                  ),
                                ),
                                // SlideCountdownClock(
                                //   duration: Duration(minutes: _appController.installmentInfoSession?.minuteExpired ?? 0),
                                //   slideDirection: SlideDirection.Up,
                                //   separator: ":",
                                //   textStyle: TextStyle(
                                //     fontSize: 15,
                                //     fontFamily: AppFonts.robotoBold,
                                //     color: AppColors.blackDarkText,
                                //   ),
                                //   onDone: () {},
                                // ),
                                Padding(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 10),
                                  child: Text(
                                    AppUtils.formatCurrency(_appController
                                            .installmentInfoSession
                                            ?.finalAmountPay) +
                                        'đ',
                                    textAlign: TextAlign.center,
                                    style: TextStyle(
                                      fontSize: 26,
                                      fontFamily: AppFonts.robotoMedium,
                                      color: AppColors.redText2,
                                    ),
                                  ),
                                ),
                                TouchableWidget(
                                  onPressed: controller.onPressLink,
                                  padding: EdgeInsets.all(15),
                                  margin: EdgeInsets.only(bottom: 15),
                                  decoration: BoxDecoration(
                                    color: AppColors.gray3,
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(8)),
                                  ),
                                  child: Row(
                                    children: <Widget>[
                                      Image.asset(
                                        AppImages.icLink,
                                        width: 20,
                                        height: 20,
                                      ),
                                      Expanded(
                                        flex: 1,
                                        child: Padding(
                                          padding:
                                              const EdgeInsets.only(left: 10),
                                          child: Text(
                                            _appController
                                                    .installmentInfoSession
                                                    ?.linkCheckout ??
                                                '',
                                            style: TextStyle(
                                              fontSize: 14,
                                              fontFamily: AppFonts.robotoMedium,
                                              color: AppColors.blueText,
                                            ),
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Row(
                                  children: <Widget>[
                                    Expanded(
                                      flex: 1,
                                      child: TouchableWidget(
                                        height: 50,
                                        onPressed: controller.onPressCopy,
                                        decoration: BoxDecoration(
                                          color: AppColors.blue,
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(6)),
                                        ),
                                        child: Text(
                                          AppStrings.getString(AppStrings.copyLink) ?? '',
                                          style: buttonTextStyle(),
                                        ),
                                      ),
                                    ),
                                    Container(
                                      width: 10,
                                    ),
                                    Expanded(
                                      flex: 1,
                                      child: TouchableWidget(
                                        height: 50,
                                        onPressed: controller.onPressShare,
                                        decoration: BoxDecoration(
                                          color: AppColors.warning,
                                          borderRadius: BorderRadius.all(
                                              Radius.circular(6)),
                                        ),
                                        child: Text(
                                          AppStrings.getString(AppStrings.shareLink) ?? '',
                                          style: buttonTextStyle(),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                TouchableWidget(
                                  height: 50,
                                  onPressed: controller.onPressClose,
                                  margin: EdgeInsets.only(top: 10),
                                  decoration: BoxDecoration(
                                    borderRadius:
                                        BorderRadius.all(Radius.circular(6)),
                                    border: Border.all(
                                      color: AppColors.grayBorder,
                                      width: 1,
                                    ),
                                  ),
                                  child: Text(
                                    AppStrings.getString(AppStrings.close) ?? '',
                                    style: buttonTextStyle(
                                        color: AppColors.greyTextContent),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  AnimatedContainer(
                    duration: Duration(milliseconds: 300),
                    height: controller.showMessageCopySuccess.value
                        ? 40 + MediaQuery.of(context).padding.top
                        : 0,
                    color: AppColors.green,
                    padding: EdgeInsets.only(
                        top: MediaQuery.of(context).padding.top,
                        left: 15,
                        right: 15),
                    child: Row(
                      children: <Widget>[
                        Container(
                          width: 18,
                          height: 18,
                          padding: EdgeInsets.all(3),
                          margin: EdgeInsets.only(right: 10),
                          decoration: BoxDecoration(
                              color: AppColors.white,
                              borderRadius: BorderRadius.circular(10)),
                          child: Image.asset(
                            AppImages.icCheck,
                            color: AppColors.green,
                          ),
                        ),
                        Text(
                          AppStrings.getString(AppStrings.copyUrlSuccessful) ?? '',
                          style: TextStyle(
                            fontSize: 14,
                            fontFamily: AppFonts.robotoMedium,
                            color: AppColors.white,
                          ),
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget buildScreenP12() {
    return WillPopScope(
      onWillPop: onWillPop,
      child: Stack(
        children: <Widget>[
          Container(
            width: MediaQuery.of(controller.context).size.width,
            color: Colors.white,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(
                  margin: EdgeInsets.only(top: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Container(
                        child: Text(
                          'Link Trả góp ',
                          style: style_S16_W400_BlackColor,
                        ),
                      ),

                      Text('(Hết hạn: ', style: style_S14_W400_RedColor,),
                      Container(
                        child: Obx(
                              () => controller.isExpireDate.value ? Text('Hết hạn') : SlideCountdown(
                                padding: EdgeInsets.zero,
                            duration: Duration(
                                minutes: _appController
                                    .installmentInfoSession
                                    ?.minuteExpired ??
                                    0),
                            separatorType: SeparatorType.symbol,
                            slideDirection: SlideDirection.up,
                            durationTitle: DurationTitle.id(),
                              separatorStyle: style_S14_W400_RedColor,
                            decoration: BoxDecoration(),
                            textStyle: style_S14_W400_RedColor,
                            onDone: () {
                              controller.showPopUpExpireDate();
                            },
                          ),
                        ),
                      ),
                      Text(')', style: style_S14_W400_RedColor,),

                    ],
                  ),
                ),
                SizedBox(
                  height: 10,
                ),
                Expanded(
                    child: Container(
                  decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(10),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.black.withOpacity(0.2),
                          spreadRadius: 1,
                          blurRadius: 2,
                        ),
                      ]),
                  // padding: EdgeInsets.all(10),
                  child: QrImageView(
                    padding: EdgeInsets.all(10),
                    backgroundColor: Colors.transparent,
                    data: _appController.installmentInfoSession?.linkCheckout ??
                        '',
                    version: QrVersions.auto,
                  ),
                )),
                Container(
                  margin: EdgeInsets.only(top: 10),
                  child: AmountWidget(
                    amount: _appController
                            .installmentInfoSession?.finalAmountPay
                            .toString() ??
                        "",
                    textColor: AppColors.orange1,
                    fontSize: AppDimens.textSizeLarge28,
                  ),
                ),
                Container(
                  // margin: EdgeInsets.only(left: 40, right: 40, bottom: 10),
                  margin: EdgeInsets.symmetric(horizontal: 10, vertical: 10),
                  child: Text(
                    AppStrings.getString(AppStrings.tv_guide_scan_qr_link) ?? '',
                    style: style_S14_W400_HintColor,
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.symmetric(vertical: AppDimens.spaceLarge24),
            child: CommonButton(
              padding: EdgeInsets.zero,
              color: AppColors.transparent,
              child: Image.asset(
                AppImages.ic_cancel_qr,
                alignment: Alignment.centerLeft,
                width: AppDimens.icon40,
                height: AppDimens.icon40,
                // height: AppDimens.icon60,
              ),
              onPressed: () => controller.onPressClose(),
            ),
          ),
          // Container(
          //   margin: EdgeInsets.only(top: 20),
          //   child: Align(
          //     alignment: Alignment.topRight,
          //     child: Obx(
          //           () => controller.isExpireDate.value ? Text('Hết hạn') : SlideCountdown(
          //         duration: Duration(
          //             minutes: _appController
          //                 .installmentInfoSession
          //                 ?.minuteExpired ??
          //                 0),
          //         separatorType: SeparatorType.symbol,
          //         slideDirection: SlideDirection.up,
          //         durationTitle: DurationTitle.id(),
          //           separatorStyle: style_S14_W400_RedColor,
          //         decoration: BoxDecoration(),
          //         textStyle: style_S14_W400_RedColor,
          //         onDone: () {
          //           controller.showPopUpExpireDate();
          //         },
          //       ),
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }
}
