import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/installment_list_bank_controller.dart';
import 'package:mposxs/app/data/model/mp_data_login_model.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_common_style.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';
import 'package:mposxs/app/ui/widget/common_header.dart';
import 'package:mposxs/app/ui/widget/common_image_network.dart';
import 'package:mposxs/app/ui/widget/common_screen.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';

class InstallmentListBankScreen extends GetView<InstallmentListBankController> {
  Widget buildItemBank(MPInstallmentInfo item, BuildContext context) {
    return TouchableWidget(
      onPressed: () => controller.onPressItemBank(item),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        children: <Widget>[
          Expanded(
            flex: 2,
            child: Padding(
              padding: const EdgeInsets.only(left: 10, right: 10),
              child: CommonImageNetwork(
                url: item.logo,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: FittedBox(
              fit: BoxFit.scaleDown,
              child: Text(
                item.bankName!,
                style: TextStyle(
                  fontSize: 13,
                  color: AppColors.blackBlueText,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    controller.context = context;
    return CommonScreen(
      mainBackgroundColor: AppColors.white,
      header: CommonHeader(
        title: AppStrings.getString(AppStrings.installmentListBankTitle) ?? '',
        headerBackgroundColor: AppColors.white,
        titleColor: AppColors.black,
      ),
      child: Column(
        children: <Widget>[
          SizedBox(height: AppDimens.spaceMedium),
          // search
          Container(
            height: 44,
            margin: EdgeInsets.only(left: 15, right: 15),
            decoration: bodyDecoration(),
            child: Padding(
              padding: const EdgeInsets.only(left: 15, right: 15),
              child: Row(
                children: <Widget>[
                  Expanded(
                      flex: 1,
                      child: TextField(
                        controller: controller.textSearchController,
                        style: TextStyle(fontSize: 16, color: AppColors.blueText, fontFamily: AppFonts.robotoMedium),
                        decoration: InputDecoration(
                          contentPadding: EdgeInsets.all(0),
                          border: InputBorder.none,
                          hintText: AppStrings.getString(AppStrings.installmentListBankSearch) ?? '',
                          hintStyle: TextStyle(
                            fontSize: 14,
                            fontFamily: AppFonts.robotoItalic,
                            color: AppColors.tabUnSelected,
                          ),
                          counter: SizedBox(
                            height: 0.0,
                          ),
                        ),
                        maxLength: 50,
                        onChanged: controller.onChangedSearch,
                      )),
                  Obx(() => controller.showClearSearch.value
                      ? TouchableWidget(
                          width: 20,
                          height: 20,
                          padding: EdgeInsets.all(0),
                          margin: EdgeInsets.only(right: 10),
                          child: Image.asset(
                            AppImages.icClearText3,
                            width: 13,
                            height: 13,
                          ),
                          onPressed: controller.onPressClearSearch,
                        )
                      : SizedBox.shrink()),
                  Image.asset(
                    AppImages.icSearch,
                    width: 18,
                    height: 18,
                    fit: BoxFit.contain,
                  )
                ],
              ),
            ),
          ),
          // listBank
          Expanded(
            flex: 1,
            child: SingleChildScrollView(
              child: Obx(() => GridView.count(
                    shrinkWrap: true,
                    physics: new NeverScrollableScrollPhysics(),
                    padding: EdgeInsets.only(
                      top: 15,
                      left: 15,
                      right: 15,
                      bottom: MediaQuery.of(context).padding.bottom + 15,
                    ),
                    crossAxisCount: 3,
                    crossAxisSpacing: 10,
                    mainAxisSpacing: 10,
                    childAspectRatio: 55 / 41,
                    children:
                        (controller.listBankInstallment).map((item) => buildItemBank(item, context)).toList(),
                  )),
            ),
          ),
        ],
      ),
    );
  }
}
