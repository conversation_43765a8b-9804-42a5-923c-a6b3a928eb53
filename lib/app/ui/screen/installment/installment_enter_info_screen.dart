import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/controller/installment_enter_info_controller.dart';
import 'package:mposxs/app/data/model/mp_data_login_model.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_common_style.dart';
import 'package:mposxs/app/ui/widget/common_header.dart';
import 'package:mposxs/app/ui/widget/common_image_network.dart';
import 'package:mposxs/app/ui/widget/common_screen.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';
import 'package:mposxs/app/util/app_utils.dart';

class InstallmentEnterInfoScreen extends GetView<InstallmentEnterInfoController> {
  final MyAppController _appController = Get.find<MyAppController>();

  Widget buildItemCardType(Map item) {
    bool isSelected = (item['id'] == controller.selectedCardType.value['id']);
    return Stack(
      children: <Widget>[
        Container(
          width: MyAppController.isKozenP12orN4() ? 50 : 79,
          height: 47,
          margin: EdgeInsets.only(right: 5),
          alignment: Alignment.topLeft,
          child: TouchableWidget(
            width: 74,
            height: 42,
            padding: EdgeInsets.all(0),
            onPressed: () => controller.onPressItemCardType(item),
            decoration: BoxDecoration(
                color: AppColors.grayBackground,
                borderRadius: BorderRadius.all(Radius.circular(6)),
                border: Border.all(color: !isSelected ? AppColors.grayBackground : AppColors.primary, width: 1)),
            child: Image.asset(item['logo']),
          ),
        ),
        Positioned(
          bottom: 1,
          right: 6,
          child: !isSelected
              ? Container()
              : Container(
                  width: 15,
                  height: 15,
                  padding: EdgeInsets.all(3),
                  decoration: BoxDecoration(color: AppColors.primary, borderRadius: BorderRadius.circular(7.5)),
                  child: Image.asset(
                    AppImages.icCheck,
                  ),
                ),
        ),
      ],
    );
  }

  Widget buildItemPeriod(MPItemPeriod item) {
    bool isSelected = false;
    if (controller.selectedPeriod.value.installmentOutId != null) {
      isSelected = (item.installmentOutId == controller.selectedPeriod.value.installmentOutId);
    }
    return TouchableWidget(
      padding: EdgeInsets.symmetric(vertical: 8, horizontal: 10),
      onPressed: () => controller.onPressItemPeriod(item),
      width: 60,
      margin: EdgeInsets.only(right: 10),
      decoration: BoxDecoration(
          color: isSelected ? AppColors.primary : AppColors.grayBackground, borderRadius: BorderRadius.circular(6)),
      child: Column(
        children: <Widget>[
          Text(
            item.period!,
            style: TextStyle(
                fontSize: 22,
                fontFamily: AppFonts.robotoMedium,
                color: isSelected ? AppColors.white : AppColors.greyTextContent),
          ),
          Text(
            AppStrings.getString(AppStrings.month) ?? '',
            style: TextStyle(fontSize: 12, color: isSelected ? AppColors.white : AppColors.greyTextContent),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    Color colorCheck = controller.allowChangeFee ? AppColors.primary : AppColors.tabUnSelected;
    return CommonScreen(
      header: CommonHeader(
        title: AppStrings.getString(AppStrings.installmentPaymentTitle) ?? '',
        titleColor: AppColors.white,
        headerBackgroundHeight: MyAppController.isKozenP12orN4()?110: 160,
        headerDecoration: BoxDecoration(
          color: AppColors.primary,
          borderRadius: BorderRadius.only(
            bottomLeft: Radius.circular(22),
            bottomRight: Radius.circular(22),
          ),
        ),
        headerExtend: MyAppController.isKozenP12orN4()?_buildExtendHeaderP12():_buildExtendHeaderNormal(),
      ),
      child: Column(
        children: <Widget>[
          Expanded(
            flex: 1,
            child: SingleChildScrollView(
              child: Container(
                margin: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                padding: EdgeInsets.symmetric(vertical: 10),
                decoration: bodyDecoration(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      child: Row(
                        children: <Widget>[
                          Container(
                            width: 74,
                            height: 42,
                            padding: EdgeInsets.all(5),
                            margin: EdgeInsets.only(right: 10),
                            decoration: BoxDecoration(
                              color: AppColors.grayBackground,
                              borderRadius: BorderRadius.all(Radius.circular(6)),
                            ),
                            child: CommonImageNetwork(
                              url: _appController.installmentInfoSession?.selectedBank?.logo ?? '',
                            ),
                          ),
                          Expanded(
                            flex: 1,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Text(
                                  (_appController.installmentInfoSession?.selectedBank?.bankName ?? '') +
                                      ' - ' +
                                      (_appController.installmentInfoSession?.selectedBank?.bankLongName ?? ''),
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: AppColors.darkGrayText,
                                  ),
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                                Text(
                                  AppStrings.getString(AppStrings.doNotSupportDebitCard) ?? '',
                                  style: TextStyle(
                                    fontSize: 12,
                                    fontFamily: AppFonts.robotoItalic,
                                    color: AppColors.redText2,
                                  ),
                                ),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                    Container(
                      height: 40,
                      margin: EdgeInsets.only(top: 5),
                      child: Row(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(top: 10, bottom: 10, left: 10),
                            child: Text(
                              AppStrings.getString(AppStrings.installmentSelectTypeCardLabel) ?? '',
                              style: TextStyle(
                                fontSize: 14,
                                color: AppColors.darkGrayText,
                              ),
                            ),
                          ),
                          // SingleChildScrollView(
                          //   scrollDirection: Axis.horizontal,
                          //   padding: EdgeInsets.symmetric(horizontal: 10),
                          //   child: ConstrainedBox(
                          //     constraints: BoxConstraints(minWidth: MediaQuery.of(context).size.width),
                          //     child: Obx(() => Row(
                          //       children: controller.listDataCard.map(buildItemCardType).toList(),
                          //     )),
                          //   ),
                          // ),

                          Expanded(
                            child: ListView(
                              padding: EdgeInsets.symmetric(horizontal: 10),
                              scrollDirection: Axis.horizontal,
                              children: [
                                Obx(() => Row(
                                  children: controller.listDataCard.map(buildItemCardType).toList(),),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      height: 65,
                      margin: EdgeInsets.only(top: 10),
                      child: Row(
                        children: [
                          Padding(
                            padding: const EdgeInsets.only(top: 15, bottom: 10, left: 10),
                            child: Text(
                              AppStrings.getString(AppStrings.installmentInfoPeriodTitle) ?? '',
                              style: TextStyle(
                                fontSize: 14,
                                color: AppColors.darkGrayText,
                              ),
                            ),
                          ),
                          Expanded(
                            child: ListView(
                              padding: EdgeInsets.symmetric(horizontal: 10),
                              scrollDirection: Axis.horizontal,
                              children: [
                                  Obx(() => Row(
                                        children: (_appController.installmentInfoSession?.selectedBank?.listPeriod ?? [])
                                            .map(buildItemPeriod)
                                            .toList()),
                                  ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    (controller.allowScanCard && controller.allowCreateLink
                        ? TouchableWidget(
                            onPressed: controller.onCheckedPayLinkChange,
                            padding: EdgeInsets.only(top: 10, bottom: 10, right: 10),
                            margin: EdgeInsets.only(top: 10, left: 10),
                            child: Row(
                              children: <Widget>[
                                Obx(() => Container(
                                      width: 18,
                                      height: 18,
                                      margin: EdgeInsets.only(right: 10),
                                      padding: EdgeInsets.all(3),
                                      decoration: BoxDecoration(
                                          color: controller.isCheckPayLink.value ? AppColors.primary : null,
                                          borderRadius: BorderRadius.circular(9),
                                          border: Border.all(color: AppColors.primary, width: 1)),
                                      child: controller.isCheckPayLink.value
                                          ? Image.asset(
                                              AppImages.icCheck,
                                            )
                                          : Container(),
                                    )),
                                Text(
                                  AppStrings.getString(AppStrings.installmentLinkTitle) ?? '',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: AppColors.primary,
                                  ),
                                )
                              ],
                            ),
                          )
                        : SizedBox.shrink()),
                  ],
                ),
              ),
            ),
          ),
          //==> fee + total amount + btn
          MyAppController.isKozenP12orN4()?_buildBottomWidgetP12(context, colorCheck):_buildBottomWidgetNormal(context, colorCheck)

        ],
      ),
    );
  }

  _buildExtendHeaderP12(){
    return Row(children: [
      Expanded(
        flex: 1,
        child: Container(
          alignment: Alignment.centerRight,
          child: Text(
            '${AppStrings.getString(AppStrings.labelBillAmount)}:',
            style: TextStyle(fontSize: 15, color: Colors.white),
          ),
        ),
      ),
      Expanded(
        flex: 1,
        child: FittedBox(
        fit: BoxFit.contain,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Text(
              AppUtils.formatCurrency(_appController.installmentInfoSession?.amount ?? 0),
              style: TextStyle(fontSize: 34, color: AppColors.white),
            ),
            Padding(
              padding: const EdgeInsets.only(bottom: 20, left: 2),
              child: Text(
                'đ',
                style: TextStyle(
                    fontSize: 16,
                    color: AppColors.white.withOpacity(0.66),
                    decoration: TextDecoration.underline),
              ),
            ),
          ],
        ),
      ))
    ],);
  }

  _buildExtendHeaderNormal(){
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          AppStrings.getString(AppStrings.labelBillAmount) ?? '',
          style: TextStyle(fontSize: 14, color: AppColors.white.withOpacity(0.5)),
        ),
        FittedBox(
          fit: BoxFit.contain,
          child: Padding(
            padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Text(
                  AppUtils.formatCurrency(_appController.installmentInfoSession?.amount ?? 0),
                  style: TextStyle(fontSize: 34, color: AppColors.white),
                ),
                Padding(
                  padding: const EdgeInsets.only(bottom: 20, left: 2),
                  child: Text(
                    'đ',
                    style: TextStyle(
                        fontSize: 16,
                        color: AppColors.white.withOpacity(0.66),
                        decoration: TextDecoration.underline),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  _buildBottomWidgetP12(context, colorCheck) {
    return Container(
      padding: EdgeInsets.only(top: 10, left: 10, right: 10, bottom: 10),
      decoration: bottomDecoration(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          //==> fee
          Row(
            children: <Widget>[
              Expanded(
                flex: 1,
                child: DottedBorder(
                  borderType: BorderType.RRect,
                  dashPattern: [3, 3],
                  radius: Radius.circular(6),
                  color: colorCheck,
                  child: ClipRRect(
                    borderRadius: BorderRadius.all(Radius.circular(4)),
                    child: TouchableWidget(
                      onPressed: controller.allowChangeFee ? controller.onCheckedFeeInstallment : null,
                      padding: EdgeInsets.all(5),
                      decoration: BoxDecoration(
                        color: colorCheck.withOpacity(0.05),
                      ),
                      child: Row(
                        children: <Widget>[
                          Obx(() => Container(
                            width: 18,
                            height: 18,
                            margin: EdgeInsets.only(right: 5),
                            padding: EdgeInsets.all(3),
                            decoration: BoxDecoration(
                                color: controller.enableFeeInstallment.value ? colorCheck : null,
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(color: colorCheck, width: 1)),
                            child: controller.enableFeeInstallment.value
                                ? Image.asset(
                              AppImages.icCheck,
                            )
                                : SizedBox.shrink(),
                          )),
                          Expanded(
                            flex: 1,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: <Widget>[
                                Text(
                                  AppStrings.getString(AppStrings.installmentInfoFeeInstallmentCard) ?? '',
                                  maxLines: 1,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: colorCheck,
                                  ),
                                ),
                                Obx(() => Text(
                                  AppUtils.formatCurrency(controller.feeInstallment.value) + 'đ',
                                  maxLines: 1,
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontFamily: AppFonts.robotoMedium,
                                    color: colorCheck,
                                  ),
                                )),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              Container(
                width: 10,
              ),
              Expanded(
                flex: 1,
                child: DottedBorder(
                  borderType: BorderType.RRect,
                  dashPattern: [3, 3],
                  radius: Radius.circular(6),
                  color: colorCheck,
                  child: ClipRRect(
                    borderRadius: BorderRadius.all(Radius.circular(4)),
                    child: TouchableWidget(
                      onPressed: controller.allowChangeFee ? controller.onCheckedFeeTrans : null,
                      padding: EdgeInsets.all(5),
                      decoration: BoxDecoration(
                        color: colorCheck.withOpacity(0.05),
                      ),
                      child: Row(
                        children: <Widget>[
                          Obx(() => Container(
                            width: 18,
                            height: 18,
                            margin: EdgeInsets.only(right: 5),
                            padding: EdgeInsets.all(3),
                            decoration: BoxDecoration(
                                color: controller.enableFeeTrans.value ? colorCheck : null,
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(color: colorCheck, width: 1)),
                            child: controller.enableFeeTrans.value
                                ? Image.asset(
                              AppImages.icCheck,
                            )
                                : SizedBox.shrink(),
                          )),
                          Expanded(
                            flex: 1,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: <Widget>[
                                Obx(() => Text(
                                  controller.isCheckPayLink.value
                                      ? AppStrings.getString(AppStrings.installmentInfoFeeCreateLink) ?? ''
                                      : AppStrings.getString(AppStrings.installmentInfoFeeScanCard) ?? '',
                                  maxLines: 1,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: colorCheck,
                                  ),
                                )),
                                Obx(() => Text(
                                  AppUtils.formatCurrency(controller.feeCard.value) + 'đ',
                                  maxLines: 1,
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontFamily: AppFonts.robotoMedium,
                                    color: colorCheck,
                                  ),
                                )),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 10,),
          Row(
            children: [
              Expanded(
                child: Column(children: [
                  Text(
                    '${AppStrings.getString(AppStrings.totalAmount)}:',
                    style: TextStyle(
                      fontSize: 14,
                      color: AppColors.greyTextContent,
                    ),
                  ),
                  Obx(() => Text(
                    AppUtils.formatCurrency(controller.finalAmountPay.value) + 'đ',
                    style: TextStyle(
                      fontSize: 20,
                      fontFamily: AppFonts.robotoMedium,
                      color: AppColors.warning,
                    ),
                  )),

                ],),
              ),
              TouchableWidget(
                height: 50,
                onPressed: controller.onPressContinue,
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.all(Radius.circular(6)),
                ),
                child: Obx(() => Text(
                  controller.isCheckPayLink.value
                      ? AppStrings.getString(AppStrings.installmentCreateLinkButton) ?? ''
                      : AppStrings.getString(AppStrings.continueText) ?? '',
                  style: buttonTextStyle(),
                )),
              ),
            ],
          )

        ],
      ),
    );
  }

  _buildBottomWidgetNormal(context, colorCheck) {
    return Container(
      padding: EdgeInsets.only(top: 15, left: 15, right: 15, bottom: 15 + MediaQuery.of(context).padding.bottom),
      decoration: bottomDecoration(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          //==> fee
          Row(
            children: <Widget>[
              Expanded(
                flex: 1,
                child: DottedBorder(
                  borderType: BorderType.RRect,
                  dashPattern: [3, 3],
                  radius: Radius.circular(6),
                  color: colorCheck,
                  child: ClipRRect(
                    borderRadius: BorderRadius.all(Radius.circular(4)),
                    child: TouchableWidget(
                      onPressed: controller.allowChangeFee ? controller.onCheckedFeeInstallment : null,
                      padding: EdgeInsets.all(5),
                      decoration: BoxDecoration(
                        color: colorCheck.withOpacity(0.05),
                      ),
                      child: Row(
                        children: <Widget>[
                          Obx(() => Container(
                            width: 18,
                            height: 18,
                            margin: EdgeInsets.only(right: 5),
                            padding: EdgeInsets.all(3),
                            decoration: BoxDecoration(
                                color: controller.enableFeeInstallment.value ? colorCheck : null,
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(color: colorCheck, width: 1)),
                            child: controller.enableFeeInstallment.value
                                ? Image.asset(
                              AppImages.icCheck,
                            )
                                : SizedBox.shrink(),
                          )),
                          Expanded(
                            flex: 1,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: <Widget>[
                                Text(
                                  AppStrings.getString(AppStrings.installmentInfoFeeInstallmentCard) ?? '',
                                  maxLines: 1,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: colorCheck,
                                  ),
                                ),
                                Obx(() => Text(
                                  AppUtils.formatCurrency(controller.feeInstallment.value) + 'đ',
                                  maxLines: 1,
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontFamily: AppFonts.robotoMedium,
                                    color: colorCheck,
                                  ),
                                )),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ),
              Container(
                width: 10,
              ),
              Expanded(
                flex: 1,
                child: DottedBorder(
                  borderType: BorderType.RRect,
                  dashPattern: [3, 3],
                  radius: Radius.circular(6),
                  color: colorCheck,
                  child: ClipRRect(
                    borderRadius: BorderRadius.all(Radius.circular(4)),
                    child: TouchableWidget(
                      onPressed: controller.allowChangeFee ? controller.onCheckedFeeTrans : null,
                      padding: EdgeInsets.all(5),
                      decoration: BoxDecoration(
                        color: colorCheck.withOpacity(0.05),
                      ),
                      child: Row(
                        children: <Widget>[
                          Obx(() => Container(
                            width: 18,
                            height: 18,
                            margin: EdgeInsets.only(right: 5),
                            padding: EdgeInsets.all(3),
                            decoration: BoxDecoration(
                                color: controller.enableFeeTrans.value ? colorCheck : null,
                                borderRadius: BorderRadius.circular(4),
                                border: Border.all(color: colorCheck, width: 1)),
                            child: controller.enableFeeTrans.value
                                ? Image.asset(
                              AppImages.icCheck,
                            )
                                : SizedBox.shrink(),
                          )),
                          Expanded(
                            flex: 1,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: <Widget>[
                                Obx(() => Text(
                                  controller.isCheckPayLink.value
                                      ? AppStrings.getString(AppStrings.installmentInfoFeeCreateLink) ?? ''
                                      : AppStrings.getString(AppStrings.installmentInfoFeeScanCard) ?? '',
                                  maxLines: 1,
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: colorCheck,
                                  ),
                                )),
                                Obx(() => Text(
                                  AppUtils.formatCurrency(controller.feeCard.value) + 'đ',
                                  maxLines: 1,
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontFamily: AppFonts.robotoMedium,
                                    color: colorCheck,
                                  ),
                                )),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),

          Padding(
            padding: const EdgeInsets.only(top: 10, bottom: 10),
            child: Row(
              children: <Widget>[
                Text(
                  '${AppStrings.getString(AppStrings.totalAmount)}:',
                  style: TextStyle(
                    fontSize: 14,
                    color: AppColors.greyTextContent,
                  ),
                ),
                Spacer(),
                Obx(() => Text(
                  AppUtils.formatCurrency(controller.finalAmountPay.value) + 'đ',
                  style: TextStyle(
                    fontSize: 20,
                    fontFamily: AppFonts.robotoMedium,
                    color: AppColors.warning,
                  ),
                )),
              ],
            ),
          ),
          TouchableWidget(
            height: 50,
            onPressed: controller.onPressContinue,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.all(Radius.circular(6)),
            ),
            child: Obx(() => Text(
              controller.isCheckPayLink.value
                  ? AppStrings.getString(AppStrings.installmentCreateLinkButton) ?? ''
                  : AppStrings.getString(AppStrings.continueText) ?? '',
              style: buttonTextStyle(),
            )),
          ),
        ],
      ),
    );
  }
}
