import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/change_pass_controller.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_common_style.dart';
import 'package:mposxs/app/ui/widget/common_header.dart';
import 'package:mposxs/app/ui/widget/common_screen.dart';
import 'package:mposxs/app/ui/widget/common_text_field.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';
import 'package:mposxs/app/util/app_validation.dart';

class ChangePassScreen extends GetView<ChangePassController> {
  @override
  Widget build(BuildContext context) {
    controller.context = context;
    return CommonScreen(
      header: CommonHeader(
        title: AppStrings.getString(AppStrings.buttonChangePassword) ?? '',
        // title: AppStrings.labelChangePassword.tr,
        headerBackgroundColor: AppColors.white,
        titleColor: AppColors.black,
      ),
      child: Column(children: <Widget>[
        Expanded(
          flex: 1,
          child: SingleChildScrollView(
            child: Container(
              margin: EdgeInsets.symmetric(vertical: 10, horizontal: 15),
              padding: EdgeInsets.symmetric(vertical: 15, horizontal: 15),
              decoration: bodyDecoration(),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    AppStrings.getString(AppStrings.contentChangePassword) ?? '',
                    style: TextStyle(
                      fontSize: 16,
                      color: AppColors.blackBlueText,
                    ),
                  ),
                  Obx(() => CommonTextField(
                        height: 80,
                        fontSize: 20,
                        fontFamily: AppFonts.robotoMedium,
                        controller: controller.textOldPassController,
                        hintText: AppStrings.getString(AppStrings.passwordOld) ?? '',
                        hintTextFontSize: 16,
                        obscureText: !controller.isShowOldPassword.value,
                        errorText:
                            isNullEmpty(controller.errorOldPassword.value) ? null : controller.errorOldPassword.value,
                        onChanged: controller.onChangedOldPassword,
                        keyboardType: TextInputType.number,
                        textInputAction: TextInputAction.next,
                        suffix: TouchableWidget(
                          width: 25,
                          height: 17,
                          padding: EdgeInsets.all(0),
                          child: Image.asset(
                            AppImages.icEye,
                            width: 17,
                            height: 11,
                          ),
                          onPressed: controller.onPressShowHideOldPassword,
                        ),
                      )),
                  Obx(() => CommonTextField(
                        fontSize: 20,
                        fontFamily: AppFonts.robotoMedium,
                        controller: controller.textNewPassController,
                        hintText: AppStrings.getString(AppStrings.passwordNew) ?? '',
                        hintTextFontSize: 16,
                        obscureText: !controller.isShowNewPassword.value,
                        errorText:
                            isNullEmpty(controller.errorNewPassword.value) ? null : controller.errorNewPassword.value,
                        onChanged: controller.onChangedNewPassword,
                        keyboardType: TextInputType.number,
                        textInputAction: TextInputAction.next,
                        suffix: TouchableWidget(
                          width: 25,
                          height: 17,
                          padding: EdgeInsets.all(0),
                          child: Image.asset(
                            AppImages.icEye,
                            width: 17,
                            height: 11,
                          ),
                          onPressed: controller.onPressShowHideNewPassword,
                        ),
                      )),
                  Obx(() => CommonTextField(
                        height: 80,
                        fontSize: 20,
                        fontFamily: AppFonts.robotoMedium,
                        controller: controller.textReNewPassController,
                        hintText: AppStrings.getString(AppStrings.passwordReNew) ?? '',
                        hintTextFontSize: 16,
                        obscureText: !controller.isShowReNewPassword.value,
                        errorText: isNullEmpty(controller.errorReNewPassword.value)
                            ? null
                            : controller.errorReNewPassword.value,
                        onChanged: controller.onChangedReNewPassword,
                        keyboardType: TextInputType.number,
                        suffix: TouchableWidget(
                          width: 25,
                          height: 17,
                          padding: EdgeInsets.all(0),
                          child: Image.asset(
                            AppImages.icEye,
                            width: 17,
                            height: 11,
                          ),
                          onPressed: controller.onPressShowHideReNewPassword,
                        ),
                      )),
                ],
              ),
            ),
          ),
        ),
        Container(
          padding: EdgeInsets.only(top: 10, left: 15, right: 15, bottom: 10 + MediaQuery.of(context).padding.bottom),
          decoration: bottomDecoration(),
          child: TouchableWidget(
            height: 50,
            onPressed: controller.onPressChangePassword,
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.all(Radius.circular(6)),
            ),
            child: Text(
              AppStrings.getString(AppStrings.buttonChangePassword) ?? '',
              style: buttonTextStyle(),
            ),
          ),
        ),
      ]),
    );
  }
}
