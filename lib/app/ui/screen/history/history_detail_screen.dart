import 'package:cashiermodule/constants/style.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/controller/history_detail_controller.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_common_style.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';
import 'package:mposxs/app/ui/widget/common_header.dart';
import 'package:mposxs/app/ui/widget/common_screen.dart';
import 'package:mposxs/app/ui/widget/header_button.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/app_validation.dart';

class HistoryDetailScreen extends GetView<HistoryDetailController> {
  Future<bool> _onWillPop() {
    controller.onPressClosePage();
    return Future.value(false);
  }

  Widget _buildRowItem(String label, String value) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 3),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            '$label:',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.greyTextContent,
            ),
          ),
          SizedBox(
            width: 15,
          ),
          Expanded(
            flex: 1,
            child: Text(
              value,
              maxLines: 3,
              textAlign: TextAlign.end,
              style: TextStyle(
                fontSize: 16,
                color: AppColors.darkGrayText,
                fontFamily: AppFonts.robotoMedium,
              ),
            ),
          ),
        ],
      ),
    );
  }

  buildBodyNormal() {
    return Container(
      child: ListView(
        padding: EdgeInsets.all(15),
        children: <Widget>[
          Column(
            children: <Widget>[
              _buildRowItem(AppStrings.getString(AppStrings.labelPaymentMethod) ?? '',
                  controller.paymentMethod.value ?? ''),
              Container(
                margin: EdgeInsets.symmetric(vertical: 3),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Text(
                      '${AppStrings.getString(AppStrings.totalAmount)}:',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.greyTextContent,
                      ),
                    ),
                    buildWidgetAmount(NumberFormat("#,###", "vi_VN").format(
                        controller.detailTransaction.value?.amount ?? 0))
                    // Text(
                    //   '${NumberFormat("#,###", "vi_VN").format(controller.detailTransaction.value?.amount ?? 0)}đ',
                    //   style: TextStyle(
                    //     fontSize: 20,
                    //     color: AppColors.orangeDark,
                    //     fontFamily: AppFonts.robotoBold,
                    //   ),
                    // ),
                    // Padding(
                    //   padding: const EdgeInsets.only(bottom: 3),
                    //   child: Text(
                    //     'đ',
                    //     style: TextStyle(
                    //         fontSize: 12,
                    //         color: AppColors.blackText,
                    //         fontFamily: AppFonts.robotoMedium,
                    //         decoration: TextDecoration.underline),
                    //   ),
                    // )
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.symmetric(vertical: 3),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Text(
                      '${AppStrings.getString(AppStrings.labelStatus)}:',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.greyTextContent,
                      ),
                    ),
                    Container(
                      padding:
                          EdgeInsets.symmetric(vertical: 2, horizontal: 15),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(21),
                        color: controller.colorStatus.value,
                      ),
                      child: Text(
                        controller.textStatus.value,
                        style: TextStyle(
                          fontSize: 13,
                          color: AppColors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              (isNullEmpty(controller.errMessage.value)
                  ? SizedBox.shrink()
                  : Container(
                      width: Get.width - 30,
                      padding:
                          EdgeInsets.symmetric(vertical: 10, horizontal: 15),
                      margin: const EdgeInsets.symmetric(vertical: 10),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6),
                        color: controller.errMessageBackground.value,
                      ),
                      child: Column(
                        children: <Widget>[
                          Text(
                            controller.errMessage.value,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: controller.errMessageTextColor.value,
                              fontSize: 14,
                              fontFamily: AppFonts.robotoItalic,
                            ),
                          ),
                        ],
                      ),
                    )),
              (!isNullEmpty(controller.errVMMessage.value) &&
                      controller.isVaymuon
                  ? Container(
                      width: Get.width - 30,
                      padding:
                          EdgeInsets.symmetric(vertical: 10, horizontal: 15),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6),
                        color: controller.errVMMessageBackground.value,
                      ),
                      child: Column(
                        children: <Widget>[
                          Text(
                            controller.errVMMessage.value,
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: controller.errVMMessageTextColor.value,
                              fontSize: 14,
                              fontFamily: AppFonts.robotoItalic,
                            ),
                          ),
                        ],
                      ),
                    )
                  : SizedBox.shrink()),
              Container(
                color: AppColors.lightGrayOpacity,
                width: Get.width - 30,
                height: 1,
                margin: EdgeInsets.symmetric(vertical: 10),
              ),
              (controller.isNormal
                  ? _buildRowItem(AppStrings.getString(AppStrings.labelCardHolderName) ?? '',
                      controller.detailTransaction.value?.cardholderName ?? '')
                  : SizedBox.shrink()),
              (controller.isNormal
                  ? _buildRowItem(AppStrings.getString(AppStrings.labelCardType) ?? '',
                      controller.detailTransaction.value?.issuerName ?? '')
                  : SizedBox.shrink()),
              (controller.isNormal
                  ? _buildRowItem(AppStrings.getString(AppStrings.labelCardNumber) ?? '',
                      controller.detailTransaction.value?.pan ?? '')
                  : SizedBox.shrink()),
              (controller.isVaymuon
                  ? _buildRowItem(AppStrings.getString(AppStrings.labelCustomer) ?? '',
                      controller.detailTransaction.value?.cardholderName ?? '')
                  : SizedBox.shrink()),
              Container(
                color: AppColors.lightGrayOpacity,
                width: Get.width - 30,
                height: 1,
                margin: EdgeInsets.symmetric(vertical: 10),
              ),
              (controller.detailTransaction.value?.transactionType == 'NORMAL'
                  ? SizedBox.shrink()
                  : _buildRowItem(AppStrings.getString(AppStrings.authCode)!,
                      controller.detailTransaction.value?.authCode ?? '')),
              (controller.detailTransaction.value?.transactionType == 'NORMAL'
                  ? SizedBox.shrink()
                  : _buildRowItem(AppStrings.getString(AppStrings.rrnNo)!,
                      controller.detailTransaction.value?.rrn ?? '')),
              (controller.detailTransaction.value?.transactionType == 'NORMAL'
                  ? SizedBox.shrink()
                  : Container(
                      color: AppColors.lightGrayOpacity,
                      width: Get.width - 30,
                      height: 1,
                      margin: EdgeInsets.symmetric(vertical: 10),
                    )),
              _buildRowItem(AppStrings.getString(AppStrings.labelTransactionId)!,
                  controller.detailTransaction.value?.txid ?? ''),
              _buildRowItem(
                  'Ref No', controller.detailTransaction.value?.rrn ?? ''),
              _buildRowItem(AppStrings.getString(AppStrings.labelTransactionTime)!,
                  '${DateFormat('HH:mm, dd/MM/yyyy', 'vi_VN').format(DateTime.fromMillisecondsSinceEpoch(controller.detailTransaction.value?.createdDate ?? DateTime.now().millisecondsSinceEpoch, isUtc: true).add(Duration(hours: 7)))}'),
              SizedBox(height: 10),
              (!isNullEmpty(controller.description.value)
                  ? Container(
                      margin: EdgeInsets.only(bottom: 20),
                      width: Get.width - 30,
                      padding:
                          EdgeInsets.symmetric(vertical: 10, horizontal: 20),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6),
                        color: AppColors.gray3,
                      ),
                      child: Text(
                        controller.description.value,
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.gray,
                          fontFamily: AppFonts.robotoItalic,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    )
                  : SizedBox.shrink()),
              Row(
                children: <Widget>[
                  (controller.enableQuickDraw.value
                      ? Expanded(
                          flex: 1,
                          child: TouchableWidget(
                            onPressed: controller.onPressQuickDraw,
                            height: 50,
                            decoration: BoxDecoration(
                              color: AppColors.warning,
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Text(
                              AppStrings.getString(AppStrings.labelFastWithdraw)!,
                              style: TextStyle(
                                fontSize: 18,
                                color: AppColors.white,
                                fontFamily: AppFonts.robotoMedium,
                              ),
                            )),
                        )
                      : SizedBox.shrink()),
                  (!controller.enableQuickDraw.value ||
                          !controller.enableBtnSendEmail.value
                      ? SizedBox.shrink()
                      : SizedBox(
                          width: 10,
                        )),
                  (controller.enableBtnSendEmail.value
                      ? Expanded(
                          flex: 1,
                          child: TouchableWidget(
                            onPressed: controller.onPressSendReceipt,
                            height: 50,
                            decoration: BoxDecoration(
                              color: AppColors.mainBackground,
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Text(
                              AppStrings.getString(AppStrings.titleGuiBienNhan)!,
                              style: TextStyle(
                                fontSize: 18,
                                color: AppColors.blueText,
                                fontFamily: AppFonts.robotoMedium,
                              ),
                            ),
                          ),
                        )
                      : SizedBox.shrink())
                ],
              ),
              (controller.enableVoidTransaction.value
                  ? TouchableWidget(
                      margin: EdgeInsets.only(top: 10),
                      onPressed: controller.onPressVoidTransaction,
                      height: 50,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        AppStrings.getString(AppStrings.labelCancelTransaction)!,
                        style: TextStyle(
                          fontSize: 18,
                          color: AppColors.white,
                          fontFamily: AppFonts.robotoMedium,
                        ),
                      ),
                    )
                  : SizedBox.shrink()),

              (controller.enableBtnCallSupport.value
                  ? TouchableWidget(
                      onPressed: AppUtils.openCallPhoneSupport,
                      height: 50,
                      margin: EdgeInsets.only(top: 10),
                      decoration: BoxDecoration(
                        color: AppColors.mainBackground,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        '${AppStrings.getString(AppStrings.titleCallSupport)}  1900-63-64-88',
                        style: TextStyle(
                          fontSize: 18,
                          color: AppColors.blueText,
                          fontFamily: AppFonts.robotoMedium,
                        ),
                      ),
                    )
                  : SizedBox.shrink())
            ],
          ),
          SizedBox(height: 10,),
          (MyAppController.isSupportPrinter() && controller.enableBtnPrint.value
              ? TouchableWidget(
                  padding: const EdgeInsets.all(0),
                  height: 50,
                  decoration: BoxDecoration(
                    color: AppColors.primary,
                    borderRadius: BorderRadius.circular(6),
                  ),
                  onPressed: controller.printTransactionReceiptLocalQR,
                  child: Container(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Image.asset(
                          AppImages.ic_print,
                          width: 25,
                          height: 25,
                          color: AppColors.white,
                        ),
                        SizedBox(width: 10),
                        Text(
                          AppStrings.getString(AppStrings.buttonPrint)!,
                          style: buttonTextStyle(color: AppColors.white),
                        ),
                      ],
                    ),
                  ),
                )
              : SizedBox.shrink()),
        ],
      ),
    );
  }

  buildBodySettle() {
    bool isSettle = controller.paramInput!['isNormal'] == false;
    // String amount = isSettle
    //     ? '${NumberFormat("#,###", "vi_VN").format(int.parse((
    //     controller.detailSettleTransaction.value?.amountAuthorized ?? '0')?.replaceAll(',', '')) ?? 0)}đ'
    //     : '${NumberFormat("#,###", "vi_VN").format(controller.detailTransaction.value?.amount ?? 0)}đ';
    String amount = (isSettle
        ? controller.detailSettleTransaction.value.amountAuthorized ?? '0'
        : controller.detailTransaction.value?.amount.toString())!;

    String holderName = (isSettle
        ? controller.detailSettleTransaction.value.cardHolderName ?? 'NO NAME'
        : controller.detailTransaction.value?.cardholderName!)!;
    String cardType = (isSettle
        ? controller.detailSettleTransaction.value.applicationLabel ?? ''
        : controller.detailTransaction.value?.issuerName!)!;
    String? cardNumber = isSettle
        ? controller.detailSettleTransaction.value.maskedPAN ?? ''
        : controller.detailTransaction.value?.pan;
    String authCode = (isSettle
        ? controller.detailSettleTransaction.value.approvalCode ?? ''
        : controller.detailTransaction.value?.authCode!)!;
    String invoiceNumber = isSettle
        ? controller.detailSettleTransaction.value.invoiceNumber ?? ''
        : controller.detailTransaction.value?.trace ?? '';
    String refNo = isSettle
        ? controller.detailSettleTransaction.value.rREFNo ?? ''
        : controller.detailTransaction.value?.rrn ?? '';
    String batchNumber =
        isSettle ? controller.detailSettleTransaction.value.batchNo ?? '' : '';
    String tid = (isSettle
        ? controller.detailSettleTransaction.value.application?.tID ?? ''
        : controller.detailTransaction.value?.tid!)!;
    String mid = (isSettle
        ? controller.detailSettleTransaction.value.application?.mID ?? ''
        : controller.detailTransaction.value?.mid!)!;
    String transactionId = (isSettle
        ? controller.detailSettleTransaction.value.transactionID ?? ''
        : controller.detailTransaction.value?.txid!)!;
    String transactionTime = isSettle
        ? '${controller.detailSettleTransaction.value.transactionTime}, ${DateFormat('dd/MM/yyyy', 'vi_VN').format(DateTime.fromMillisecondsSinceEpoch(controller.detailSettleTransaction.value.transactionDate!, isUtc: true).add(Duration(hours: 7)))}'
        : '${DateFormat('HH:mm, dd/MM/yyyy', 'vi_VN').format(DateTime.fromMillisecondsSinceEpoch(controller.detailTransaction.value?.createdDate ?? DateTime.now().millisecondsSinceEpoch, isUtc: true).add(Duration(hours: 7)))}';
    return Container(
      child: ListView(
        padding: EdgeInsets.all(15),
        children: <Widget>[
          Column(
            children: <Widget>[
              _buildRowItem(AppStrings.getString(AppStrings.labelPaymentMethod) ?? '',
                  controller.paymentMethod.value ?? ''),
              Container(
                margin: EdgeInsets.symmetric(vertical: 3),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Text(
                      '${AppStrings.getString(AppStrings.totalAmount)}:',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.greyTextContent,
                      ),
                    ),
                    // Text(
                    //   amount,
                    //   style: TextStyle(
                    //     fontSize: 20,
                    //     color: AppColors.orangeDark,
                    //     fontFamily: AppFonts.robotoBold,
                    //   ),
                    // ),
                    buildWidgetAmount(amount)
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.symmetric(vertical: 3),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Text(
                      '${AppStrings.getString(AppStrings.labelStatus)}:',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.greyTextContent,
                      ),
                    ),
                    Container(
                      padding:
                          EdgeInsets.symmetric(vertical: 2, horizontal: 15),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(21),
                        color: controller.colorStatus.value,
                      ),
                      child: Text(
                        controller.textStatus.value,
                        style: TextStyle(
                          fontSize: 13,
                          color: AppColors.white,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                color: AppColors.lightGrayOpacity,
                width: Get.width - 30,
                height: 1,
                margin: EdgeInsets.symmetric(vertical: 10),
              ),
              _buildRowItem(AppStrings.getString(AppStrings.labelCardHolderName) ?? '', holderName),
              _buildRowItem(AppStrings.getString(AppStrings.labelCardType) ?? '', cardType),
              (isNullEmpty(controller.detailSettleTransaction.value.maskedPAN)
                  ? SizedBox.shrink()
                  : _buildRowItem(AppStrings.getString(AppStrings.labelCardNumber) ?? '', cardNumber!)),
              Container(
                color: AppColors.lightGrayOpacity,
                width: Get.width - 30,
                height: 1,
                margin: EdgeInsets.symmetric(vertical: 10),
              ),
              _buildRowItem(AppStrings.getString(AppStrings.labelApprovalCode) ?? '', authCode),
              _buildRowItem(AppStrings.getString(AppStrings.labelInvoiceCode) ?? '', invoiceNumber),
              _buildRowItem(AppStrings.getString(AppStrings.labelRefNo) ?? '', refNo),
              Container(
                color: AppColors.lightGrayOpacity,
                width: Get.width - 30,
                height: 1,
                margin: EdgeInsets.symmetric(vertical: 10),
              ),
              _buildRowItem(AppStrings.getString(AppStrings.labelBatchNumber) ?? '', batchNumber),
              _buildRowItem('TID', tid),
              _buildRowItem('MID', mid),
              _buildRowItem(AppStrings.getString(AppStrings.labelTransactionId) ?? '', transactionId),
              _buildRowItem(
                  AppStrings.getString(AppStrings.labelTransactionTime) ?? '', transactionTime),
              SizedBox(
                height: 10,
              ),
              (isNullEmpty(controller.description.value)
                  ? SizedBox.shrink()
                  : Container(
                      margin: EdgeInsets.only(bottom: 10),
                      width: Get.width - 30,
                      padding:
                          EdgeInsets.symmetric(vertical: 10, horizontal: 20),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(6),
                        color: AppColors.gray3,
                      ),
                      child: Text(
                        controller.description.value,
                        style: TextStyle(
                          fontSize: 14,
                          color: AppColors.gray,
                          fontFamily: AppFonts.robotoItalic,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    )),
              (controller.enableBtnContinue.value
                  ? TouchableWidget(
                      onPressed: controller.onPressContinueUnSignPayment,
                      height: 50,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        AppStrings.getString(AppStrings.continueText) ?? '',
                        style: buttonTextStyle(color: AppColors.white),
                      ),
                    )
                  : Row(
                      children: <Widget>[
                        (controller.enableQuickDraw.value
                            ? Expanded(
                                flex: 1,
                                child: TouchableWidget(
                                  onPressed: controller.onPressQuickDraw,
                                  height: 60,
                                  decoration: BoxDecoration(
                                    color: AppColors.warning,
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  child: Text(
                                    AppStrings.getString(AppStrings.labelFastWithdraw) ?? '',
                                    style: (controller.enableQuickDraw.value && controller.enableBtnPrint.value && controller.enableBtnSendEmail.value) ? style_S14_W600_WhiteColor : style_S16_W600_WhiteColor,
                                  ),
                                ),
                              )
                            : SizedBox.shrink()),
                        ((controller.enableQuickDraw.value &&
                                controller.enableBtnPrint.value)
                            ? SizedBox(width: 5)
                            : SizedBox.shrink()),
                        (MyAppController.isSupportPrinter() &&
                                controller.enableBtnPrint.value
                            ? Expanded(
                                flex: 1,
                                child: TouchableWidget(
                                  padding: const EdgeInsets.all(0),
                                  height: 60,
                                  decoration: BoxDecoration(
                                    color: AppColors.primary,
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  onPressed: () =>
                                      controller.printTransactionReceiptLocal(
                                          transactionId),
                                  child: Container(
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Image.asset(
                                          AppImages.ic_print,
                                          width: 25,
                                          height: 25,
                                          color: AppColors.white,
                                        ),
                                        SizedBox(width: 10),
                                        Text(
                                          AppStrings.getString(AppStrings.buttonPrint) ?? '',
                                          style:  (controller.enableQuickDraw.value && controller.enableBtnPrint.value && controller.enableBtnSendEmail.value) ? style_S14_W600_WhiteColor : style_S16_W600_WhiteColor,
                                        )
                                      ],
                                    ),
                                  ),
                                ),
                              )
                            : SizedBox.shrink()),
                        ((controller.enableBtnPrint.value &&
                            controller.enableBtnSendEmail.value)
                            ? SizedBox(width: 5)
                            : SizedBox.shrink()),
                        (MyAppController.isKozenP12orN4() &&
                                controller.enableBtnSendEmail.value)
                            ? Expanded(
                                flex: 1,
                                child: TouchableWidget(
                                  onPressed: controller.onPressSendReceipt,
                                  height: 60,
                                  decoration: BoxDecoration(
                                    color: AppColors.mainBackground,
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  child: Text(
                                    AppStrings.getString(AppStrings.titleGuiBienNhan) ?? '',
                                    style:  (controller.enableQuickDraw.value && controller.enableBtnPrint.value && controller.enableBtnSendEmail.value) ? style_S14_W600_BlueColor : style_S16_W600_BlueColor,
                                    // style: TextStyle(
                                    //   fontSize: 14,
                                    //   fontWeight: FontWeight.w600,
                                    //   color: AppColors.blueText,
                                    //   fontFamily: kFontFamilyBeVietnamPro,
                                    // ),
                                  ),
                                ),
                              )
                            : SizedBox.shrink(),
                      ],
                    )),
              (controller.enableVoidTransaction.value
                  ? TouchableWidget(
                      margin: EdgeInsets.only(top: 10),
                      onPressed: controller.onPressVoidTransaction,
                      padding: const EdgeInsets.all(0),
                      height: 50,
                      decoration: BoxDecoration(
                        color: AppColors.redButton,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        AppStrings.getString(AppStrings.labelCancelTransaction) ?? '',
                        style: TextStyle(
                          fontSize: 18,
                          color: AppColors.white,
                          fontFamily: AppFonts.robotoMedium,
                        ),
                      ),
                    )
                  : SizedBox.shrink()),
              (controller.enableBtnCallSupport.value
                  ? TouchableWidget(
                      onPressed: AppUtils.openCallPhoneSupport,
                      height: 50,
                      margin: EdgeInsets.only(top: 10),
                      decoration: BoxDecoration(
                        color: AppColors.mainBackground,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        '${AppStrings.getString(AppStrings.titleCallSupport)}  1900-63-64-88',
                        style: TextStyle(
                          fontSize: 18,
                          color: AppColors.blueText,
                          fontFamily: AppFonts.robotoMedium,
                        ),
                      ),
                    )
                  : SizedBox.shrink()),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    controller.context = context;
    return WillPopScope(
      onWillPop: _onWillPop,
      child: CommonScreen(
        mainBackgroundColor: AppColors.white,
        header: CommonHeader(
          title: controller.title,
          leftWidget: HeaderButton(
            icon: AppImages.icBack,
            iconWidth: 18,
            iconHeight: 18,
            onPressed: controller.onPressClosePage,
            iconColor: AppColors.blackText,
          ),
          rightWidget: HeaderButton(
            icon: AppImages.ic_quick_payment,
            iconWidth: AppDimens.icon24,
            iconHeight: AppDimens.icon24,
            onPressed: controller.onPressQuickPayment,
          ),
        ),
        child: Stack(
          children: [
            Obx(() => controller.tempPrinterWidget.value),
            Container(
              color: AppColors.white,
              child: Obx(() => !controller.isLayoutNormal.value
                  ? (controller.paramInput!["isNormal"] == true
                      ? (controller.detailTransaction.value!.udid != null
                          ? buildBodySettle()
                          : SizedBox.shrink())
                      : (controller.detailSettleTransaction.value.udid != null
                          ? buildBodySettle()
                          : SizedBox.shrink()))
                  : (controller.detailTransaction.value!.udid != null
                      ? buildBodyNormal()
                      : SizedBox.shrink())),
            ),
          ],
        ),
      ),
    );
  }

  buildWidgetAmount(String amount) {
    String amountShow = amount.replaceAll(new RegExp("\\D"), '');

    return Row(
      children: [
        Text(
          '${NumberFormat("#,###", "vi_VN").format(int.parse(amountShow))}',
          style: TextStyle(
            fontSize: 18,
            color: AppColors.orangeDark,
            fontFamily: AppFonts.robotoBold,
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(bottom: 3),
          child: Text(
            'đ',
            style: TextStyle(
                fontSize: 12,
                color: AppColors.orangeDark,
                fontFamily: AppFonts.robotoBold,
                decoration: TextDecoration.underline),
          ),
        )
      ],
    );
  }
}
