import 'package:flutter/widgets.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/widget/base_bottom_sheet.dart';
import 'package:mposxs/app/ui/widget/common_button.dart';
import 'package:mposxs/app/ui/widget/common_text_field.dart';
import 'package:mposxs/app/util/app_validation.dart';

class BottomSheetSendEmail extends StatefulWidget {
  final Function? onPressConfirmEmail;

  const BottomSheetSendEmail({Key? key, this.onPressConfirmEmail}) : super(key: key);

  @override
  _BottomSheetSendEmailState createState() => _BottomSheetSendEmailState();
}

class _BottomSheetSendEmailState extends State<BottomSheetSendEmail> {
  TextEditingController _textEmailController = TextEditingController();
  String? _errMessageConfirmEmail;

  _onPressCloseBottomSendEmail() {
    Navigator.of(context).pop();
    _textEmailController.text = '';
    setState(() {
      _errMessageConfirmEmail = null;
    });
  }

  _onPressConfirmEmail() {
    String? error = checkEmptyAndValidEmail(context, _textEmailController.text);
    if (error != null) {
      setState(() {
        _errMessageConfirmEmail = error;
      });
    } else if (widget.onPressConfirmEmail != null) {
      widget.onPressConfirmEmail!(_textEmailController.text);
      _onPressCloseBottomSendEmail();
    }
  }

  @override
  Widget build(BuildContext context) {
    return BaseBottomSheet(
      onPressClose: _onPressCloseBottomSendEmail,
      title: AppStrings.getString(AppStrings.labelButtonSendEmail),
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 15, horizontal: 15),
        child: Column(
          children: <Widget>[
            CommonTextField(
              fontSize: 20,
              fontFamily: AppFonts.robotoMedium,
              controller: _textEmailController,
              hintText: AppStrings.getString(AppStrings.labelSendEmail),
              hintTextFontSize: 16,
              errorText: _errMessageConfirmEmail,
              autoFocus: true,
              keyboardType: TextInputType.emailAddress,
            ),
            SizedBox(
              height: 10,
            ),
            CommonButton(
              onPressed: _onPressConfirmEmail,
              color: AppColors.primary,
              textColor: AppColors.white,
              textSize: 18,
              title: AppStrings.getString(AppStrings.buttonConfirm),
              minWidth: MediaQuery.of(context).size.width - 30,
              height: 50,
            )
          ],
        ),
      ),
    );
  }
}
