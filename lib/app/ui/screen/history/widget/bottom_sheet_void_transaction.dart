import 'package:flutter/widgets.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/widget/base_bottom_sheet.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';
import 'package:mposxs/app/util/app_validation.dart';

class BottomSheetVoidTransaction extends StatefulWidget {
  final String? amount;
  final String? cardType;
  final String? cardNumber;
  final String? authCode;
  final String? rrnNo;
  final String? transactionId;
  final Function? onPressConfirmVoid;

  const BottomSheetVoidTransaction({
    Key? key,
    this.amount,
    this.cardType,
    this.cardNumber,
    this.authCode,
    this.rrnNo,
    this.transactionId,
    this.onPressConfirmVoid,
  }) : super(key: key);

  @override
  _BottomSheetVoidTransactionState createState() => _BottomSheetVoidTransactionState();
}

class _BottomSheetVoidTransactionState extends State<BottomSheetVoidTransaction> {
  Widget _buildRowItem(String? label, String value) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 3),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            '$label:',
            style: TextStyle(
              fontSize: 14,
              color: AppColors.greyTextContent,
            ),
          ),
          SizedBox(
            width: 15,
          ),
          Expanded(
            flex: 1,
            child: Text(
              value,
              maxLines: 3,
              textAlign: TextAlign.end,
              style: TextStyle(
                fontSize: 16,
                color: AppColors.darkGrayText,
                fontFamily: AppFonts.robotoMedium,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return BaseBottomSheet(
        onPressClose: () => Navigator.pop(context),
        title: AppStrings.getString(AppStrings.labelConfirmCancelTransaction),
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 15, horizontal: 15),
          child: Column(
            children: <Widget>[
              Container(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    Text(
                      '${AppStrings.getString(AppStrings.totalAmount)}:',
                      style: TextStyle(
                        fontSize: 14,
                        color: AppColors.greyTextContent,
                      ),
                    ),
                    Text(
                      widget.amount!,
                      style: TextStyle(
                        fontSize: 20,
                        color: AppColors.orangeDark,
                        fontFamily: AppFonts.robotoBold,
                      ),
                    ),
                  ],
                ),
              ),
              _buildRowItem(AppStrings.getString(AppStrings.labelCardType), widget.cardType!),
              (isNullEmpty(widget.cardNumber)
                  ? SizedBox.shrink()
                  : _buildRowItem(AppStrings.getString(AppStrings.labelCardNumber), widget.cardNumber!)),
              _buildRowItem(AppStrings.getString(AppStrings.labelApprovalCode), widget.authCode!),
              _buildRowItem(AppStrings.getString(AppStrings.labelInvoiceCode), widget.rrnNo!),
              _buildRowItem(AppStrings.getString(AppStrings.labelTransactionId), widget.transactionId!),
              SizedBox(
                height: 15,
              ),
              Row(
                children: <Widget>[
                  Expanded(
                    flex: 1,
                    child: TouchableWidget(
                      height: 50,
                      decoration: BoxDecoration(
                        color: AppColors.lightBlueBackground,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      onPressed: () => Navigator.pop(context),
                      child: Text(
                        AppStrings.getString(AppStrings.skip)!,
                        style: TextStyle(
                          fontSize: 18,
                          color: AppColors.darkGrayText,
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 10,
                  ),
                  Expanded(
                    flex: 1,
                    child: TouchableWidget(
                      height: 50,
                      onPressed: widget.onPressConfirmVoid,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Text(
                        AppStrings.getString(AppStrings.labelCancelTransaction)!,
                        style: TextStyle(
                          fontSize: 18,
                          color: AppColors.white,
                        ),
                      ),
                    ),
                  ),
                ],
              )
            ],
          ),
        ));
  }
}
