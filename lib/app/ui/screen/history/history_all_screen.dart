import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/controller/history_all_controller.dart';
import 'package:mposxs/app/data/model/transaction_history_model.dart' as thm;
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';
import 'package:mposxs/app/ui/widget/common_header.dart';
import 'package:mposxs/app/ui/widget/common_screen.dart';
import 'package:mposxs/app/ui/widget/header_button.dart';
import 'package:mposxs/app/ui/widget/text_status_trans.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';
import 'package:mposxs/app/util/app_validation.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:sticky_headers/sticky_headers.dart';

class HistoryAllScreen extends GetView<HistoryAllController> {

  Widget _buildStickyHeader(int index) {
    List<thm.Data> _listItem = controller.listGroupData.value[index].data!;
    String _countTransaction = _listItem.length < 10 ? '0${_listItem.length}' : '${_listItem.length}';
    String? _title = controller.listGroupData.value[index].date;
    return Container(
        width: Get.width,
        color: AppColors.lightGrayBackground,
        padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
        child: Text(
          '$_title - $_countTransaction ${_listItem.length>1?AppStrings.getString(AppStrings.transactions) ?? '':AppStrings.getString(AppStrings.transaction) ?? ''}',
          style: TextStyle(
            fontSize: 18,
            color: AppColors.blackText,
            fontFamily: AppFonts.robotoMedium,
          ),
        ));
  }

  Widget _buildItemRowTransaction(thm.Data data) {
    // String textStatus = '${data?.status ?? ''}';
    // Color colorStatus = AppColors.gray;
    // switch (data.status) {
    //   case 100:
    //   case 105:
    //     textStatus = AppStrings.success.tr;
    //     colorStatus = AppColors.green;
    //     break;
    //   case 97:
    //     textStatus = AppStrings.failure.tr;
    //     colorStatus = AppColors.redText2;
    //     break;
    //   case 102:
    //     textStatus = AppStrings.canceled.tr;
    //     colorStatus = AppColors.redText2;
    //     break;
    //   case 104:
    //     textStatus = AppStrings.settled.tr;
    //     colorStatus = AppColors.blue;
    //     break;
    //   case 99:
    //     textStatus = AppStrings.refunded.tr;
    //     colorStatus = AppColors.tabUnSelected;
    //     break;
    //   case 98:
    //     textStatus = AppStrings.processing.tr;
    //     colorStatus = AppColors.orangeDark;
    //     break;
    // }
    bool isScanPayment = true;
    if (!isNullEmpty(data.accquirer) && data.accquirer!.toUpperCase().startsWith('MPQR')) {
      isScanPayment = false;
    }
    return TextButton(
      // highlightColor: Color.fromRGBO(204, 223, 242, 0.2),
      // splashColor: Color.fromRGBO(204, 223, 242, 0.4),
      // padding: EdgeInsets.symmetric(horizontal: 10, vertical: 3),
      onPressed: () => controller.onPressItem(data),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12),
        decoration: BoxDecoration(color: AppColors.white, borderRadius: BorderRadius.circular(6), boxShadow: [
          BoxShadow(
            color: Color.fromRGBO(204, 223, 242, 0.17),
            offset: Offset(0, 8),
            blurRadius: 25,
          )
        ]),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Expanded(
              flex: 1,
              child: Container(
                padding: const EdgeInsets.only(top: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: <Widget>[
                        Expanded(
                          flex: 1,
                          child: Text(
                            data.pan!,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                            style: TextStyle(
                              fontSize: 16,
                              color: AppColors.darkGrayText,
                              fontFamily: AppFonts.robotoMedium,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 5,
                        ),
                        Row(
                          children: [
                            Text(
                              '${NumberFormat("#,###", "vi_VN").format(data.amount ?? 0)}',
                              style: TextStyle(
                                fontSize: 18,
                                color: AppColors.blackText,
                                fontFamily: AppFonts.robotoMedium,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(bottom: 3),
                              child: Text(
                                'đ',
                                style: TextStyle(
                                    fontSize: 12,
                                    color: AppColors.blackText,
                                    fontFamily: AppFonts.robotoMedium,
                                    decoration: TextDecoration.underline),
                              ),
                            )
                          ],
                        ),
                      ],
                    ),
                    SizedBox(height: 5),
                    Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: <Widget>[
                      Text(
                        data.issuerName!,
                        style: TextStyle(
                          fontSize: 16,
                          fontFamily: AppFonts.robotoMedium,
                          color: AppColors.greyTextContent,
                        ),
                      ),
                      TextStatusTrans(status: data.status)
                      // Container(
                      //   padding: EdgeInsets.symmetric(horizontal: 15, vertical: 3),
                      //   decoration: BoxDecoration(
                      //     color: colorStatus,
                      //     borderRadius: BorderRadius.circular(21),
                      //   ),
                      //   child: Text(
                      //     textStatus,
                      //     style: TextStyle(
                      //       fontSize: 13,
                      //       color: AppColors.white,
                      //     ),
                      //   ),
                      // )
                    ]),
                    Row(mainAxisSize: MainAxisSize.max, children: [
                      Expanded(
                        flex: 1,
                        child: Container(
                          height: 1,
                          color: AppColors.lightGrayOpacity,
                          margin: const EdgeInsets.only(top: 10),
                        ),
                      )
                    ]),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: <Widget>[
                        Expanded(
                          flex: 1,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            child: Text(
                              '${DateFormat('HH:mm, dd/MM/yyyy', 'vi_VN').format(
                                  DateTime.fromMillisecondsSinceEpoch(data.createdDate!, isUtc: true).add(Duration(hours: 7)))}',
                              style: TextStyle(
                                fontSize: 14,
                                color: AppColors.greyTextContent,
                              ),
                            ),
                          ),
                        ),
                        (MyAppController.isSupportPrinter() && (data.status == 100 || data.status == 104 || data.status == 105 || data.status == 102)
                            ? TouchableWidget(
                                padding: const EdgeInsets.symmetric(vertical: 10),
                                onPressed: isScanPayment == true
                                    ? () => controller.printTransactionReceiptLocal(data.txid!, data.status == 102)
                                    : () => controller.printTransactionReceiptLocalQR(data),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Image.asset(AppImages.ic_print, width: 15, height: 15, color: AppColors.primary),
                                    SizedBox(width: 5),
                                    Text(
                                      AppStrings.getString(AppStrings.buttonPrint) ?? '',
                                      style: TextStyle(
                                        color: AppColors.primary,
                                        fontSize: 14,
                                        fontFamily: AppFonts.robotoRegular,
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            : SizedBox.shrink()),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRowTransactionList(int index) {
    return Column(children: controller.listGroupData.value[index].data!.map(_buildItemRowTransaction).toList());
  }

  Widget _buildTransactionList() {
    return Column(
      children: <Widget>[
        Expanded(
          flex: 1,
          child: SmartRefresher(
            controller: controller.refreshController,
            enablePullUp: true,
            child: ListView.builder(
              padding: EdgeInsets.only(top: 0),
              itemCount: controller.listGroupData.value.length,
              itemBuilder: (context, index) {
                return StickyHeader(
                  header: _buildStickyHeader(index),
                  content: _buildRowTransactionList(index),
                );
              },
            ),
            header: WaterDropHeader(
              refresh: CupertinoActivityIndicator(),
              complete: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: <Widget>[
                  const Icon(
                    Icons.done,
                    color: Colors.grey,
                  ),
                ],
              ),
            ),
            onLoading: controller.onLoadMoreData,
            onRefresh: controller.onRefreshData,
            footer: CustomFooter(
              builder: (BuildContext context, LoadStatus? mode) {
                Widget body;
                if (mode == LoadStatus.idle) {
                  body = Text(AppStrings.getString(AppStrings.slPullUp) ?? '', style: TextStyle(fontSize: 14, color: Colors.grey));
                } else if (mode == LoadStatus.loading) {
                  body = CupertinoActivityIndicator();
                } else if (mode == LoadStatus.failed) {
                  body = Text(AppStrings.getString(AppStrings.slFailed) ?? '', style: TextStyle(fontSize: 14, color: Colors.grey));
                } else if (mode == LoadStatus.canLoading) {
                  body = Text(AppStrings.getString(AppStrings.slCan) ?? '', style: TextStyle(fontSize: 14, color: Colors.grey));
                } else {
                  body = Text(AppStrings.getString(AppStrings.slEmpty) ?? '', style: TextStyle(fontSize: 14, color: Colors.grey));
                }
                return Container(
                  height: 55.0,
                  child: Center(child: body),
                );
              },
            ),
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    controller.context = context;
    controller.refreshController = RefreshController();
    return CommonScreen(
      mainBackgroundColor: AppColors.lightGrayBackground,
      header: CommonHeader(
        title: AppStrings.getString(AppStrings.titleAllTransaction) ?? '',
        rightWidget: HeaderButton(
          iconHeight: AppDimens.icon24,
          iconWidth: AppDimens.icon24,
          icon: AppImages.ic_quick_payment,
          onPressed: controller.onPressQuickPayment,
        ),
      ),
      child: Stack(
        children: [
          Obx(() => controller.tempPrinterWidget.value),
          Container(
            color: AppColors.lightGrayBackground,
            child: Obx(() => controller.listGroupData.value.length > 0
                ? _buildTransactionList()
                : Center(child: CircularProgressIndicator())),
          ),
        ],
      ),
    );
  }
}
