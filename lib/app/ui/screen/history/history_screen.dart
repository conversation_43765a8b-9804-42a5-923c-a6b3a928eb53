import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/controller/history_controller.dart';
import 'package:mposxs/app/data/model/settle_transaction_history_model.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/widget/common_screen.dart';
import 'package:mposxs/app/ui/widget/text_status_trans.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

class HistoryScreen extends GetView<HistoryController> {
  final Function? doLogout;
  final Function? onPressQuickPayment;

  HistoryScreen({this.doLogout, this.onPressQuickPayment});

  Widget _buildSettleItemRowTransaction(PaymentItems data) {
    return TextButton(
      // highlightColor: Color.fromRGBO(204, 223, 242, 0.2),
      // splashColor: Color.fromRGBO(204, 223, 242, 0.4),
      // padding: EdgeInsets.symmetric(horizontal: 10, vertical: 3),
      onPressed: () => controller.onPressSettleItemTransaction(data),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12),
        decoration: BoxDecoration(color: AppColors.white, borderRadius: BorderRadius.circular(6), boxShadow: [
          BoxShadow(
            color: Color.fromRGBO(204, 223, 242, 0.17),
            offset: Offset(0, 8),
            blurRadius: 25,
          )
        ]),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Expanded(
              flex: 1,
              child: Container(
                padding: const EdgeInsets.only(top: 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Row(
                      children: <Widget>[
                        // => card number
                        Expanded(
                          flex: 1,
                          child: Text(
                            data.mNumber ?? '',
                            style: TextStyle(
                              fontSize: 16,
                              color: AppColors.darkGrayText,
                              fontFamily: AppFonts.robotoMedium,
                            ),
                          ),
                        ),
                        // => amount
                        Row(
                          children: [
                            Text(
                              '${NumberFormat("#,###", "vi_VN").format(int.parse(data.mAmount!.replaceAll(',', '')) ?? 0)}',
                              style: TextStyle(
                                fontSize: 18,
                                color: AppColors.blackText,
                                fontFamily: AppFonts.robotoMedium,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(bottom: 3),
                              child: Text(
                                'đ',
                                style: TextStyle(
                                    fontSize: 12,
                                    color: AppColors.blackText,
                                    fontFamily: AppFonts.robotoMedium,
                                    decoration: TextDecoration.underline),
                              ),
                            )
                          ],
                        ),
                      ],
                    ),
                    SizedBox(height: 5),
                    Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: <Widget>[
                      Expanded(
                        child: Text(
                          data.mName ?? '',
                          style: TextStyle(
                            fontSize: 16,
                            color: AppColors.greyTextContent,
                            fontFamily: AppFonts.robotoMedium,
                          ),
                        ),
                      ),
                      TextStatusTrans(status: data.mStatus)
                      // Container(
                      //   padding: EdgeInsets.symmetric(horizontal: 15, vertical: 3),
                      //   decoration: BoxDecoration(
                      //     color: colorStatus,
                      //     borderRadius: BorderRadius.circular(21),
                      //   ),
                      //   child: Text(
                      //     textStatus,
                      //     style: TextStyle(
                      //       fontSize: 13,
                      //       color: AppColors.white,
                      //     ),
                      //   ),
                      // )
                    ]),
                    Row(mainAxisSize: MainAxisSize.max, children: [
                      Expanded(
                        flex: 1,
                        child: Container(
                          height: 1,
                          color: AppColors.lightGrayOpacity,
                          margin: const EdgeInsets.only(top: 10),
                        ),
                      )
                    ]),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: <Widget>[
                        Expanded(
                          flex: 1,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            child: Text(
                              '${data.mTime}, ${data.mDate}',
                              style: TextStyle(
                                fontSize: 14,
                                color: AppColors.greyTextContent,
                              ),
                            ),
                          ),
                        ),
                        ( MyAppController.isSupportPrinter() && (data.mStatus == 100 || data.mStatus == 104 || data.mStatus == 105 ||
                            data.mStatus == 102)
                            ? TouchableWidget(
                                padding: const EdgeInsets.symmetric(vertical: 10),
                                onPressed: () =>
                                    controller.printTransactionReceiptLocal(data.mId!, data.mStatus == 102),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Image.asset(AppImages.ic_print, width: 15, height: 15, color: AppColors.primary),
                                    SizedBox(width: 5),
                                    Text(
                                      AppStrings.getString(AppStrings.buttonPrint) ?? '',
                                      style: TextStyle(
                                        color: AppColors.primary,
                                        fontSize: 14,
                                        fontFamily: AppFonts.robotoRegular,
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            : SizedBox.shrink()),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionListWithDevice() {
    return Expanded(
      flex: 1,
      child: SmartRefresher(
        controller: controller.refreshController,
        child: ListView.builder(
          padding: EdgeInsets.only(top: 0, bottom: 20),
          itemCount: controller.listSettleTransaction.value.length,
          itemBuilder: (context, index) {
            return _buildSettleItemRowTransaction(controller.listSettleTransaction.value[index]);
          },
        ),
        header: WaterDropHeader(
          refresh: CupertinoActivityIndicator(),
          complete: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              const Icon(
                Icons.done,
                color: Colors.grey,
              ),
            ],
          ),
        ),
        onRefresh: () => controller.getSettleTransactionHistory(true),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    controller.context = context;
    controller.doLogout = doLogout;
    controller.onPressQuickPayment = onPressQuickPayment;
    controller.refreshController = RefreshController();
    return CommonScreen(
      mainBackgroundColor: AppColors.lightGrayBackground,
      child: Stack(
        children: [
          Obx(() => controller.tempPrinterWidget.value),
          Container(
            color: AppColors.lightGrayBackground,
            child: Column(
              children: <Widget>[
                Container(
                  padding: EdgeInsets.only(bottom: 8, top: 12),
                  margin: EdgeInsets.symmetric(horizontal: 15),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: <Widget>[
                      Obx(() => Material(
                            borderRadius: BorderRadius.circular(6),
                            color: controller.enableBtnSettle.value ? AppColors.primary : AppColors.tabUnSelected,
                            child: InkWell(
                              onTap: controller.enableBtnSettle.value ? controller.onPressSettled : null,
                              child: Container(
                                padding: EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Row(
                                  children: <Widget>[
                                    Text(
                                      '${AppStrings.getString(AppStrings.settlement)} ${controller.lengthListCanSettle.value} ${AppStrings.getString(AppStrings.transaction)}',
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: AppColors.white,
                                      ),
                                    ),
                                    SizedBox(
                                      width: 10,
                                    ),
                                    Image.asset(
                                      AppImages.icArrowRight,
                                      width: 8,
                                      height: 12,
                                      fit: BoxFit.contain,
                                      color: AppColors.white,
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          )),
                      InkWell(
                        onTap: controller.onPressSeeAllTransaction,
                        child: Padding(
                          padding: const EdgeInsets.only(left: 10, top: 5, bottom: 5),
                          child: Text(
                            AppStrings.getString(AppStrings.seeAllTransaction) ?? '',
                            style: TextStyle(
                              fontSize: 14,
                              color: AppColors.primary,
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                ),
                Obx(() => _buildTransactionListWithDevice()),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
