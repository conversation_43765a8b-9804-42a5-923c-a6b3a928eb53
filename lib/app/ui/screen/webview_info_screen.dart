import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/webview_info_controller.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_common_style.dart';
import 'package:mposxs/app/ui/widget/common_screen.dart';
import 'package:mposxs/app/ui/widget/header_button.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../widget/common_header.dart';

class WebViewInfoScreen extends GetView<WebViewInfoController> {
  @override
  Widget build(BuildContext context) {
    double heightHeaderAdd = MediaQuery.of(context).padding.top > 24 ? 15 : 0;
    double heightHeader =
        (MediaQuery.of(context).size.width / 15) * 3.2 + heightHeaderAdd;
    double heightHeaderContent =
        heightHeader - MediaQuery.of(context).padding.top;
    return CommonScreen(
      header: CommonHeader(
        title: controller.webViewInfoArguments?.title ?? 'mPos.vn',
        leftWidget: HeaderButton(
          icon: AppImages.icBack,
          iconWidth: 18,
          iconHeight: 18,
          onPressed: () => Get.back(),
          iconColor: AppColors.blackText,
        ),
        rightWidget: Obx(() => controller.isLoadingWeb.value
            ? Center(
                child: Container(
                    width: 18,
                    height: 18,
                    child: CircularProgressIndicator(
                        valueColor:
                            AlwaysStoppedAnimation<Color>(AppColors.white),
                        strokeWidth: 2)))
            : SizedBox.shrink()),
      ),
      child: Container(
        child: Column(
          children: <Widget>[
            Expanded(
              flex: 1,
              child: WebViewWidget(
                controller: controller.webViewController,
              ),
            ),
            (controller.webViewInfoArguments?.showControl == true
                ? Obx(() => Container(
                      padding: EdgeInsets.only(
                          top: 5,
                          left: 10,
                          right: 10,
                          bottom: 5 +
                              MediaQuery.of(context).padding.bottom),
                      decoration: bottomDecoration(),
                      child: Row(
                        children: <Widget>[
                          Expanded(
                            flex: 1,
                            child: TouchableWidget(
                              onPressed: controller.allowBack.value
                                  ? controller.onPressWVBack
                                  : null,
                              padding: EdgeInsets.all(0),
                              child: Container(
                                  height: 40,
                                  child: Icon(
                                    Icons.arrow_back,
                                    color: controller.allowBack.value
                                        ? AppColors.blue
                                        : AppColors.greyTextContent,
                                  )),
                            ),
                          ),
                          Expanded(
                            flex: 1,
                            child: TouchableWidget(
                              onPressed: controller.onPressWVReload,
                              padding: EdgeInsets.all(0),
                              child: Container(
                                  height: 40,
                                  child: Icon(
                                    Icons.refresh,
                                    color: AppColors.blue,
                                  )),
                            ),
                          ),
                          Expanded(
                            flex: 1,
                            child: TouchableWidget(
                              onPressed: controller.allowNext.value
                                  ? controller.onPressWVNext
                                  : null,
                              padding: EdgeInsets.all(0),
                              child: Container(
                                  height: 40,
                                  child: Icon(
                                    Icons.arrow_forward,
                                    color: controller.allowNext.value
                                        ? AppColors.blue
                                        : AppColors.greyTextContent,
                                  )),
                            ),
                          ),
                        ],
                      ),
                    ))
                : SizedBox.shrink()),
          ],
        ),
      ),
    );
  }
}
