import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/splash_controller.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/widget/common_screen.dart';

import '../theme/app_dimens.dart';

class SplashScreen extends GetView<SplashController> {
  @override
  Widget build(BuildContext context) {
    return CommonScreen(
      mainBackgroundColor: AppColors.white,
      child: Center(
        child: buildSplash(),
        // child: CircularProgressIndicator(),
      ),
    );
  }

  Container buildSplash() {
    return Container(
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Image.asset(AppImages.logoMpos, width: 200,),
            SizedBox(height: AppDimens.spaceMedium,),
            Text(
              AppStrings.getString(AppStrings.splashSlogan) ?? '',
              style: TextStyle(
                fontStyle: FontStyle.italic,
                color: AppColors.blackText1,
                fontSize: AppDimens.textSizeLarge,
                fontFamily: AppFonts.robotoLight,
              ),
            ),
            SizedBox(height: AppDimens.spaceMedium,),
            CircularProgressIndicator()
          ],
        ),
      ),
    );
  }
}
