import 'package:flutter/cupertino.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';

class TextStatusTrans extends StatelessWidget {

  final int? status;

  const TextStatusTrans({Key? key, this.status}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // return Container();
    String textStatus = '${status ?? ''}';
    Color colorStatus = AppColors.gray;
    switch (status) {
      case 100:
      case 105:
        textStatus = AppStrings.getString(AppStrings.success) ?? '';
        colorStatus = AppColors.green;
        break;
      case 97:
        textStatus = AppStrings.getString(AppStrings.failure) ?? '';
        colorStatus = AppColors.redText2;
        break;
      case 98:
        textStatus = AppStrings.getString(AppStrings.processing) ?? '';
        colorStatus = AppColors.orangeDark;
        break;
      case 99:
        textStatus = AppStrings.getString(AppStrings.refunded) ?? '';
        colorStatus = AppColors.tabUnSelected;
        break;
      case 101:
        textStatus = AppStrings.getString(AppStrings.reversal) ?? '';
        colorStatus = AppColors.redText2;
        break;
      case 102:
        textStatus = AppStrings.getString(AppStrings.canceled) ?? '';
        colorStatus = AppColors.redText2;
        break;
      case 103:
        textStatus = AppStrings.getString(AppStrings.labelPendingSignature) ?? '';
        colorStatus = AppColors.orangeDark;
        break;
      case 104:
        textStatus = AppStrings.getString(AppStrings.settled) ?? '';
        colorStatus = AppColors.blue;
        break;

    }
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 15, vertical: 3),
      decoration: BoxDecoration(
        color: colorStatus,
        borderRadius: BorderRadius.circular(21),
      ),
      child: Text(
        textStatus,
        style: TextStyle(
          fontSize: 13,
          color: AppColors.white,
        ),
      ),
    );
  }
}
