import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/util/app_validation.dart';

class CommonImageNetwork extends StatelessWidget {
  final String? url;
  final Widget? placeholder;
  final Widget? errorWidget;
  final BoxFit? fit;
  final double? width;
  final double? height;
  final double? loadingSize;

  CommonImageNetwork({
    required this.url,
    this.placeholder,
    this.errorWidget,
    this.fit,
    this.width,
    this.height,
    this.loadingSize,
  });

  @override
  Widget build(BuildContext context) {
    return isNullEmpty(url)
        ? Icon(Icons.error)
        : CachedNetworkImage(
            width: width,
            height: height,
            imageUrl: url!,
            placeholder: placeholder as Widget Function(BuildContext, String)? ??
                ((context, url) => Center(
                        child: Container(
                      width: loadingSize ?? 20,
                      height: loadingSize ?? 20,
                      child: CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                        strokeWidth: 2,
                      ),
                    ))),
            errorWidget: (context, url, error) {
              return errorWidget ?? Icon(Icons.error);
            },
            fit: fit ?? BoxFit.contain,
            cacheManager: CacheManager(Config('ImageCacheKey', stalePeriod: Duration(days: 1))),
          );
  }
}
