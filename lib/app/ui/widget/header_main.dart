import 'package:cashiermodule/Utilities/configuration.dart';
import 'package:cashiermodule/constants/style.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/controller/main_controller.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';
import 'package:mposxs/app/util/app_utils.dart';

class HeaderMain extends StatelessWidget {
  Function? onPressLeftIcon;
  Widget? icTitle;
  String? pathLeftIcon;
  String? title;
  Widget? icRight;
  Widget? wTitle;
  Color? bgColor;
  int? currentScreen;

  HeaderMain(
      {Key? key,
      this.bgColor,
      this.wTitle,
      this.onPressLeftIcon,
      this.icTitle,
      this.pathLeftIcon,
      this.title,
      this.icRight,
      this.currentScreen})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    double heightHeader = (Get.width / 15) * 3.2;
    double heightHeaderContent =
        (Get.width / 15) * 3.2 - MediaQuery.of(context).padding.top;
    return Container(
      width: MediaQuery.of(context).size.width,
      height: MyAppController.isKozenP12orN4() ? 90 : heightHeader,
      alignment: Alignment.bottomCenter,
      color: bgColor ?? AppColors.white,
      child: Stack(children: <Widget>[
        Container(
          width: MediaQuery.of(context).size.width,
          height: MyAppController.isKozenP12orN4()
              ? 80
              : (heightHeader - MediaQuery.of(context).padding.top),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Container(
                width: MediaQuery.of(context).size.width,
                height: MyAppController.isKozenP12orN4() ? 80 : heightHeaderContent,
                color: bgColor ?? AppColors.white,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: <Widget>[
                    GestureDetector(
                        onTap: () => onPressLeftIcon!(),
                        // onTap: () => _drawerKey.currentState.openDrawer(),
                        child: Image.asset(
                          pathLeftIcon!,
                          alignment: Alignment.centerLeft,
                          fit: BoxFit.fitWidth,
                          width: 70,
                          height: 70
                          // height: AppDimens.icon60,
                        )),
                    SizedBox(width: 40,),
                    Expanded(
                        flex: 6,
                        child: wTitle ??
                            Center(
                              child: ((currentScreen ==
                                      MainController
                                          .MAIN_SCREEN_P12_ENTER_AMOUNT)
                                  ? _buildAmount(title)
                                  : Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.center,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      Container(
                                        child: Text(
                                          title ?? '',
                                          textAlign: TextAlign.center,
                                          style: TextStyle(
                                            fontSize:
                                                AppDimens.textSizeLarge,
                                            color: (currentScreen ==
                                                    MainController
                                                        .MAIN_SCREEN_P12_QR)
                                                ? Configuration.whiteColor
                                                : Configuration
                                                    .blackMainColour,
                                            fontFamily:
                                                AppFonts.robotoMedium,
                                          ),
                                        ),
                                      ),
                                      (currentScreen ==
                                              MainController
                                                  .MAIN_SCREEN_P12_QR)
                                          ? (icTitle ?? SizedBox.shrink())
                                          : SizedBox.shrink(),
                                    ],
                                  )),
                            )),
                    Container(
                      width: 116,
                      alignment: Alignment.centerRight,
                      child: icRight ?? SizedBox.shrink(),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ]),
    );
  }

  Widget _buildAmount(String? amount) {
    return FittedBox(
      fit: BoxFit.scaleDown,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Text(
            amount != null && amount.length > 0
                ? AppUtils.formatCurrency(int.parse(amount))
                : '',
            style: TextStyle(
                fontSize: 46,
                fontWeight: FontWeight.w500,
                color: AppColors.blackText,
                fontFamily: kFontFamilyBeVietnamPro),
          ),
          Padding(
            padding: const EdgeInsets.only(bottom: 20, left: 2),
            child: Text(
              'đ',
              style: TextStyle(
                  fontFamily: kFontFamilyBeVietnamPro,
                  fontSize: 20,
                  color: AppColors.blackText.withOpacity(0.91),
                  decoration: TextDecoration.underline),
            ),
          ),
        ],
      ),
    );
  }
}
