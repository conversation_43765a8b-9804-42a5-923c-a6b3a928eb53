import 'package:cashiermodule/constants/style.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:mpos_module_base/mpos_module_base_widget.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';

enum MPTextFieldSize { small, big, medium }

enum MPTextFieldStyle { normal, error, warning, success }

enum MPTextFieldHintType { label, hint, both }

class MPTextField extends StatefulWidget {
  final TextStyle? textStyleInput;
  final TextStyle? textStyleHint;
  final TextStyle? textStyleLabel;
  final MPTextFieldSize? mpTextFieldSize;
  final MPTextFieldStyle? mpTextFieldStyle;
  final MPTextFieldHintType? mpTextFieldHintType;
  final Widget? leftWidget;
  final Widget? rightWidget;
  final bool? obscureText;
  final bool? disabled;
  final TextEditingController? controller;
  final Function()? onPressed;
  final Function(String)? onChanged; // for special case
  final Function(String)? onSubmitted;
  final EdgeInsetsGeometry? paddingContainer;
  final EdgeInsetsGeometry? contentPadding;
  final bool? showClearButton;
  final bool? autofocus;
  final String? hintText;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final int? maxLength;
  final int? maxLines;
  final FocusNode? focusNode;
  final Color? backgroundColor;
  final Color? colorBorder;
  final String? error;
  final String? assetClearText;
  final Function()? onClearText;
  final double? height;
  final List<TextInputFormatter>? inputFormatters;

  const MPTextField({
    Key? key,
    this.textStyleInput,
    this.textStyleHint,
    this.textStyleLabel,
    this.mpTextFieldSize,
    this.leftWidget,
    this.rightWidget,
    this.mpTextFieldStyle,
    this.obscureText,
    this.disabled,
    required this.controller,
    this.onPressed,
    this.paddingContainer,
    this.hintText,
    this.keyboardType,
    this.onChanged,
    this.maxLength,
    this.maxLines,
    this.showClearButton,
    this.autofocus,
    this.textInputAction,
    this.focusNode,
    this.onSubmitted,
    this.contentPadding,
    this.backgroundColor,
    this.colorBorder,
    this.assetClearText,
    this.onClearText,
    this.error,
    this.mpTextFieldHintType,
    this.height,
    this.inputFormatters,
  }) : super(key: key);

  @override
  State<StatefulWidget> createState() {
    return MPTextFieldState();
  }
}

class MPTextFieldState extends State<MPTextField> {
  late FocusNode _focus;
  bool _isFocus = false;
  bool _showClear = false;

  @override
  void initState() {
    super.initState();
    _focus = widget.focusNode ?? FocusNode();
    _focus.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _focus.removeListener(_onFocusChange);

    _focus.dispose();

    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocus = _focus.hasFocus;
    });
  }

  onChanged(String text) {
    if (widget.onChanged != null) {
      widget.onChanged!(text);
    }
    if (text.isNotEmpty && _showClear == false) {
      setState(() {
        _showClear = true;
      });
    } else if (text.isEmpty && _showClear == true) {
      setState(() {
        _showClear = false;
        if (widget.onClearText != null) {
          widget.onClearText!();
        }
      });
    }
  }

  Widget _buildButtonClear() {
    return MPButton(
      onPressed: () {
        widget.controller!.clear();
        onChanged('');
      },
      mpButtonStyle: MPButtonStyle.invertStyle,
      backgroundColor: Colors.transparent,
      rippleColor: AppColors.primary,
      width: 40.0.sp,
      padding: EdgeInsets.zero,
      leftWidget: (widget.assetClearText ?? '').isNotEmpty
          ? MPImageWidget.asset(
        path: widget.assetClearText!,
        width: 20.0,
        height: 20.0,
      )
          : Icon(
        Icons.clear,
        color: AppColors.blackText,
        size: 20.0.sp,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    double height = 56.0.sp;
    double textSize = 16.sp;
    if (widget.mpTextFieldSize == MPTextFieldSize.medium) {
      height = 48.0.sp;
      textSize = 16.sp;
    } else if (widget.mpTextFieldSize == MPTextFieldSize.small) {
      height = 36.0.sp;
      textSize = 14.sp;
    }

    Color colorBackground = AppColors.white;
    Color colorBorder = AppColors.blackText;
    Color colorBackgroundFocus = AppColors.white;
    Color colorBorderFocus = AppColors.primary;

    MPTextFieldStyle? mpTextFieldStyle =
    (widget.error ?? '').isNotEmpty ? MPTextFieldStyle.error : widget.mpTextFieldStyle;

    switch (mpTextFieldStyle) {
      case MPTextFieldStyle.normal:
        colorBackground = AppColors.blackText;
        colorBorder = AppColors.blackText;
        colorBackgroundFocus = AppColors.white;
        colorBorderFocus = AppColors.primary;
        break;
      case MPTextFieldStyle.error:
        colorBackground = AppColors.white;
        colorBorder = AppColors.blackText;
        colorBackgroundFocus = AppColors.white;
        colorBorderFocus = AppColors.red;
        break;
      case MPTextFieldStyle.warning:
        colorBackground = AppColors.white;
        colorBorder = AppColors.blackText;
        colorBackgroundFocus = AppColors.white;
        colorBorderFocus = AppColors.orange;
        break;
      case MPTextFieldStyle.success:
        colorBackground = AppColors.white;
        colorBorder = AppColors.blackText;
        colorBackgroundFocus = AppColors.white;
        colorBorderFocus = AppColors.green;
        break;
      default:
        colorBackground = AppColors.white;
        colorBorder = AppColors.blackText;
        colorBackgroundFocus = AppColors.white;
        colorBorderFocus = AppColors.primary;
        break;
    }

    if (widget.disabled == true) {
      colorBackground = AppColors.blackText;
      colorBorder = AppColors.blackText;
      colorBackgroundFocus = AppColors.blackText;
      colorBorderFocus = Colors.transparent;
    }

    Widget? right = !(widget.showClearButton == true && _showClear) && widget.rightWidget == null
        ? null
        : Row(
      children: [
        widget.showClearButton == true && _showClear ? _buildButtonClear() : const SizedBox.shrink(),
        widget.rightWidget ?? const SizedBox.shrink(),
      ],
    );

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Stack(
          children: [
            Container(
              height: widget.height ?? height,
              padding: widget.paddingContainer ??
                  EdgeInsets.only(
                    left: widget.leftWidget != null ? 0.0.sp : 16.0.sp,
                    right: right != null || (widget.showClearButton == true && _showClear) ? 0.0.sp : 16.0.sp,
                  ),
              decoration: BoxDecoration(
                color: _isFocus ? colorBackgroundFocus : (widget.backgroundColor ?? colorBackground),
                borderRadius: BorderRadius.circular(8.0.sp),
                border: Border.all(
                    color: _isFocus ? (widget.colorBorder ?? colorBorderFocus) : (widget.colorBorder ?? colorBorder),
                    width: 1.0),
              ),
              child: Row(
                children: [
                  widget.leftWidget ?? const SizedBox.shrink(),
                  Expanded(
                    flex: 1,
                    child: TextField(
                      focusNode: _focus,
                      controller: widget.controller,
                      onSubmitted: widget.onSubmitted,
                      maxLines: widget.maxLines ?? 1,
                      style: widget.textStyleInput ??
                          TextStyle(
                            color: AppColors.black,
                            fontSize: textSize,
                            fontWeight: FontWeight.w400,
                            fontFamily: kFontFamilyBeVietnamPro
                          ),
                      enabled: widget.disabled != true,
                      decoration: InputDecoration(
                        labelText:
                        widget.mpTextFieldHintType == MPTextFieldHintType.hint ? null : widget.hintText ?? '',
                        labelStyle: widget.textStyleLabel ??
                            TextStyle(
                              color: AppColors.black,
                              fontSize: textSize,
                              fontWeight: FontWeight.w400,
                                fontFamily: kFontFamilyBeVietnamPro
                            ),
                        hintText: widget.mpTextFieldHintType == MPTextFieldHintType.hint ||
                            widget.mpTextFieldHintType == MPTextFieldHintType.both
                            ? (widget.hintText ?? '')
                            : null,
                        hintStyle: widget.textStyleHint ??
                            TextStyle(
                              color: AppColors.black,
                              fontSize: textSize,
                              fontWeight: FontWeight.w400,
                                fontFamily: kFontFamilyBeVietnamPro
                            ),
                        border: InputBorder.none,
                        contentPadding: widget.contentPadding,
                      ),
                      obscureText: widget.obscureText ?? false,
                      keyboardType: widget.keyboardType,
                      textInputAction: widget.textInputAction ?? TextInputAction.done,
                      onChanged: onChanged,
                      inputFormatters: [
                        LengthLimitingTextInputFormatter(widget.maxLength ?? 250),
                        ...(widget.inputFormatters ?? [])
                      ],
                      autofocus: widget.autofocus ?? false,
                    ),
                  ),
                  right ?? const SizedBox.shrink(),
                ],
              ),
            ),
            widget.onPressed != null
                ? Positioned.fill(
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  highlightColor: AppColors.primary.withOpacity(0.1),
                  splashColor: AppColors.primary.withOpacity(0.1),
                  customBorder: RoundedRectangleBorder(borderRadius: BorderRadius.all(Radius.circular(8.0.sp))),
                  onTap: widget.onPressed,
                ),
              ),
            )
                : const SizedBox.shrink(),
          ],
        ),
        (widget.error ?? '').isEmpty
            ? const SizedBox.shrink()
            : Padding(
          padding: EdgeInsets.only(top: 4.0.sp, left: 18.0.sp),
          child: Text(
            widget.error!,
            style: TextStyle(
              color: AppColors.red,
              fontSize: 12.0.sp,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
      ],
    );
  }
}
