import 'package:cashiermodule/constants/style.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_common_style.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';
import 'package:mposxs/app/util/app_validation.dart';

class DialogAlert extends StatelessWidget {
  final String? title, description, description2, text1stButton, text2ndButton, image;
  final Function? onPress1stButton, onPress2ndButton;
  final bool? isTwoButton;
  final bool? isBtnClose;
  final TextAlign? descriptionTextAlign;
  final Widget? widgetDescription;

  DialogAlert({
    this.title,
    this.description,
    this.description2,
    this.text1stButton,
    this.text2ndButton,
    this.onPress1stButton,
    this.onPress2ndButton,
    this.isTwoButton,
    this.image,
    this.descriptionTextAlign,
    this.widgetDescription,
    this.isBtnClose,
  });

  closePopup(BuildContext context) {
    Navigator.of(context).pop();
  }

  Future<bool> onWillPop() {
    return Future.value(false);
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: onWillPop,
        child: Dialog(
          elevation: 0.0,
          backgroundColor: Colors.transparent,
          child: Stack(
            children: <Widget>[
              Container(
                padding: EdgeInsets.all(15),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(6)),
                  color: AppColors.white,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    (image != null
                        ? Container(
                            margin: EdgeInsets.only(bottom: 15),
                            child: Image.asset(
                              image!,
                              width: 50,
                              height: 50,
                              fit: BoxFit.contain,
                            ),
                          )
                        : SizedBox.shrink()),
                    Text(
                      title ?? AppStrings.getString(AppStrings.titleNotice)!,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 18,
                        fontFamily: AppFonts.robotoMedium,
                        color: AppColors.gray,
                      ),
                    ),
                    (isNullEmpty(description)
                        ? SizedBox.shrink()
                        : Padding(
                            padding: EdgeInsets.only(top: 10, bottom: (isNullEmpty(description2) ? 20 : 10)),
                            child: Text(
                              description ?? '',
                              textAlign: descriptionTextAlign ?? TextAlign.center,
                              style: TextStyle(
                                fontSize: 14,
                                color: AppColors.greyTextContent,
                              ),
                            ),
                          )),
                    (widgetDescription ?? SizedBox.shrink()),
                    (isNullEmpty(description2)
                        ? SizedBox.shrink()
                        : Padding(
                            padding: const EdgeInsets.only(bottom: 20),
                            child: Text(
                              description2 ?? '',
                              textAlign: descriptionTextAlign ?? TextAlign.center,
                              style: TextStyle(
                                fontSize: 14,
                                color: AppColors.redText3,
                              ),
                            ),
                          )),
                    Row(
                      children: <Widget>[
                        Expanded(
                          flex: 1,
                          child: TouchableWidget(
                              height: 44,
                              padding: EdgeInsets.all(0),
                              decoration: BoxDecoration(
                                  color: isTwoButton == true ? AppColors.mainBackground : AppColors.primary,
                                  borderRadius: BorderRadius.circular(6)),
                              onPressed: onPress1stButton ?? () => closePopup(context),
                              child: Text(
                                text1stButton ?? AppStrings.getString(AppStrings.ok)!,
                                style: buttonTextStyle(color: isTwoButton == true ? AppColors.gray : AppColors.white),
                              )),
                        ),
                        (isTwoButton == true
                            ? Container(
                                width: 10,
                              )
                            : SizedBox.shrink()),
                        (isTwoButton == true
                            ? Expanded(
                                flex: 1,
                                child: TouchableWidget(
                                    height: 44,
                                    padding: EdgeInsets.all(0),
                                    decoration:
                                        BoxDecoration(color: AppColors.primary, borderRadius: BorderRadius.circular(6)),
                                    onPressed: onPress2ndButton ?? () => closePopup(context),
                                    child: Text(
                                      text2ndButton ?? AppStrings.getString(AppStrings.cancel)!,
                                      style: buttonTextStyle(color: AppColors.white),
                                    )),
                              )
                            : SizedBox.shrink()),
                      ],
                    ),
                    _buildHeaderInfoApp(context),
                  ],
                ),
              ),
              (isBtnClose == true
                  ? Positioned(
                      top: 10,
                      right: 10,
                      child: TouchableWidget(
                        onPressed: () => closePopup(context),
                        child: Image.asset(
                          AppImages.icClose,
                          width: 14,
                          height: 14,
                          fit: BoxFit.contain,
                          color: AppColors.darkGrayText,
                        ),
                      ),
                    )
                  : Positioned(top: 10, right: 10, child: Container()))
            ],
          ),
        ));
  }

  Widget _buildHeaderInfoApp(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 5),
      child: Column(
        children: [
          Text(
            "${Get.find<MyAppController>().loginAccount ?? ""} | ${Get.find<MyAppController>().serialNumber ?? "" } | ${Get.find<MyAppController>().buildNumber ?? ""}",
            textAlign: TextAlign.center,
            style: TextStyle(
              fontSize: 12,
              fontFamily: kFontFamilyBeVietnamPro,
              color: AppColors.gray2,
            ),
          )
        ],
      ),
    );
  }
}
