import 'package:cashiermodule/widget_custom/touchable_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/data/model/qr_data_config.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/screen/qr/item_select_qr.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';

class QrWalletWidget extends StatelessWidget {
  final String? tileBoxQr;
  final Function onPressItem;
  final List<QrChildren> listQr;

  const QrWalletWidget({
    Key? key,
    this.tileBoxQr,
    required this.onPressItem,
    required this.listQr,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        //header
        _buildHeaderWalletQr(),
        Container(
          padding: EdgeInsets.all(10),
          height: 250,
          child: _buildListWalletQr(),
        )
      ],
    );
  }

  Widget _buildHeaderWalletQr() {
    return Container(
      height: 60,
      padding: EdgeInsets.only(top: 10, left: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(
            ((tileBoxQr != null) && tileBoxQr!.isNotEmpty)
                ? tileBoxQr!
                : AppStrings.getString(AppStrings.tv_name_group_qr_default)!,
            style: TextStyle(
                color: AppColors.black,
                fontFamily: AppFonts.robotoBold,
                fontSize: AppDimens.textSizeLarge24),
          ),
          Expanded(
            child: Container(),
          ),
          TouchableWidget(
            padding: EdgeInsets.zero,
              onPressed: () => Get.back(),
              child: Image.asset(
                AppImages.ic_cancel_wallet_qr,
                width: AppDimens.icon80,
                height: AppDimens.icon80,
                fit: BoxFit.contain,
              ))
        ],
      ),
    );
  }

  Widget _buildListWalletQr() {
    return GridView.builder(
      itemCount: listQr.length,
      scrollDirection: Axis.vertical,
      gridDelegate: SliverGridDelegateWithMaxCrossAxisExtent(
          maxCrossAxisExtent: 200,
          childAspectRatio: 1.5,
          crossAxisSpacing: 10,
          mainAxisSpacing: 10),
      itemBuilder: (BuildContext context, int index) {
        return ItemSelectQr(
          icon: listQr[index].logoChild,
          name: listQr[index].shortNameChild,
          borderCircular: 6,
          isSelect: false,
          onPressed: () {
            Get.back();
            onPressItem(listQr[index].qrType, listQr[index].shortNameChild, listQr[index].logoChild, listQr[index].amountMin);
          }
        );
      },
    );
  }
}
