import 'package:cashiermodule/constants/style.dart';
import 'package:flutter/material.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';
import 'package:mposxs/app/util/app_utils.dart';

class AmountWidget extends StatelessWidget{
  final String amount;
  final Color textColor;
  final double fontSize;

  const AmountWidget(
      {Key? key,
        this.amount = '0',
        this.textColor = AppColors.primary,
        this.fontSize = 46,})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return FittedBox(
      fit: BoxFit.scaleDown,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: <Widget>[
          Text(
            amount.length > 0
                ? AppUtils.formatCurrency(int.parse(amount))
                : '',
            style: TextStyle(
                fontSize: fontSize,
                color: textColor,
                fontWeight: FontWeight.w500,
                fontFamily: kFontFamilyBeVietnamPro),
          ),
          Padding(
            padding: const EdgeInsets.only(bottom: 10, left: 2),
            child: Text(
              'đ',
              style: TextStyle(
                  fontSize: AppDimens.textSizeMedium,
                  color: textColor ?? AppColors.primary.withOpacity(0.81),
                  decoration: TextDecoration.underline),
            ),
          ),
        ],
      ),
    );
  }


}