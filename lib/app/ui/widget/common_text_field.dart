import 'package:flutter/material.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';
import 'package:mposxs/app/util/app_validation.dart';

class CommonTextField extends StatefulWidget {
  final double? fontSize; // cỡ chữ
  final Color? color; // màu chữ
  final String? errorText; // nội dung lỗi (null: ẩn)
  final double? errorFontSize; // cỡ chữ lỗi
  final Color? errorColor; // màu chữ lỗi
  final Color? focusColor; // màu dòng gạch chân khi focus
  final String? fontFamily; // font chữ
  final bool? hasFloatingPlaceholder; // floating label hay không
  final String? hintText; // nội dung label
  final double? hintTextFontSize; // kích thước label (khi chưa floating)
  final String? hintTextFontFamily; // font chữ label
  final Widget? suffix; // widget sau (mặc định là nút clear)
  final Widget? prefixIcon; // widget trước
  final TextEditingController controller; // controller (bắt buộc phải có)
  final int? maxLength; //
  final bool? showMaxLengthCount; // hiện bộ đếm của maxLength
  final TextInputType? keyboardType; //
  final bool? obscureText; // ân text (vd: mật khẩu)
  final FocusNode? focusNode; //
  final bool? autoFocus; //
  final Function? onChanged; //
  final EdgeInsetsGeometry? contentPadding; //
  final bool? hideUnderBorderLine; // ẩn hiện gạch chân
  final Widget? imageClear; // widget thay cho nút clear mặc định
  final ThemeData?
      themeData; // dùng để thay đổi Theme (vd: màu chữ label khi floating)
  final Brightness? keyboardAppearance;
  final TextInputAction? textInputAction;
  final bool? isBorder;
  final double? height;
  final Function(String)? onSubmit;

  const CommonTextField({
    this.fontSize,
    this.errorText,
    this.errorFontSize,
    this.fontFamily,
    this.color,
    this.focusColor,
    this.errorColor,
    this.hasFloatingPlaceholder,
    this.hintText,
    this.hintTextFontSize,
    this.hintTextFontFamily,
    this.suffix,
    this.prefixIcon,
    required this.controller,
    this.maxLength,
    this.showMaxLengthCount,
    this.keyboardType,
    this.obscureText,
    this.focusNode,
    this.onChanged,
    this.contentPadding,
    this.hideUnderBorderLine,
    this.imageClear,
    this.themeData,
    this.autoFocus,
    this.keyboardAppearance,
    this.textInputAction,
    this.isBorder,
    this.height,
    this.onSubmit,
  });

  @override
  State<StatefulWidget> createState() {
    return _CommonTextFieldState();
  }
}

class _CommonTextFieldState extends State<CommonTextField> {
  void onChanged(String text) {
    setState(() {});
  }

  FocusNode focusNode = FocusNode();

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: widget.themeData ??
          ThemeData(
            primaryColor: AppColors.primary,
            hintColor: AppColors.tabUnSelected,
          ),
      child: Container(
        height: widget.height ?? 70,
        padding: (widget.isBorder == true)
            ? EdgeInsets.symmetric(horizontal: 5)
            : EdgeInsets.zero,
        decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.circular(8.0),
          border: (widget.isBorder == true)
              ? Border.all(color: AppColors.blackText, width: 1.0)
              : Border.symmetric(),
        ),
        child: TextField(
          onSubmitted: widget.onSubmit,
          style: TextStyle(
              fontSize: widget.fontSize ?? 18,
              color: widget.color ?? AppColors.blackText,
              fontFamily: widget.fontFamily ?? AppFonts.robotoRegular),
          decoration: InputDecoration(
              contentPadding: widget.contentPadding,
              floatingLabelBehavior: (isNullEmpty(widget.errorText))
                  ? (widget.hasFloatingPlaceholder == false
                      ? FloatingLabelBehavior.never
                      : FloatingLabelBehavior.auto)
                  : FloatingLabelBehavior.always,
              labelText: widget.hasFloatingPlaceholder == false
                  ? null
                  : widget.hintText,
              labelStyle: TextStyle(
                  color: AppColors.greyText,
                  fontSize: widget.hintTextFontSize ?? 15,
                  fontFamily:
                      widget.hintTextFontFamily ?? AppFonts.robotoRegular),
              hintText: widget.hasFloatingPlaceholder == false
                  ? widget.hintText
                  : null,
              hintStyle: TextStyle(
                  fontSize: widget.hintTextFontSize ?? 15,
                  fontFamily:
                      widget.hintTextFontFamily ?? AppFonts.robotoRegular),
              errorText:
                  isNullEmpty(widget.errorText) ? null : widget.errorText,
              errorStyle: TextStyle(
                  fontSize: widget.errorFontSize ?? 14,
                  color: widget.errorColor ?? AppColors.redText),
              suffix: widget.suffix ??
                  (widget.controller.text.isNotEmpty
                      ? TouchableWidget(
                          width: 30,
                          height: widget.fontSize == null
                              ? 17
                              : widget.fontSize! - 1,
                          padding: EdgeInsets.all(0),
                          child: widget?.imageClear ??
                              Image.asset(
                                AppImages.icClearText2,
                                width: 15,
                                height: 15,
                              ),
                          onPressed: () {
                            Future.delayed(Duration(milliseconds: 50))
                                .then((_) {
                              widget.controller.clear();
                              widget.focusNode ?? focusNode.requestFocus();
                              if (widget.onChanged != null) {
                                widget.onChanged!('');
                              } else {
                                onChanged('');
                              }
                            });
                          },
                        )
                      : null),
              prefixIcon: widget.prefixIcon,
              counter: widget.showMaxLengthCount == true
                  ? null
                  : SizedBox(
                      height: 0.0,
                    ),
              focusedBorder: widget.hideUnderBorderLine == true
                  ? UnderlineInputBorder(
                      borderSide:
                          BorderSide(color: AppColors.transparent, width: 0.1))
                  : UnderlineInputBorder(
                      borderSide: BorderSide(
                          color: widget.focusColor ?? AppColors.primary,
                          width: 2)),
              enabledBorder: widget.hideUnderBorderLine == true
                  ? UnderlineInputBorder(
                      borderSide:
                          BorderSide(color: AppColors.transparent, width: 0.1))
                  : UnderlineInputBorder(
                      borderSide: BorderSide(
                          color: AppColors.tabUnSelected, width: 1))),
          controller: widget.controller,
          maxLength: widget.maxLength,
          keyboardType: widget.keyboardType,
          obscureText: widget.obscureText ?? false,
          focusNode: widget.focusNode ?? focusNode,
          onChanged: widget.onChanged as void Function(String)? ?? onChanged,
          autofocus: widget.autoFocus ?? false,
          keyboardAppearance: widget.keyboardAppearance ?? Brightness.light,
          textInputAction: widget.textInputAction ?? TextInputAction.done,
        ),
      ),
    );
  }
}
