import 'dart:async';

import 'package:flutter/material.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_common_style.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';
import 'package:mposxs/app/util/app_validation.dart';

class DialogCountDownAlert extends StatefulWidget {
  final String? title, description, description2, text1stButton, text2ndButton, image;
  final Function? onPress1stButton, onPress2ndButton;
  final bool? isTwoButton;
  final bool? isBtnClose;
  final TextAlign? descriptionTextAlign;
  final Widget? widgetDescription;
  final int? timeSecondCountDown;

  DialogCountDownAlert({
    this.title,
    this.description,
    this.description2,
    this.text1stButton,
    this.text2ndButton,
    this.onPress1stButton,
    this.onPress2ndButton,
    this.isTwoButton,
    this.image,
    this.descriptionTextAlign,
    this.widgetDescription,
    this.isBtnClose,
    this.timeSecondCountDown
  });

  @override
  State<StatefulWidget> createState() => _CountdownDialogState();
}

class _CountdownDialogState extends State<DialogCountDownAlert> {
  late Timer _timer;
  int? _remainingSeconds = 0;
  bool showCountDown = true;

  closePopup(BuildContext context) {
    Navigator.of(context).pop();
  }

  Future<bool> onWillPop() {
    return Future.value(false);
  }

  @override
  void initState() {
    super.initState();
    _remainingSeconds = widget.timeSecondCountDown;
    _timer = Timer.periodic(Duration(seconds: 1), (Timer timer) {
      setState(() {
        if (_remainingSeconds! < 1) {
          timer.cancel();
          showCountDown = false;
        } else {
          _remainingSeconds = _remainingSeconds! - 1;
        }
      });
    });
  }

  @override
  void dispose() {
    super.dispose();
    _timer.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
        onWillPop: onWillPop,
        child: Dialog(
          elevation: 0.0,
          backgroundColor: Colors.transparent,
          child: Stack(
            children: <Widget>[
              Container(
                padding: EdgeInsets.all(15),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.all(Radius.circular(6)),
                  color: AppColors.white,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    (widget.image != null
                        ? Container(
                            margin: EdgeInsets.only(bottom: 15),
                            child: Image.asset(
                              widget.image!,
                              width: 50,
                              height: 50,
                              fit: BoxFit.contain,
                            ),
                          )
                        : SizedBox.shrink()),
                    Text(
                      widget.title ?? AppStrings.getString(AppStrings.titleNotice)!,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 18,
                        fontFamily: AppFonts.robotoMedium,
                        color: AppColors.gray,
                      ),
                    ),
                    (isNullEmpty(widget.description)
                        ? SizedBox.shrink()
                        : Padding(
                            padding: EdgeInsets.only(top: 10, bottom: (isNullEmpty(widget.description2) ? 20 : 10)),
                            child: Text(
                              widget.description ?? '',
                              textAlign: widget.descriptionTextAlign ?? TextAlign.center,
                              style: TextStyle(
                                fontSize: 14,
                                color: AppColors.greyTextContent,
                              ),
                            ),
                          )),
                    (widget.widgetDescription ?? SizedBox.shrink()),
                    (isNullEmpty(widget.description2)
                        ? SizedBox.shrink()
                        : Padding(
                            padding: const EdgeInsets.only(bottom: 20),
                            child: Text(
                              widget.description2 ?? '',
                              textAlign: widget.descriptionTextAlign ?? TextAlign.center,
                              style: TextStyle(
                                fontSize: 14,
                                color: AppColors.redText3,
                              ),
                            ),
                          )),
                    Stack(
                      children: <Widget>[
                        Row(
                          children: <Widget>[
                            Expanded(
                              flex: 1,
                              child: TouchableWidget(
                                  height: 44,
                                  padding: EdgeInsets.all(0),
                                  decoration: BoxDecoration(
                                      color: widget.isTwoButton == true ? AppColors.mainBackground : AppColors.primary,
                                      borderRadius: BorderRadius.circular(6)),
                                  onPressed: widget.onPress1stButton ?? () => closePopup(context),
                                  child: Text(
                                    widget.text1stButton ?? AppStrings.getString(AppStrings.ok)!,
                                    style: buttonTextStyle(color: widget.isTwoButton == true ? AppColors.gray : AppColors.white),
                                  )),
                            ),
                            (widget.isTwoButton == true
                                ? Container(
                              width: 10,
                            )
                                : SizedBox.shrink()),
                            (widget.isTwoButton == true
                                ? Expanded(
                              flex: 1,
                              child: TouchableWidget(
                                  height: 44,
                                  padding: EdgeInsets.all(0),
                                  decoration:
                                  BoxDecoration(color: AppColors.primary, borderRadius: BorderRadius.circular(6)),
                                  onPressed: widget.onPress2ndButton ?? () => closePopup(context),
                                  child: Text(
                                    widget.text2ndButton ?? AppStrings.getString(AppStrings.cancel)!,
                                    style: buttonTextStyle(color: AppColors.white),
                                  )),
                            )
                                : SizedBox.shrink()),

                          ],
                        ),
                        (showCountDown
                            ? Row(
                                children: [
                                  Expanded(
                                    flex: 1,
                                    child: Container(
                                        height: 44,
                                        alignment: Alignment.center,
                                        padding: EdgeInsets.all(0),
                                        decoration: BoxDecoration(color: AppColors.gray2, borderRadius: BorderRadius.circular(6)),
                                        child: Text(
                                          '$_remainingSeconds',
                                          style: buttonTextStyle(color: AppColors.white),
                                        )),
                                  )
                                ],
                              )
                            : SizedBox.shrink())
                      ],
                    ),
                  ],
                ),
              ),
              //==> icon close (in top right)
              (widget.isBtnClose == true
                  ? Positioned(
                      top: 10,
                      right: 10,
                      child: TouchableWidget(
                        onPressed: () => closePopup(context),
                        child: Image.asset(
                          AppImages.icClose,
                          width: 14,
                          height: 14,
                          fit: BoxFit.contain,
                          color: AppColors.darkGrayText,
                        ),
                      ),
                    )
                  : Positioned(top: 10, right: 10, child: SizedBox.shrink())),
            ],
          ),
        ));
  }
}
