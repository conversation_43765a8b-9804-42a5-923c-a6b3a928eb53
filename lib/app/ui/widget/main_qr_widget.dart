import 'package:card_swiper/card_swiper.dart';
import 'package:flutter/material.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:qr_flutter/qr_flutter.dart';

import '../../res/image/app_images.dart';

class MainQrWidget extends StatefulWidget {
  final String? qrCode;
  final Function callbackIndexSw;

  const MainQrWidget({Key? key, this.qrCode, required this.callbackIndexSw}) : super(key: key);

  @override
  State<MainQrWidget> createState() => _MainQrWidgetState();
}

class _MainQrWidgetState extends State<MainQrWidget> {

  String? iconQr;

  void changeIconQr(int index) {
    setState(() {
      if (index == 0) {
        iconQr = AppImages.ic_vietqr_sw;
      }else {
        iconQr = AppImages.ic_appleqr_sw;
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: 280,
      child: Row(
        children: [
          // Expanded(
          //   flex: 3, // chiếm 1/3 màn hình
          //   child: Container(),
          // ),
          // 2/3 màn hình bên phải
          Expanded(
            // flex: 10, // chiếm 2/3 màn hình
            child: Center(
              child: Container(
                child: Stack(
                  children: [
                    Align(
                      alignment: Alignment.centerRight, // Neo widget vào phía bên phải
                      child: Container(
                        decoration: BoxDecoration(
                            color: AppColors.grayBackground.withOpacity(0.5),
                            borderRadius: BorderRadius.circular(10),
                            boxShadow: [
                              BoxShadow(
                                color: AppColors.black.withOpacity(0.2),
                                spreadRadius: 1,
                                blurRadius: 2,
                              ),
                            ]
                        ),
                        width: 100,
                        height: 200,
                        child: Center(child: Image.asset(iconQr ?? '')),
                      ),
                    ),
                    Center(
                      child: Container(
                        width: 380,
                        height: 280,
                        child: Swiper(
                          itemWidth: 280.0,
                          itemHeight: 280.0,
                          layout: SwiperLayout.CUSTOM,
                          customLayoutOption: CustomLayoutOption(
                              startIndex: -1,
                              stateCount: 3
                          )..addTranslate([
                            Offset(-370.0, -60.0),
                            Offset(0.0, 0.0),
                            Offset(370.0, -40.0)
                          ]),
                          itemBuilder: (BuildContext context, int index) {
                            return Container(
                              width: 280,
                              height: 280,
                              child: Container(
                                padding: EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                    color: AppColors.white,
                                    borderRadius: BorderRadius.circular(10),
                                    boxShadow: [
                                      BoxShadow(
                                        color: AppColors.black.withOpacity(0.2),
                                        spreadRadius: 1,
                                        blurRadius: 2,
                                      ),
                                    ]
                                ),
                                child: QrImageView(
                                  padding: EdgeInsets.all(5),
                                  backgroundColor: Colors.transparent,
                                  data: widget.qrCode ?? '',
                                  version: QrVersions.auto,
                                  embeddedImage: AssetImage(AppImages.ic_vietqr_mini),
                                  embeddedImageStyle:
                                  QrEmbeddedImageStyle(size: Size(30, 30)),
                                ),
                              ),);
                          },
                          autoplay: false,
                          indicatorLayout: PageIndicatorLayout.NONE,
                          itemCount: 2,
                          scrollDirection: Axis.horizontal,
                          // control: const SwiperControl(),
                          viewportFraction: 0.8,
                          onIndexChanged: (index) {
                            widget.callbackIndexSw(index);
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          SizedBox(
            width: 10,
          )
        ],
      ),
    );
  }
}
