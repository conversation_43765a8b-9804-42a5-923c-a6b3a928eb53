import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/theme/app_dimens.dart';

import 'common_button.dart';

class BaseDialog extends StatelessWidget {

  // final Image icTop;
  final String? pathIcTop;
  final String? title;
  final String? desc;
  final Widget? widgetDesc;
  final String? subDesc;
  final Function? onPress1stButton, onPress2ndButton;
  final String? nameBtn1st, nameBtn2nd;
  final bool? isTwoButton;


  BaseDialog({this.pathIcTop, this.title, this.desc, this.widgetDesc,
    this.subDesc, this.onPress1stButton, this.onPress2ndButton,
    this.nameBtn1st,
    this.nameBtn2nd, this.isTwoButton});

  @override
  Widget build(BuildContext context) {
    return Dialog(shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.0)),
        elevation: 0.0,
        backgroundColor: Colors.transparent,
        child: dialogContent(context));
  }

  dialogContent(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppDimens.spaceSmall),
      decoration: BoxDecoration(
        color: AppColors.white,
        borderRadius: BorderRadius.all(Radius.circular(AppDimens.radiusMedium)),
      ),
      child: Column(
        // mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          pathIcTop==null?SizedBox():
            Image.asset(pathIcTop!, width: AppDimens.icon60, height: AppDimens.icon60,),
          SizedBox(height: AppDimens.spaceXSmall10,),
          Text(title!, style: TextStyle(
            color: AppColors.black,
            fontWeight: FontWeight.bold,
            fontSize: AppDimens.textSizeMedium
          ),),
          SizedBox(height: AppDimens.spaceXSmall8,),
          desc==null?SizedBox():Text(desc!, style: TextStyle(
            color: AppColors.black,
            fontSize: AppDimens.textSizeSmall
          ),),
          (subDesc==null)?SizedBox():Text(subDesc!,style: TextStyle(
              color: AppColors.black,
              fontSize: AppDimens.textSizeSmall
          ),),
          // (widgetDesc??SizedBox()),
          (widgetDesc==null?SizedBox():widgetDesc!),
          Row(
            children: <Widget>[
              Expanded(
                flex: 1,
                child: Container(
                  margin: EdgeInsets.only(top: AppDimens.spaceMedium),
                  child: CommonButton(
                    color: (isTwoButton == true) ? AppColors.mainBackground : AppColors.bgButton,
                    onPressed: () => onPress1stButton??Get.back(),
                    title: nameBtn1st??AppStrings.getString(AppStrings.ok)!,
                    textColor: (isTwoButton == true) ? AppColors.gray : AppColors.white,
                  ),
                ),
              ),

              (isTwoButton == true
                  ? SizedBox(width: AppDimens.spaceXSmall10,)
                  : SizedBox.shrink()),

              (onPress2ndButton==null?SizedBox():
              Expanded(
                flex: 1,
                child: Container(
                  margin: EdgeInsets.only(top: AppDimens.spaceMedium),
                  child: CommonButton(
                    onPressed: onPress2ndButton as void Function()?,
                    title: nameBtn2nd??AppStrings.getString(AppStrings.cancel)!,
                  ),
                ),
              )),
            ],
          )
        ],
      ),
    );
  }
}

