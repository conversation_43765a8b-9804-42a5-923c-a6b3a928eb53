import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/ui/widget/touchable_widget.dart';

class BaseBottomSheet extends StatelessWidget {
  final String? title;
  final Function? onPressClose;
  final Widget? child;
  final Widget? leftWidget;
  final bool? hideCloseButton;
  final BoxDecoration? headerDecoration;
  final bool? isCloseHeader;
  final TextAlign? titleAlign;

  const BaseBottomSheet(
      {Key? key,
      this.title,
      this.onPressClose,
      this.child,
      this.leftWidget,
      this.hideCloseButton,
      this.headerDecoration,
      this.titleAlign,
      this.isCloseHeader})
      : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: (isCloseHeader != true) ?
          EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom) : EdgeInsets.zero,
      decoration: BoxDecoration(
          color: AppColors.white,
          borderRadius: BorderRadius.only(
              topLeft: const Radius.circular(6),
              topRight: const Radius.circular(6))),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            (isCloseHeader != true)
                ? Container(
                    height: 70,
                    decoration: headerDecoration ??
                        BoxDecoration(
                          border: Border(
                              bottom: BorderSide(
                                  width: 1,
                                  color: AppColors.lightGray.withOpacity(0.1))),
                        ),
                    child: Row(
                      children: <Widget>[
                        leftWidget ?? SizedBox.shrink(),
                        Expanded(
                          flex: 1,
                          child: Container(
                            // margin: EdgeInsets.only(left: 44),
                            margin: EdgeInsets.only(left: 20),
                            child: Text(
                              title ?? '',
                              textAlign: titleAlign ?? TextAlign.center,
                              style: TextStyle(
                                  fontSize: 18,
                                  fontFamily: AppFonts.robotoMedium,
                                  color: AppColors.blackText),
                            ),
                          ),
                        ),
                        hideCloseButton == true
                            ? SizedBox(width: 44)
                            : TouchableWidget(
                          margin: EdgeInsets.only(right: 20),
                                child: Image.asset(AppImages.icClose,
                                    width: 20,
                                    height: 20,
                                    color: AppColors.greyTextContent),
                                onPressed: onPressClose ?? Get.back,
                                padding: EdgeInsets.all(15),
                              ),
                      ],
                    ),
                  )
                : SizedBox.shrink(),
            child ?? SizedBox.shrink(),
          ],
        ),
      ),
    );
  }
}
