import 'package:flutter/material.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';

class CommonButton extends MaterialButton {
  final String? title;
  final double? maxWidth;
  final double? minWidth;
  final double? height;
  final double? textSize;
  final double? elevation;
  final double? borderCircular;
  final String? fontFamily;
  final TextDecoration? textDecoration;

  const CommonButton({
    required VoidCallback? onPressed,
    Color? textColor,
    Color? color,
    this.title,
    this.maxWidth,
    this.minWidth,
    this.height,
    this.textSize,
    this.elevation,
    this.fontFamily,
    this.borderCircular,
    Widget? child,
    ShapeBorder? shape,
    EdgeInsetsGeometry? padding,
    this.textDecoration,
  }) : super(
          onPressed: onPressed,
          textColor: textColor,
          color: color,
          child: child,
          shape: shape,
          padding: padding,
        );

  @override
  Widget build(BuildContext context) {
    final ButtonThemeData buttonTheme = ButtonTheme.of(context);
    return RawMaterialButton(
      onPressed: () async{
          // delay for show effect press to button
          await Future.delayed(Duration(milliseconds: 100));
          onPressed!();
      },
      fillColor: color ?? AppColors.bgButton,
      elevation: elevation ?? 0,
      constraints: color != Colors.transparent
          ? buttonTheme.getConstraints(this).copyWith(
              maxWidth: maxWidth ?? double.infinity,
              minWidth: minWidth ?? double.infinity,
              minHeight: height ?? 50,
              maxHeight: height ?? 50)
          : BoxConstraints.tightFor(),
      padding: padding ?? EdgeInsets.only(left: 10, right: 10),
      shape: shape ??
          RoundedRectangleBorder(
            borderRadius: new BorderRadius.circular(borderCircular??6),
          ),
      child: child ??
          Text(
            title!,
            style: TextStyle(
              fontSize: textSize ?? 18,
              color: textColor ?? AppColors.white,
              fontFamily: fontFamily ?? AppFonts.robotoMedium,
              decoration: textDecoration??TextDecoration.none,
            ),
          ),
    );
  }
}
