import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/controller/app_controller.dart';
import 'package:mposxs/app/res/string/app_strings.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';

class HeaderBaseInfor extends StatelessWidget {
  final MyAppController _appController = Get.find<MyAppController>();

  HeaderBaseInfor();

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.fromLTRB(20, 0, 20, 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Text(_appController.loginAccount.tr +
              " | " +
              _appController.serialNumber!.tr + ' | '),
          RichText(
            text: TextSpan(
              children: [
                TextSpan(
                  text: '${AppStrings.getString(AppStrings.hotline)} ',
                  style: TextStyle(color: AppColors.blackText),
                ),
                TextSpan(
                  text: '1900-63-64-88',
                  style: TextStyle(color: AppColors.blueText),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
