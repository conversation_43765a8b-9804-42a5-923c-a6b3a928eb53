import 'package:cashiermodule/constants/style.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dash/flutter_dash.dart';

import '../../res/string/app_strings.dart';
import '../../util/app_utils.dart';
import '../theme/app_colors.dart';

class QuickDrawWidget extends StatelessWidget {

  String? feeQuickDraw;

  QuickDrawWidget(this.feeQuickDraw);

  @override
  Widget build(BuildContext context) {
    return Container(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            child: RichText(
              text: new TextSpan(
                children: <TextSpan>[
                  new TextSpan(
                      text: AppStrings.getString(AppStrings.des_quick_draw) ??'',
                      style: style_S18_W400_BlueColor),
                  new TextSpan(text: ' "${AppStrings.getString(AppStrings.buttonConfirm)}".',  style: style_S18_W600_BlackColor),
                ],
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 10),
            child:
            RichText(
              text: new TextSpan(
                children: <TextSpan>[
                  new TextSpan(
                      text: AppStrings.getString(AppStrings.title_cost_for_service) ?? '',
                      style: style_S18_W400_BlueColor),
                  new TextSpan(text: ' ${AppUtils.formatCurrency(feeQuickDraw)} đ.',  style: style_S18_W600_RedColor),
                ],
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.symmetric(vertical: 15),
            child: Dash(
              length: MediaQuery.of(context).size.width - 110,
              direction: Axis.horizontal,
              dashLength: 3.5,
              dashGap: 3.5,
              dashColor: AppColors.dash,
              dashThickness: 1,
            ),
          ),
          Text(AppStrings.getString(AppStrings.labelTariffFastWithdraw) ?? '', style: style_S16_W600_BlackColor,),
          Text(AppStrings.getString(AppStrings.content_note_quick_withdraw1) ?? '', style: style_S16_W400_BlackColor,),
          Text(AppStrings.getString(AppStrings.content_note_quick_withdraw2) ?? '', style: style_S16_W400_BlackColor,),
        ],
      ),
    );
  }
}
