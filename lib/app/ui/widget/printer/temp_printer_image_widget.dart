import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:screenshot/screenshot.dart';

class TempPrinterImageWidget extends StatelessWidget {
  final Image? imageByUrlPrint;
  final ScreenshotController? screenshotController;

  TempPrinterImageWidget({
    Key? key,
    this.imageByUrlPrint,
    this.screenshotController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        scrollDirection: Axis.vertical,
        child: Screenshot(
          controller: screenshotController!,
          child: Container(
            width: 384,
            color: AppColors.white,
            child: imageByUrlPrint,
          ),
        ),
      ),
    );
  }
}
