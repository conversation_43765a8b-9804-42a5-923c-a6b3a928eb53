import 'package:flutter/material.dart';

class BottomSheetTestBillPrinter extends StatefulWidget {

    final Widget? widgetChild;
    
  const BottomSheetTestBillPrinter({Key? key, Widget? child, this.widgetChild}) : super(key: key);

  @override
  _BottomSheetTestBillPrinterState createState() => _BottomSheetTestBillPrinterState();
}

class _BottomSheetTestBillPrinterState extends State<BottomSheetTestBillPrinter> {
  // @override
  // Widget build(BuildContext context) {
  //     return BaseBottomSheet(
  //         onPressClose: () => Navigator.pop(context),
  //         title: AppStrings.getString(AppStrings.labelConfirmCancelTransaction),
  //         child: Container(
  //             padding: EdgeInsets.symmetric(vertical: 15, horizontal: 15),
  //             child: ListView(
  //                 children: <Widget>[
  //                     widget.widgetChild
  //                 ],
  //             ),
  //         ));
  // }

  @override
  Widget build(BuildContext context) {
      return SizedBox.expand(
          child: DraggableScrollableSheet(
            
              builder: (BuildContext context, ScrollController scrollController) {
                  return Container(
                      color: Colors.blue[100],
                      child: ListView(
                          children: <Widget>[
                              widget.widgetChild!
                          ],
                      ),
                      /*child: ListView.builder(
                          controller: scrollController,
                          itemCount: 25,
                          itemBuilder: (BuildContext context, int index) {
                              return ListTile(title: Text('Item $index'));
                          },
                      ),*/
                  );
              },
          ),
      );
  }
}
