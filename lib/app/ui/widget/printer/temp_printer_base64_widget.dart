import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:screenshot/screenshot.dart';

class TempPrinterBase64Widget extends StatelessWidget {
  final String? base64Image;
  final ScreenshotController? screenshotController;

  TempPrinterBase64Widget({
    Key? key,
    this.base64Image,
    this.screenshotController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: SingleChildScrollView(
          scrollDirection: Axis.vertical,
          child: Screenshot(
            controller: screenshotController!,
            child: Container(
              width: 384,
              color: AppColors.white,
              child: Image.memory(
                base64Decode(base64Image!),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
