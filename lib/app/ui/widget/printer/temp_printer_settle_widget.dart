import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:screenshot/screenshot.dart';

class TempPrinterSettleWidget extends StatelessWidget {
  final String? base64ImageSettle;
  final ScreenshotController? screenshotController;

  TempPrinterSettleWidget({
    Key? key,
    this.base64ImageSettle,
    this.screenshotController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        scrollDirection: Axis.vertical,
        child: Screenshot(
          controller: screenshotController!,
          child: Container(
            width: 384,
            color: AppColors.white,
            child: Stack(
              children: [
                Container(
                  width: 384,
                  child: Image.memory(
                    base64Decode(base64ImageSettle!),
                  ),
                ),
                Container(
                  width: 384,
                  height: 175,
                  color: AppColors.white,
                  child: Image.asset(AppImages.ic_logo_mpos_print, width: 384),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
