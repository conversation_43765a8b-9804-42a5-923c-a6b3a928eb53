import 'package:flutter/material.dart';
import 'package:mposxs/app/res/font/app_fonts.dart';
import 'package:mposxs/app/res/image/app_images.dart';
import 'package:mposxs/app/ui/theme/app_colors.dart';
import 'package:mposxs/app/util/app_utils.dart';
import 'package:mposxs/app/util/app_validation.dart';
import 'package:screenshot/screenshot.dart';

class TempPrinterBillQrWidget extends StatelessWidget {
  final String? printTID;
  final String? printMID;
  final String? printRef;
  final int? printAmount;
  final String? printDate;
  final String? printTime;
  final String? printMCName;
  final String? printMCAddress;
  final String? printTxid;
  final String? printDes;
  final String? printQRName;
  final String? printAuthCode;
  final ScreenshotController? screenshotController;

  TempPrinterBillQrWidget({
    Key? key,
    this.printTID,
    this.printMID,
    this.printRef,
    this.printAmount,
    this.printDate,
    this.printTime,
    this.printMCName,
    this.printMCAddress,
    this.printTxid,
    this.printDes,
    this.printQRName,
    this.printAuthCode,
    this.screenshotController,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        scrollDirection: Axis.vertical,
        child: Screenshot(
          controller: screenshotController!,
          child: Container(
            width: 384,
            color: AppColors.white,
            child: Column(
              children: [
                Image.asset(AppImages.ic_logo_mpos_print, width: 384),
                SizedBox(height: 25),
                Text(
                  ((printMCName ?? '') + '\n' + (printMCAddress ?? '')).toUpperCase(),
                  style: TextStyle(
                    fontSize: 18,
                    color: Colors.black,
                    fontFamily: AppFonts.robotoRegular,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 15),
                Text(
                  'SALE - THANH TOAN',
                  style: TextStyle(
                    fontSize: 28,
                    color: Colors.black,
                    fontFamily: AppFonts.robotoMedium,
                  ),
                  textAlign: TextAlign.center,
                ),
                Text(
                  '---------------------------------------------------------',
                  style: TextStyle(
                    fontSize: 24,
                    color: Colors.black,
                    fontFamily: AppFonts.robotoRegular,
                  ),
                  textAlign: TextAlign.center,
                ),
                Row(
                  children: [
                    Text(
                      'DATE/NGAY:',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                        fontFamily: AppFonts.robotoRegular,
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        printDate ?? '',
                        style: TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                          fontFamily: AppFonts.robotoRegular,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    )
                  ],
                ),
                SizedBox(height: 5),
                Row(
                  children: [
                    Text(
                      'TIME/GIO:',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                        fontFamily: AppFonts.robotoRegular,
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        printTime ?? '',
                        style: TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                          fontFamily: AppFonts.robotoRegular,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    )
                  ],
                ),
                SizedBox(height: 5),
                Row(
                  children: [
                    Text(
                      'TID:',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                        fontFamily: AppFonts.robotoRegular,
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        printTID ?? '',
                        style: TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                          fontFamily: AppFonts.robotoRegular,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    )
                  ],
                ),
                SizedBox(height: 5),
                Row(
                  children: [
                    Text(
                      'MID:',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                        fontFamily: AppFonts.robotoRegular,
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        printMID ?? '',
                        style: TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                          fontFamily: AppFonts.robotoRegular,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Text(
                      'Transaction ID:',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                        fontFamily: AppFonts.robotoRegular,
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        printTxid ?? '',
                        style: TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                          fontFamily: AppFonts.robotoRegular,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Text(
                      'Ref No:',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                        fontFamily: AppFonts.robotoRegular,
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        printRef ?? '',
                        style: TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                          fontFamily: AppFonts.robotoRegular,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Text(
                      'Appr Code/MCC:',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                        fontFamily: AppFonts.robotoRegular,
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        printAuthCode ?? '',
                        style: TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                          fontFamily: AppFonts.robotoRegular,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 5),
                Row(
                  children: [
                    Text(
                      'TYPE/LOAI:',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                        fontFamily: AppFonts.robotoRegular,
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        printQRName ?? '',
                        style: TextStyle(
                          fontSize: 20,
                          color: Colors.black,
                          fontFamily: AppFonts.robotoRegular,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 5),
                !isNullEmpty(printDes)
                    ? Column(
                        children: [
                          Row(
                            children: [
                              Text(
                                'DES/GHICHU:',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Colors.black,
                                  fontFamily: AppFonts.robotoRegular,
                                ),
                              ),
                              Expanded(
                                flex: 1,
                                child: Text(
                                  printDes ?? '',
                                  style: TextStyle(
                                    fontSize: 20,
                                    color: Colors.black,
                                    fontFamily: AppFonts.robotoRegular,
                                  ),
                                  textAlign: TextAlign.right,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 5),
                        ],
                      )
                    : SizedBox.shrink(),
                Text(
                  '---------------------------------------------------------',
                  style: TextStyle(
                    fontSize: 24,
                    color: Colors.black,
                    fontFamily: AppFonts.robotoRegular,
                  ),
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 5),
                Row(
                  children: [
                    Text(
                      'TOTAL/TONG:',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.black,
                        fontFamily: AppFonts.robotoRegular,
                      ),
                    ),
                    Expanded(
                      flex: 1,
                      child: Text(
                        '${AppUtils.formatCurrency(printAmount ?? 0)} VND',
                        style: TextStyle(
                          fontSize: 24,
                          color: Colors.black,
                          fontFamily: AppFonts.robotoMedium,
                        ),
                        textAlign: TextAlign.right,
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 25),
                Text(
                  'NO REFUND/KHONG HOAN TIEN\n*** CUSTOMER COPY/ LIEN DANH CHO KHACH HANG***',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.black,
                    fontFamily: AppFonts.robotoRegular,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
