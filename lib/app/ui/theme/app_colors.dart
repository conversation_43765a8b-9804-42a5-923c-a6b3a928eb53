import 'package:flutter/material.dart';

class AppColors {
  static const primary = Color(0xFF008BF4);
  static const primary2 = Color(0xFF083E88);
  static const header = Color(0xFFF2F3F5);
  static const success = Color(0xFF6FA637);
  static const warning = Color(0xFFE99323);
  static const error = Color(0xFFC21A15);
  static const white = Color(0xFFFFFFFF);
  static const black = Color(0xFF000000);
  static const orange = Color(0xFFE37125);
  static const lightGray = Color(0xFF707070);
  static const lightGrayRipple = Color(0xFFE0E0E0);
  static const red = Color(0xFFFF0000);
  static const redText = Color(0xFFCF232A);
  static const redText2 = Color(0xFFC21A15);
  static const redText3 = Color(0xFFD12D29);
  static const redButton = Color(0xFFD12D29);
  static const transparent = Color(0x0000000000);
  static const mainBackground = Color(0xFFF0F4F7);
  static const blackText = Color(0xFF393939);
  static const blackText1 = Color(0xFF404041);
  static const blackText2 = Color(0xFF434448);
  static const blackDarkText = Color(0xFF2E2E2E);
  static const blackBlueText = Color(0xFF616A71);
  static const blueText = Color(0xFF2472FC);
  static const greyText = Color(0xFFB8BBBC);
  static const darkGrayText = Color(0xFF4B4B4B);
  static const greyTextContent = Color(0xFF868F95);
  static const lightGreyText = Color(0xFFC7C7C7);
  static const backDrop = Color(0xB200000000);
  static const headerGradient1 = Color(0xFFD12D29);
  static const headerGradient2 = Color(0xFFD15129);
  static const gray = Color(0xFF515151);
  static const gray1 = Color(0xFF808890);
  static const gray2 = Color(0xFF898989);
  static const gray3 = Color(0xFFF4F4F4);
  static const blue = Color(0xFF0066B3);
  static const darkBlue = Color(0xFF175FDF);
  static const lightBlue = Color(0xFFDEECF7);
  static const tabUnSelected = Color(0xFFB8BDC0);
  static const tabSelected = Color(0xFFC21A15);
  static const lightGrayOpacity = Color(0x20707070);
  static const grayBorder = Color(0xFFDCDCDC);
  static const grayBorderQr = Color(0xFFE5E5E5);
  static const grayBackground = Color(0xFFF5F5F5);
  static const green = Color(0xFF00913D);
  static const green2 = Color(0xFF18994E);
  static const grayBackgroundStt = Color(0xFFE9EFF3);
  static const yellow = Color(0xFFF9A617);
  static const lightBlueBackground = Color(0xFFE4EAEE);
  static const lightGrayBackground = Color(0xFFF2F2F2);
  static const orangeDark = Color(0xFFE99323);
  static const orange1 = Color(0xFFFFA412);
  static const dash = Color(0xFFC2C9CE);
  static const dash2 = Color(0xFFABABAB);
  static const pink = Color(0xFFFFF6EF);
  static const darkRed = Color(0xFF720C08);
  static const lightGreen = Color(0xFF3BB54A);
  static const lightYellow = Color(0xFFFFEEB9);
  static const yellow1 = Color(0xFFFFF3DC);
  static const bgDarkRed = Color(0xFFB42400);

  static const bgButton1 = Color(0xFFF1F6EB);
  static const bgButton = Color(0xFF6FA637);
  static const bgMenu = Color(0xAA000000);
  static const bgIcClose = Color(0x4AFFFFFF);
  static const bgCompanyInfo = Color(0x88000000);

  static const bg_type_qr = Color(0xFF5C8A2E);
  static const bg_transparent_qr = Color(0x43242424);
  static const white1 = Color(0xE7FFFFFF);
  static const bg_btn_qr = Color(0xFFFFA412);
  static const ufo_test = Color(0xFFFFB346);


  static const btn_scan_Card    = Color(0xFF0979FD);
  static const btn_vietQR       = Color(0xFFFFA412);
  static const btn_installment  = Color(0xFFD12D29);
  static const btn_wallet       = Color(0xFF6FA637);
  static const btn_moto         = Color(0xFFFF7013);
  static const btn_link         = Color(0xFF00B2D5);
  static const btn_BNPL         = Color(0xFF1825A9);
  static const btn_check_card   = Color(0xFFF12091);
  static const btn_deposit      = Color(0xFF1B9A51);

}
