import 'dart:async';
import 'dart:ui';

import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:mposxs/app/data/provider/native_bridge.dart';

import 'app/binding/app_binding.dart';
import 'app/res/string/app_strings.dart';
import 'app/route/app_pages.dart';
import 'app/ui/screen/root_screen.dart';
import 'build_constants.dart';

void main() {
  // WidgetsFlutterBinding.ensureInitialized();
  BuildConstants.setEnvironment(Environment.LIVE);
  mainDelegate();
}

void mainDelegate() async {
  WidgetsFlutterBinding.ensureInitialized();

  // await initializeService();

  FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  // FlutterError.onError = FirebaseCrashlytics.instance.recordFlutterFatalError;

  FlutterError.onError = (errorDetails) {
    FirebaseCrashlytics.instance.recordFlutterFatalError(errorDetails);
  };
  // Pass all uncaught asynchronous errors that aren't handled by the Flutter framework to Crashlytics
  PlatformDispatcher.instance.onError = (error, stack) {
    FirebaseCrashlytics.instance.recordError(error, stack, fatal: true);
    return true;
  };


  runApp(
    ScreenUtilInit(
      builder: (BuildContext context, Widget? child) {
        return GetMaterialApp(
          initialBinding: AppBinding(),
          initialRoute: AppRoute.splash_screen,
          getPages: AppPages.pages,
          localizationsDelegates: [
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          translations: AppStrings(),
          supportedLocales: [const Locale('vi', 'VN'), const Locale('en', 'US')],
          locale: const Locale('vi', 'VN'),
          fallbackLocale: const Locale('vi', 'VN'),
          builder: (context, child) {
            return RootScreen(child);
          },
        );
      },
    ),
  );
}



@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // If you're going to use other Firebase services in the background, such as Firestore,
  // make sure you call `initializeApp` before using other Firebase services.
  await Firebase.initializeApp();
  if (message.notification != null) {
    print('MessagingBackground notification: ${message.data}');
    String messageData = message.data['body'];
    _putNotiSuccessToCashier(messageData);
  }
  print("Handling a background message: ${message.data}");
}

_putNotiSuccessToCashier(String messageData) {
  String currentRoute = Get.currentRoute;
  if (currentRoute == AppRoute.listOrderPage) {
    NativeBridge.getInstance().putSuccessQrNotification(messageData);
  }
}

