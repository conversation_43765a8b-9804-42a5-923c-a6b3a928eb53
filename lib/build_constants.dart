import 'package:cashiermodule/model_instance/app_configuration.dart' as cashierAppConfiguration;

enum Environment { DEV, LIVE, PROD }

class BuildConstants {
  static late Map<String, dynamic> _config;
  static var currentEnvironment = Environment.DEV;

  static void setEnvironment(Environment env) {
    switch (env) {
      case Environment.PROD:
        cashierAppConfiguration.AppConfiguration().setEnvironment(cashierAppConfiguration.Environment.PROD);
        _config = _Config.prodConstants;
        currentEnvironment = Environment.PROD;
        break;
      case Environment.LIVE:
        cashierAppConfiguration.AppConfiguration().setEnvironment(cashierAppConfiguration.Environment.STAGING);
        _config = _Config.stagingConstants;
        currentEnvironment = Environment.LIVE;
        break;
      case Environment.DEV:
        cashierAppConfiguration.AppConfiguration().setEnvironment(cashierAppConfiguration.Environment.DEV);
        _config = _Config.devConstants;
        currentEnvironment = Environment.DEV;
        break;
    }
  }

  static get serverAPI {
    return _config[_Config.SERVER_API];
  }

  static get appTYPE {
    return _config[_Config.APP_TYPE];
  }
}

class _Config {
  static const SERVER_API = "SERVER_API";
  static const APP_TYPE = "APP_TYPE";

  static Map<String, dynamic> prodConstants = {
    SERVER_API: "https://api.mpos.vn",
    APP_TYPE: "MPOS_LITE",
  };

  static Map<String, dynamic> stagingConstants = {
    SERVER_API: "https://staging-api.mpos.vn",
    APP_TYPE: "MPOS_LITE",
  };

  static Map<String, dynamic> devConstants = {
    SERVER_API: "https://devapi.mpos.vn",
    APP_TYPE: "MPOS_LITE",
  };
}
