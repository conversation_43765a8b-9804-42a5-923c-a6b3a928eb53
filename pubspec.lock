# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _fe_analyzer_shared:
    dependency: transitive
    description:
      name: _fe_analyzer_shared
      sha256: ae92f5d747aee634b87f89d9946000c2de774be1d6ac3e58268224348cd0101a
      url: "https://pub.dev"
    source: hosted
    version: "61.0.0"
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      sha256: "2f428053492f92303e42c9afa8e3a78ad1886760e7b594e2b5a6b6ee47376360"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  analyzer:
    dependency: transitive
    description:
      name: analyzer
      sha256: ea3d8652bda62982addfd92fdc2d0214e5f82e43325104990d4f4c4a2a313562
      url: "https://pub.dev"
    source: hosted
    version: "5.13.0"
  archive:
    dependency: transitive
    description:
      name: archive
      sha256: "22600aa1e926be775fa5fe7e6894e7fb3df9efda8891c73f70fb3262399a432d"
      url: "https://pub.dev"
    source: hosted
    version: "3.4.10"
  args:
    dependency: transitive
    description:
      name: args
      sha256: eef6c46b622e0494a36c5a12d10d77fb4e855501a91c1b9ef9339326e58f0596
      url: "https://pub.dev"
    source: hosted
    version: "2.4.2"
  asn1lib:
    dependency: transitive
    description:
      name: asn1lib
      sha256: b74e3842a52c61f8819a1ec8444b4de5419b41a7465e69d4aa681445377398b0
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  async:
    dependency: transitive
    description:
      name: async
      sha256: bfe67ef28df125b7dddcea62755991f807aa39a2492a23e1550161692950bbe0
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  audio_session:
    dependency: transitive
    description:
      name: audio_session
      sha256: "343e83bc7809fbda2591a49e525d6b63213ade10c76f15813be9aed6657b3261"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.21"
  audioplayers:
    dependency: "direct main"
    description:
      name: audioplayers
      sha256: a565e7e3e8a21a823b8cd7fed0bde1eb3796a96b373374be557adecfb511fa6b
      url: "https://pub.dev"
    source: hosted
    version: "0.20.1"
  auto_size_text:
    dependency: transitive
    description:
      name: auto_size_text
      sha256: "3f5261cd3fb5f2a9ab4e2fc3fba84fd9fcaac8821f20a1d4e71f557521b22599"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  bugsnag_flutter:
    dependency: transitive
    description:
      name: bugsnag_flutter
      sha256: "38d85eb4894633fcfdd7b31b12c3f247c589ac075635b2f4b827134a7c0abb87"
      url: "https://pub.dev"
    source: hosted
    version: "2.5.0"
  build:
    dependency: transitive
    description:
      name: build
      sha256: "3fbda25365741f8251b39f3917fb3c8e286a96fd068a5a242e11c2012d495777"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  build_config:
    dependency: transitive
    description:
      name: build_config
      sha256: bf80fcfb46a29945b423bd9aad884590fb1dc69b330a4d4700cac476af1708d1
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  build_daemon:
    dependency: transitive
    description:
      name: build_daemon
      sha256: "757153e5d9cd88253cb13f28c2fb55a537dc31fefd98137549895b5beb7c6169"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  build_resolvers:
    dependency: transitive
    description:
      name: build_resolvers
      sha256: "0713a05b0386bd97f9e63e78108805a4feca5898a4b821d6610857f10c91e975"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  build_runner:
    dependency: "direct dev"
    description:
      name: build_runner
      sha256: b0a8a7b8a76c493e85f1b84bffa0588859a06197863dba8c9036b15581fd9727
      url: "https://pub.dev"
    source: hosted
    version: "2.3.3"
  build_runner_core:
    dependency: transitive
    description:
      name: build_runner_core
      sha256: "0671ad4162ed510b70d0eb4ad6354c249f8429cab4ae7a4cec86bbc2886eb76e"
      url: "https://pub.dev"
    source: hosted
    version: "7.2.7+1"
  built_collection:
    dependency: transitive
    description:
      name: built_collection
      sha256: "376e3dd27b51ea877c28d525560790aee2e6fbb5f20e2f85d5081027d94e2100"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.1"
  built_value:
    dependency: transitive
    description:
      name: built_value
      sha256: "69acb7007eb2a31dc901512bfe0f7b767168be34cb734835d54c070bfa74c1b2"
      url: "https://pub.dev"
    source: hosted
    version: "8.8.0"
  cached_network_image:
    dependency: transitive
    description:
      path: cached_network_image
      ref: develop
      resolved-ref: "26321a568851f4bfa979d57d67127babf6aa2731"
      url: "https://github.com/DucLQ92/flutter_cached_network_image.git"
    source: git
    version: "3.2.3"
  cached_network_image_platform_interface:
    dependency: transitive
    description:
      name: cached_network_image_platform_interface
      sha256: bb2b8403b4ccdc60ef5f25c70dead1f3d32d24b9d6117cfc087f496b178594a7
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  cached_network_image_web:
    dependency: transitive
    description:
      name: cached_network_image_web
      sha256: b8eb814ebfcb4dea049680f8c1ffb2df399e4d03bf7a352c775e26fa06e02fa0
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  card_swiper:
    dependency: "direct main"
    description:
      name: card_swiper
      sha256: "21e52a144decbf0054e7cfed8bbe46fc89635e6c86b767eaccfe7d5aeba32528"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  carousel_slider:
    dependency: transitive
    description:
      name: carousel_slider
      sha256: "9c695cc963bf1d04a47bd6021f68befce8970bcd61d24938e1fb0918cf5d9c42"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.1"
  cashiermodule:
    dependency: "direct main"
    description:
      path: "cashier/cashier_module"
      ref: android_release
      resolved-ref: "7193751fe11d34a5e913bcb037e4fb68f72f7585"
      url: "https://gitlab.saobang.vn/sondh/cashier.git"
    source: git
    version: "1.0.0+1"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: e6a326c8af69605aec75ed6c187d06b349707a27fbff8222ca9cc2cff167975c
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  checked_yaml:
    dependency: transitive
    description:
      name: checked_yaml
      sha256: feb6bed21949061731a7a75fc5d2aa727cf160b91af9a3e464c5e3a32e28b5ff
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  cloud_firestore_platform_interface:
    dependency: transitive
    description:
      name: cloud_firestore_platform_interface
      sha256: d023142c18c28b2610c23c196e829c96976569cc2aa2f8e45328ae8a64c428d1
      url: "https://pub.dev"
    source: hosted
    version: "5.7.7"
  cloud_firestore_web:
    dependency: transitive
    description:
      name: cloud_firestore_web
      sha256: "3d7d4fa8c1dc5a1f7cb33985ae0ab9924d33d76d4959fe26aed84b7d282887e3"
      url: "https://pub.dev"
    source: hosted
    version: "2.8.10"
  code_builder:
    dependency: transitive
    description:
      name: code_builder
      sha256: "1be9be30396d7e4c0db42c35ea6ccd7cc6a1e19916b5dc64d6ac216b5544d677"
      url: "https://pub.dev"
    source: hosted
    version: "4.7.0"
  collection:
    dependency: "direct main"
    description:
      name: collection
      sha256: cfc915e6923fe5ce6e153b0723c753045de46de1b4d63771530504004a45fae0
      url: "https://pub.dev"
    source: hosted
    version: "1.17.0"
  connectivity:
    dependency: "direct main"
    description:
      name: connectivity
      sha256: a8e91263cf3e25fb5cc95e19dfde4999e32a648ac3b9e8a558a28165731678f8
      url: "https://pub.dev"
    source: hosted
    version: "3.0.6"
  connectivity_for_web:
    dependency: transitive
    description:
      name: connectivity_for_web
      sha256: "01a390c1d5adc2ed1fa1f52d120c07fe9fd01166a93f965a832fd6cfc0ea6482"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.0+1"
  connectivity_macos:
    dependency: transitive
    description:
      name: connectivity_macos
      sha256: "51ae08d5162eca9669b9d8951ed83ce19c5355a81149f94e4dee2740beb93628"
      url: "https://pub.dev"
    source: hosted
    version: "0.2.1+2"
  connectivity_platform_interface:
    dependency: transitive
    description:
      name: connectivity_platform_interface
      sha256: "2d82e942df9d49f29a24bb07fb5ce085d4a53e47818c62364d2b6deb9e0d7a8e"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  connectivity_plus:
    dependency: transitive
    description:
      name: connectivity_plus
      sha256: "77a180d6938f78ca7d2382d2240eb626c0f6a735d0bfdce227d8ffb80f95c48b"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  connectivity_plus_platform_interface:
    dependency: transitive
    description:
      name: connectivity_plus_platform_interface
      sha256: cf1d1c28f4416f8c654d7dc3cd638ec586076255d407cef3ddbdaf178272a71a
      url: "https://pub.dev"
    source: hosted
    version: "1.2.4"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: "0f08b14755d163f6e2134cb58222dd25ea2a2ee8a195e53983d57c075324d592"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.1"
  credit_card_type_detector:
    dependency: "direct main"
    description:
      name: credit_card_type_detector
      sha256: e49d8ce0e76969093982ed9c920f2204bd34e41ddbcaf4589d999af6dc00a3e4
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: "445db18de832dba8d851e287aff8ccf169bed30d2e94243cb54c7d2f1ed2142c"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.3+6"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: ff625774173754681d66daaf4a448684fb04b78f902da9cb3d308c19cc5e8bab
      url: "https://pub.dev"
    source: hosted
    version: "3.0.3"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: "831883fb353c8bdc1d71979e5b342c7d88acfbc643113c14ae51e2442ea0f20f"
      url: "https://pub.dev"
    source: hosted
    version: "0.17.3"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: d57953e10f9f8327ce64a508a355f0b1ec902193f66288e8cb5070e7c47eeb2d
      url: "https://pub.dev"
    source: hosted
    version: "1.0.6"
  dart_style:
    dependency: transitive
    description:
      name: dart_style
      sha256: "1efa911ca7086affd35f463ca2fc1799584fb6aa89883cf0af8e3664d6a02d55"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "79e0c23480ff85dc68de79e2cd6334add97e48f7f4865d17686dd6ea81a47e8c"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.11"
  device_apps:
    dependency: "direct main"
    description:
      name: device_apps
      sha256: e84dc74d55749993fd671148cc0bd53096e1be0c268fc364285511b1d8a4c19b
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  device_info_plus:
    dependency: transitive
    description:
      name: device_info_plus
      sha256: "2c35b6d1682b028e42d07b3aee4b98fa62996c10bc12cb651ec856a80d6a761b"
      url: "https://pub.dev"
    source: hosted
    version: "9.0.2"
  device_info_plus_platform_interface:
    dependency: transitive
    description:
      name: device_info_plus_platform_interface
      sha256: "0b04e02b30791224b31969eb1b50d723498f402971bff3630bca2ba839bd1ed2"
      url: "https://pub.dev"
    source: hosted
    version: "7.0.2"
  diacritic:
    dependency: transitive
    description:
      name: diacritic
      sha256: "96db5db6149cbe4aa3cfcbfd170aca9b7648639be7e48025f9d458517f807fe4"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.5"
  dio:
    dependency: "direct main"
    description:
      name: dio
      sha256: "253a18bbd4851fecba42f7343a1df3a9a4c1d31a2c1b37e221086b4fa8c8dbc9"
      url: "https://pub.dev"
    source: hosted
    version: "5.8.0+1"
  dio_web_adapter:
    dependency: transitive
    description:
      name: dio_web_adapter
      sha256: "0a2e95fc6bdeb623bb623fc41e90e6924e9a3bbd65089f9221f83c185366b479"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  dotted_border:
    dependency: "direct main"
    description:
      name: dotted_border
      sha256: "108837e11848ca776c53b30bc870086f84b62ed6e01c503ed976e8f8c7df9c04"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  encrypt:
    dependency: transitive
    description:
      name: encrypt
      sha256: "4fd4e4fdc21b9d7d4141823e1e6515cd94e7b8d84749504c232999fba25d9bbb"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.1"
  event_bus:
    dependency: transitive
    description:
      name: event_bus
      sha256: "1a55e97923769c286d295240048fc180e7b0768902c3c2e869fe059aafa15304"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  eventify:
    dependency: transitive
    description:
      name: eventify
      sha256: b829429f08586cc2001c628e7499e3e3c2493a1d895fd73b00ecb23351aa5a66
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  fading_edge_scrollview:
    dependency: transitive
    description:
      name: fading_edge_scrollview
      sha256: c25c2231652ce774cc31824d0112f11f653881f43d7f5302c05af11942052031
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: ed5337a5660c506388a9f012be0288fb38b49020ce2b45fe1f8b8323fe429f99
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  file:
    dependency: transitive
    description:
      name: file
      sha256: "1b92bec4fc2a72f59a8e15af5f52cd441e4a7860b49499d69dfa817af20e925d"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.4"
  firebase_analytics:
    dependency: transitive
    description:
      name: firebase_analytics
      sha256: "5e277bfb0ffa57a7f0935f6e6f529fcdaf27d65f195d50e5b2bc4a38a2fc00ae"
      url: "https://pub.dev"
    source: hosted
    version: "9.3.8"
  firebase_analytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_analytics_platform_interface
      sha256: "52c9bd117e2468059381aa20269f3df7362b26ee52fb24c8671cda30ea85ac53"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.7"
  firebase_analytics_web:
    dependency: transitive
    description:
      name: firebase_analytics_web
      sha256: ff1aee3a05f34f5c8f8c21a0470441c66958a509f96ccca17fdedaced141768e
      url: "https://pub.dev"
    source: hosted
    version: "0.4.2+7"
  firebase_core:
    dependency: transitive
    description:
      name: firebase_core
      sha256: "4f1d7c13a909e82ff026679c9b8493cdeb35a9c76bc46c42bf9e2240c9e57e80"
      url: "https://pub.dev"
    source: hosted
    version: "1.24.0"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      sha256: b51257a8b4388565cd66062d727d3e60067b5f5cc3390eb0ecd20b8f97741bdb
      url: "https://pub.dev"
    source: hosted
    version: "4.5.1"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      sha256: "839f1b48032a61962792cea1225fae030d4f27163867f181d6d2072dd40acbee"
      url: "https://pub.dev"
    source: hosted
    version: "1.7.3"
  firebase_crashlytics:
    dependency: "direct main"
    description:
      name: firebase_crashlytics
      sha256: "09655d691972d940a89d7bb1bceb145d0051509dcb753319b36a562f96f00cae"
      url: "https://pub.dev"
    source: hosted
    version: "2.9.0"
  firebase_crashlytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_crashlytics_platform_interface
      sha256: "12085451cdad3bf36eb9470b2da3478b6076d6dd2705e5d49690828b1f7a0d02"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.0"
  firebase_messaging:
    dependency: "direct main"
    description:
      name: firebase_messaging
      sha256: "3a2e5c067a52868afbcefcdffa06ef2aa84a6e7bf5c8bc5eeed9d8dd0c7671bc"
      url: "https://pub.dev"
    source: hosted
    version: "12.0.3"
  firebase_messaging_platform_interface:
    dependency: transitive
    description:
      name: firebase_messaging_platform_interface
      sha256: "71f120e8b405a172cb0088a2cd62fd8b77be0f0c942a5bda411b80268c1702f8"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.0"
  firebase_messaging_web:
    dependency: transitive
    description:
      name: firebase_messaging_web
      sha256: b7c5e019c4ecf3a512c4a0f00a4a9c1f260eb11872380a91f35983387488ce94
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: "25517a4deb0c03aa0f32fd12db525856438902d9c16536311e76cdc57b31d7d1"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_background_service:
    dependency: "direct main"
    description:
      name: flutter_background_service
      sha256: "70a1c185b1fa1a44f8f14ecd6c86f6e50366e3562f00b2fa5a54df39b3324d3d"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.0"
  flutter_background_service_android:
    dependency: transitive
    description:
      name: flutter_background_service_android
      sha256: b73d903056240e23a5c56d9e52d3a5d02ae41cb18b2988a97304ae37b2bae4bf
      url: "https://pub.dev"
    source: hosted
    version: "6.3.0"
  flutter_background_service_ios:
    dependency: transitive
    description:
      name: flutter_background_service_ios
      sha256: "6037ffd45c4d019dab0975c7feb1d31012dd697e25edc05505a4a9b0c7dc9fba"
      url: "https://pub.dev"
    source: hosted
    version: "5.0.3"
  flutter_background_service_platform_interface:
    dependency: transitive
    description:
      name: flutter_background_service_platform_interface
      sha256: ca74aa95789a8304f4d3f57f07ba404faa86bed6e415f83e8edea6ad8b904a41
      url: "https://pub.dev"
    source: hosted
    version: "5.1.2"
  flutter_blurhash:
    dependency: transitive
    description:
      name: flutter_blurhash
      sha256: "05001537bd3fac7644fa6558b09ec8c0a3f2eba78c0765f88912882b1331a5c6"
      url: "https://pub.dev"
    source: hosted
    version: "0.7.0"
  flutter_cache_manager:
    dependency: "direct main"
    description:
      name: flutter_cache_manager
      sha256: "8207f27539deb83732fdda03e259349046a39a4c767269285f449ade355d54ba"
      url: "https://pub.dev"
    source: hosted
    version: "3.3.1"
  flutter_dash:
    dependency: "direct main"
    description:
      name: flutter_dash
      sha256: dc282109cc12806853e102a4e8b9d99abb7f527fcedec2ea03860e2500ee6622
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  flutter_datetime_picker:
    dependency: "direct main"
    description:
      name: flutter_datetime_picker
      sha256: "8e695c63c769350e541951227c2775190ec73ceda774a315b1dc9a99d5facfe5"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  flutter_html:
    dependency: transitive
    description:
      name: flutter_html
      sha256: "02ad69e813ecfc0728a455e4bf892b9379983e050722b1dce00192ee2e41d1ee"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0-beta.2"
  flutter_keyboard_visibility:
    dependency: "direct main"
    description:
      name: flutter_keyboard_visibility
      sha256: "4983655c26ab5b959252ee204c2fffa4afeb4413cd030455194ec0caa3b8e7cb"
      url: "https://pub.dev"
    source: hosted
    version: "5.4.1"
  flutter_keyboard_visibility_linux:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_linux
      sha256: "6fba7cd9bb033b6ddd8c2beb4c99ad02d728f1e6e6d9b9446667398b2ac39f08"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  flutter_keyboard_visibility_macos:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_macos
      sha256: c5c49b16fff453dfdafdc16f26bdd8fb8d55812a1d50b0ce25fc8d9f2e53d086
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  flutter_keyboard_visibility_platform_interface:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_platform_interface
      sha256: e43a89845873f7be10cb3884345ceb9aebf00a659f479d1c8f4293fcb37022a4
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  flutter_keyboard_visibility_web:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_web
      sha256: d3771a2e752880c79203f8d80658401d0c998e4183edca05a149f5098ce6e3d1
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  flutter_keyboard_visibility_windows:
    dependency: transitive
    description:
      name: flutter_keyboard_visibility_windows
      sha256: fc4b0f0b6be9b93ae527f3d527fb56ee2d918cd88bbca438c478af7bcfd0ef73
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  flutter_local_notifications:
    dependency: "direct main"
    description:
      name: flutter_local_notifications
      sha256: "674173fd3c9eda9d4c8528da2ce0ea69f161577495a9cc835a2a4ecd7eadeb35"
      url: "https://pub.dev"
    source: hosted
    version: "17.2.4"
  flutter_local_notifications_linux:
    dependency: transitive
    description:
      name: flutter_local_notifications_linux
      sha256: c49bd06165cad9beeb79090b18cd1eb0296f4bf4b23b84426e37dd7c027fc3af
      url: "https://pub.dev"
    source: hosted
    version: "4.0.1"
  flutter_local_notifications_platform_interface:
    dependency: transitive
    description:
      name: flutter_local_notifications_platform_interface
      sha256: "85f8d07fe708c1bdcf45037f2c0109753b26ae077e9d9e899d55971711a4ea66"
      url: "https://pub.dev"
    source: hosted
    version: "7.2.0"
  flutter_localizations:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_ringtone_player:
    dependency: transitive
    description:
      name: flutter_ringtone_player
      sha256: "0b036416fda0654da52221989bd1a8ccd2876cea57f61ecc3a4fc272bd738c67"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  flutter_screenutil:
    dependency: transitive
    description:
      name: flutter_screenutil
      sha256: "0a122936b450324cbdfd51be0819cc6fcebb093eb65585e9cd92263f7a1a8a39"
      url: "https://pub.dev"
    source: hosted
    version: "5.7.0"
  flutter_secure_storage:
    dependency: "direct main"
    description:
      name: flutter_secure_storage
      sha256: "98352186ee7ad3639ccc77ad7924b773ff6883076ab952437d20f18a61f0a7c5"
      url: "https://pub.dev"
    source: hosted
    version: "8.0.0"
  flutter_secure_storage_linux:
    dependency: transitive
    description:
      name: flutter_secure_storage_linux
      sha256: be76c1d24a97d0b98f8b54bce6b481a380a6590df992d0098f868ad54dc8f688
      url: "https://pub.dev"
    source: hosted
    version: "1.2.3"
  flutter_secure_storage_macos:
    dependency: transitive
    description:
      name: flutter_secure_storage_macos
      sha256: "6c0a2795a2d1de26ae202a0d78527d163f4acbb11cde4c75c670f3a0fc064247"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  flutter_secure_storage_platform_interface:
    dependency: transitive
    description:
      name: flutter_secure_storage_platform_interface
      sha256: cf91ad32ce5adef6fba4d736a542baca9daf3beac4db2d04be350b87f69ac4a8
      url: "https://pub.dev"
    source: hosted
    version: "1.1.2"
  flutter_secure_storage_web:
    dependency: transitive
    description:
      name: flutter_secure_storage_web
      sha256: f4ebff989b4f07b2656fb16b47852c0aab9fed9b4ec1c70103368337bc1886a9
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  flutter_secure_storage_windows:
    dependency: transitive
    description:
      name: flutter_secure_storage_windows
      sha256: "38f9501c7cb6f38961ef0e1eacacee2b2d4715c63cc83fe56449c4d3d0b47255"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  flutter_svg:
    dependency: transitive
    description:
      name: flutter_svg
      sha256: f991fdb1533c3caeee0cdc14b04f50f0c3916f0dbcbc05237ccbe4e3c6b93f3f
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_tts:
    dependency: "direct main"
    description:
      name: flutter_tts
      sha256: aed2a00c48c43af043ed81145fd8503ddd793dafa7088ab137dbef81a703e53d
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  flutter_typeahead:
    dependency: transitive
    description:
      name: flutter_typeahead
      sha256: b9942bd5b7611a6ec3f0730c477146cffa4cd4b051077983ba67ddfc9e7ee818
      url: "https://pub.dev"
    source: hosted
    version: "4.8.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  fluttertoast:
    dependency: "direct main"
    description:
      name: fluttertoast
      sha256: "81b68579e23fcbcada2db3d50302813d2371664afe6165bc78148050ab94bf66"
      url: "https://pub.dev"
    source: hosted
    version: "8.2.5"
  freezed_annotation:
    dependency: transitive
    description:
      name: freezed_annotation
      sha256: c3fd9336eb55a38cc1bbd79ab17573113a8deccd0ecbbf926cca3c62803b5c2d
      url: "https://pub.dev"
    source: hosted
    version: "2.4.1"
  frontend_server_client:
    dependency: transitive
    description:
      name: frontend_server_client
      sha256: "408e3ca148b31c20282ad6f37ebfa6f4bdc8fede5b74bc2f08d9d92b55db3612"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.0"
  gallery_saver:
    dependency: transitive
    description:
      name: gallery_saver
      sha256: df8b7e207ca12d64c71e0710a7ee3bc48aa7206d51cc720716fedb1543a66712
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  get:
    dependency: "direct main"
    description:
      name: get
      sha256: "2ba20a47c8f1f233bed775ba2dd0d3ac97b4cf32fc17731b3dfc672b06b0e92a"
      url: "https://pub.dev"
    source: hosted
    version: "4.6.5"
  glob:
    dependency: transitive
    description:
      name: glob
      sha256: "0e7014b3b7d4dac1ca4d6114f82bf1782ee86745b9b42a92c9289c23d8a0ab63"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.2"
  graphs:
    dependency: transitive
    description:
      name: graphs
      sha256: aedc5a15e78fc65a6e23bcd927f24c64dd995062bcd1ca6eda65a3cff92a4d19
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  html:
    dependency: transitive
    description:
      name: html
      sha256: "3a7812d5bcd2894edf53dfaf8cd640876cf6cef50a8f238745c8b8120ea74d3a"
      url: "https://pub.dev"
    source: hosted
    version: "0.15.4"
  http:
    dependency: transitive
    description:
      name: http
      sha256: "5895291c13fa8a3bd82e76d5627f69e0d85ca6a30dcac95c4ea19a5d555879c2"
      url: "https://pub.dev"
    source: hosted
    version: "0.13.6"
  http_multi_server:
    dependency: transitive
    description:
      name: http_multi_server
      sha256: "97486f20f9c2f7be8f514851703d0119c3596d14ea63227af6f7a481ef2b2f8b"
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  image_gallery_saver:
    dependency: transitive
    description:
      name: image_gallery_saver
      sha256: "0aba74216a4d9b0561510cb968015d56b701ba1bd94aace26aacdd8ae5761816"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.3"
  internet_connection_checker:
    dependency: transitive
    description:
      name: internet_connection_checker
      sha256: "1c683e63e89c9ac66a40748b1b20889fd9804980da732bf2b58d6d5456c8e876"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0+1"
  intl:
    dependency: "direct main"
    description:
      name: intl
      sha256: "910f85bce16fb5c6f614e117efa303e85a1731bb0081edf3604a2ae6e9a3cc91"
      url: "https://pub.dev"
    source: hosted
    version: "0.17.0"
  io:
    dependency: transitive
    description:
      name: io
      sha256: "2ec25704aba361659e10e3e5f5d672068d332fc8ac516421d483a11e5cbd061e"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  js:
    dependency: transitive
    description:
      name: js
      sha256: "5528c2f391ededb7775ec1daa69e65a2d61276f7552de2b5f7b8d34ee9fd4ab7"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.5"
  json_annotation:
    dependency: transitive
    description:
      name: json_annotation
      sha256: b10a7b2ff83d83c777edba3c6a0f97045ddadd56c944e1a23a3fdf43a1bf4467
      url: "https://pub.dev"
    source: hosted
    version: "4.8.1"
  json_serializable:
    dependency: "direct main"
    description:
      name: json_serializable
      sha256: "43793352f90efa5d8b251893a63d767b2f7c833120e3cc02adad55eefec04dc7"
      url: "https://pub.dev"
    source: hosted
    version: "6.6.2"
  just_audio:
    dependency: "direct main"
    description:
      name: just_audio
      sha256: b7cb6bbf3750caa924d03f432ba401ec300fd90936b3f73a9b33d58b1e96286b
      url: "https://pub.dev"
    source: hosted
    version: "0.9.37"
  just_audio_platform_interface:
    dependency: transitive
    description:
      name: just_audio_platform_interface
      sha256: "4cd94536af0219fa306205a58e78d67e02b0555283c1c094ee41e402a14a5c4a"
      url: "https://pub.dev"
    source: hosted
    version: "4.5.0"
  just_audio_web:
    dependency: transitive
    description:
      name: just_audio_web
      sha256: "134356b0fe3d898293102b33b5fd618831ffdc72bb7a1b726140abdf22772b70"
      url: "https://pub.dev"
    source: hosted
    version: "0.4.9"
  list_counter:
    dependency: transitive
    description:
      name: list_counter
      sha256: c447ae3dfcd1c55f0152867090e67e219d42fe6d4f2807db4bbe8b8d69912237
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  logger:
    dependency: transitive
    description:
      name: logger
      sha256: "7ad7215c15420a102ec687bb320a7312afd449bac63bfb1c60d9787c27b9767f"
      url: "https://pub.dev"
    source: hosted
    version: "1.4.0"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: "623a88c9594aa774443aa3eb2d41807a48486b5613e67599fb4c41c0ad47c340"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  lottie:
    dependency: "direct main"
    description:
      name: lottie
      sha256: "23522951540d20a57a60202ed7022e6376bed206a4eee1c347a91f58bd57eb9f"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  marquee:
    dependency: transitive
    description:
      name: marquee
      sha256: "4b5243d2804373bdc25fc93d42c3b402d6ec1f4ee8d0bb72276edd04ae7addb8"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.3"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: "16db949ceee371e9b99d22f88fa3a73c4e59fd0afed0bd25fc336eb76c198b72"
      url: "https://pub.dev"
    source: hosted
    version: "0.12.13"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: d92141dc6fe1dad30722f9aa826c7fbc896d021d792f80678280601aff8cf724
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  math_expressions:
    dependency: "direct main"
    description:
      name: math_expressions
      sha256: "3576593617c3870d75728a751f6ec6e606706d44e363f088ac394b5a28a98064"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: "6c268b42ed578a53088d834796959e4a1814b5e9e164f147f580a386e5decf42"
      url: "https://pub.dev"
    source: hosted
    version: "1.8.0"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: e4ff8e8564c03f255408decd16e7899da1733852a9110a58fe6d1b817684a63e
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  mpos_module_base:
    dependency: transitive
    description:
      path: "."
      ref: main
      resolved-ref: "6b82ea8f08f4be32f1353c8de251521024bd9f05"
      url: "https://gitlab.saobang.vn/nextpay1/mobile-library/mpos_module_base"
    source: git
    version: "0.0.1"
  mpos_module_installment:
    dependency: transitive
    description:
      path: "."
      ref: main
      resolved-ref: be53484fe7f5778a27559def597e54ac3f00099d
      url: "https://gitlab.saobang.vn/nextpay1/mobile-library/mpos_modules/mpos_module_installment"
    source: git
    version: "0.0.1"
  mpos_module_link:
    dependency: transitive
    description:
      path: "."
      ref: main
      resolved-ref: "2a731ee9031e07344d850e374596d773d83047c0"
      url: "https://gitlab.saobang.vn/nextpay1/mobile-library/mpos_modules/mpos_module_link"
    source: git
    version: "0.0.1"
  mpos_module_qr:
    dependency: transitive
    description:
      path: "."
      ref: main_new_ui
      resolved-ref: "3e4d05ffcbd16485a4e4f5bd5cbc0554c9f259c5"
      url: "https://gitlab.saobang.vn/nextpay1/mobile-library/mpos_module_qr"
    source: git
    version: "0.0.1"
  mpos_module_static_qr:
    dependency: transitive
    description:
      path: "."
      ref: main
      resolved-ref: "272af60497686f3506049a596cf17b5fde919f41"
      url: "https://gitlab.saobang.vn/nextpay1/mobile-library/mpos_modules/mpos_module_static_qr"
    source: git
    version: "0.0.1"
  mqtt5_client:
    dependency: "direct main"
    description:
      name: mqtt5_client
      sha256: b715e09ab4e20133536dd4df4030477da8ad9624fcff13102c2bd9074d761689
      url: "https://pub.dev"
    source: hosted
    version: "3.4.0"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
  network_info_plus:
    dependency: "direct main"
    description:
      name: network_info_plus
      sha256: "4601b815b1c6a46d84839f65cd774a7d999738471d910fae00d813e9e98b04e1"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0+1"
  network_info_plus_platform_interface:
    dependency: transitive
    description:
      name: network_info_plus_platform_interface
      sha256: "881f5029c5edaf19c616c201d3d8b366c5b1384afd5c1da5a49e4345de82fb8b"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.3"
  nextpay_module_webapp:
    dependency: transitive
    description:
      path: "."
      ref: main
      resolved-ref: df2cc0dd22cfc97842e3d9e44c7f7383a12e6358
      url: "https://gitlab.saobang.vn/nextpay1/mobile-library/nextpay_modules/nextpay_module_webapp"
    source: git
    version: "0.0.1"
  nm:
    dependency: transitive
    description:
      name: nm
      sha256: "2c9aae4127bdc8993206464fcc063611e0e36e72018696cd9631023a31b24254"
      url: "https://pub.dev"
    source: hosted
    version: "0.5.0"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      sha256: "107f3ed1330006a3bea63615e81cf637433f5135a52466c7caa0e7152bca9143"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  package_config:
    dependency: transitive
    description:
      name: package_config
      sha256: "1c5b77ccc91e4823a5af61ee74e6b972db1ef98c2ff5a18d3161c982a55448bd"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  package_info:
    dependency: "direct main"
    description:
      name: package_info
      sha256: "6c07d9d82c69e16afeeeeb6866fe43985a20b3b50df243091bfc4a4ad2b03b75"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  package_info_plus:
    dependency: transitive
    description:
      name: package_info_plus
      sha256: ceb027f6bc6a60674a233b4a90a7658af1aebdea833da0b5b53c1e9821a78c7b
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: "9bc8ba46813a4cc42c66ab781470711781940780fd8beddd0c3da62506d3a6c6"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  path:
    dependency: transitive
    description:
      name: path
      sha256: db9d4f58c908a4ba5953fcee2ae317c94889433e5024c27ce74a37f94267945b
      url: "https://pub.dev"
    source: hosted
    version: "1.8.2"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      sha256: bbb1934c0cbb03091af082a6389ca2080345291ef07a5fa6d6e078ba8682f977
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: e3e67b1629e6f7e8100b367d3db6ba6af4b1f0bb80f64db18ef1fbabd2fa9ccf
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  path_provider:
    dependency: transitive
    description:
      name: path_provider
      sha256: a1aa8aaa2542a6bc57e381f132af822420216c80d4781f7aa085ca3229208aaa
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: e595b98692943b4881b219f0a9e3945118d3c16bd7e2813f98ec6e532d905f72
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "19314d595120f82aca0ba62787d58dde2cc6b5df7d2f0daf72489e38d1b57f2d"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "94b1e0dd80970c1ce43d5d4e050a9918fce4f4a775e6142424c30a29a363265c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: "8bc9f22eee8690981c22aa7fc602f5c85b497a6fb2ceb35ee5a5e5ed85ad8170"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  pbkdf2ns:
    dependency: transitive
    description:
      name: pbkdf2ns
      sha256: "395eb73e1859cd0864de14d97e07b70392eda7f7fd6f0ed7537dd7062dc0650c"
      url: "https://pub.dev"
    source: hosted
    version: "0.0.2"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: "49392a45ced973e8d94a85fdb21293fbb40ba805fc49f2965101ae748a3683b4"
      url: "https://pub.dev"
    source: hosted
    version: "5.1.0"
  pin_code_fields:
    dependency: transitive
    description:
      name: pin_code_fields
      sha256: "4c0db7fbc889e622e7c71ea54b9ee624bb70c7365b532abea0271b17ea75b729"
      url: "https://pub.dev"
    source: hosted
    version: "8.0.1"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "0a279f0707af40c890e80b1e9df8bb761694c074ba7e1d4ab1bc4b728e200b59"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: da3fdfeccc4d4ff2da8f8c556704c08f912542c5fb3cf2233ed75372384a034d
      url: "https://pub.dev"
    source: hosted
    version: "2.1.6"
  pointer_interceptor:
    dependency: transitive
    description:
      name: pointer_interceptor
      sha256: acfcd63c00ec3d5a7894b0e2a875893716d31958fe03f064734dba7dfd9113d9
      url: "https://pub.dev"
    source: hosted
    version: "0.9.3+5"
  pointycastle:
    dependency: transitive
    description:
      name: pointycastle
      sha256: "7c1e5f0d23c9016c5bbd8b1473d0d3fb3fc851b876046039509e18e0c7485f2c"
      url: "https://pub.dev"
    source: hosted
    version: "3.7.3"
  pool:
    dependency: transitive
    description:
      name: pool
      sha256: "20fe868b6314b322ea036ba325e6fc0711a22948856475e2c2b6306e8ab39c2a"
      url: "https://pub.dev"
    source: hosted
    version: "1.5.1"
  pretty_dio_logger:
    dependency: transitive
    description:
      path: "."
      ref: master
      resolved-ref: c084e06c0ea9bebd625f2ce25f9cde7d52cc68af
      url: "https://github.com/DucLQ92/pretty_dio_logger.git"
    source: git
    version: "1.3.1"
  protobuf:
    dependency: transitive
    description:
      name: protobuf
      sha256: "01dd9bd0fa02548bf2ceee13545d4a0ec6046459d847b6b061d8a27237108a08"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  provider:
    dependency: transitive
    description:
      name: provider
      sha256: "4abbd070a04e9ddc287673bf5a030c7ca8b685ff70218720abab8b092f53dd84"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.5"
  pub_semver:
    dependency: transitive
    description:
      name: pub_semver
      sha256: "40d3ab1bbd474c4c2328c91e3a7df8c6dd629b79ece4c4bd04bee496a224fb0c"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  pubspec_parse:
    dependency: transitive
    description:
      name: pubspec_parse
      sha256: c63b2876e58e194e4b0828fcb080ad0e06d051cb607a6be51a9e084f47cb9367
      url: "https://pub.dev"
    source: hosted
    version: "1.2.3"
  pull_to_refresh:
    dependency: "direct main"
    description:
      name: pull_to_refresh
      sha256: bbadd5a931837b57739cf08736bea63167e284e71fb23b218c8c9a6e042aad12
      url: "https://pub.dev"
    source: hosted
    version: "2.0.0"
  qr:
    dependency: transitive
    description:
      name: qr
      sha256: "64957a3930367bf97cc211a5af99551d630f2f4625e38af10edd6b19131b64b3"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.1"
  qr_code_scanner:
    dependency: transitive
    description:
      name: qr_code_scanner
      sha256: f23b68d893505a424f0bd2e324ebea71ed88465d572d26bb8d2e78a4749591fd
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  qr_flutter:
    dependency: "direct main"
    description:
      name: qr_flutter
      sha256: "5095f0fc6e3f71d08adef8feccc8cea4f12eec18a2e31c2e8d82cb6019f4b097"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.0"
  rethink_db_ns:
    dependency: transitive
    description:
      name: rethink_db_ns
      sha256: dac14016cd8a40dfcaed63ac956b11bc8a7a0328f52e35f8a5f8abc644f0ab87
      url: "https://pub.dev"
    source: hosted
    version: "0.0.4"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: "0c7c0cedd93788d996e33041ffecda924cc54389199cde4e6a34b440f50044cb"
      url: "https://pub.dev"
    source: hosted
    version: "0.27.7"
  screen_brightness:
    dependency: transitive
    description:
      name: screen_brightness
      sha256: ed8da4a4511e79422fc1aa88138e920e4008cd312b72cdaa15ccb426c0faaedd
      url: "https://pub.dev"
    source: hosted
    version: "0.2.2+1"
  screen_brightness_android:
    dependency: transitive
    description:
      name: screen_brightness_android
      sha256: "3df10961e3a9e968a5e076fe27e7f4741fa8a1d3950bdeb48cf121ed529d0caf"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.0+2"
  screen_brightness_ios:
    dependency: transitive
    description:
      name: screen_brightness_ios
      sha256: "99adc3ca5490b8294284aad5fcc87f061ad685050e03cf45d3d018fe398fd9a2"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.0"
  screen_brightness_macos:
    dependency: transitive
    description:
      name: screen_brightness_macos
      sha256: "64b34e7e3f4900d7687c8e8fb514246845a73ecec05ab53483ed025bd4a899fd"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.0+1"
  screen_brightness_platform_interface:
    dependency: transitive
    description:
      name: screen_brightness_platform_interface
      sha256: b211d07f0c96637a15fb06f6168617e18030d5d74ad03795dd8547a52717c171
      url: "https://pub.dev"
    source: hosted
    version: "0.1.0"
  screen_brightness_windows:
    dependency: transitive
    description:
      name: screen_brightness_windows
      sha256: "9261bf33d0fc2707d8cf16339ce25768100a65e70af0fcabaf032fc12408ba86"
      url: "https://pub.dev"
    source: hosted
    version: "0.1.3"
  screenshot:
    dependency: "direct main"
    description:
      name: screenshot
      sha256: "30bb9fade6eb2578a1fc2e84f6b184141fc86883cda10988d4500ff00eb728e2"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.0"
  share:
    dependency: "direct main"
    description:
      name: share
      sha256: "97e6403f564ed1051a01534c2fc919cb6e40ea55e60a18ec23cee6e0ce19f4be"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.4"
  share_plus:
    dependency: transitive
    description:
      name: share_plus
      sha256: ed3fcea4f789ed95913328e629c0c53e69e80e08b6c24542f1b3576046c614e8
      url: "https://pub.dev"
    source: hosted
    version: "7.0.2"
  share_plus_platform_interface:
    dependency: transitive
    description:
      name: share_plus_platform_interface
      sha256: "251eb156a8b5fa9ce033747d73535bf53911071f8d3b6f4f0b578505ce0d4496"
      url: "https://pub.dev"
    source: hosted
    version: "3.4.0"
  shared_preferences:
    dependency: "direct main"
    description:
      name: shared_preferences
      sha256: "81429e4481e1ccfb51ede496e916348668fd0921627779233bd24cc3ff6abd02"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.2"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "8568a389334b6e83415b6aae55378e158fbc2314e074983362d20c562780fb06"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: "7bf53a9f2d007329ee6f3df7268fd498f8373602f943c975598bbb34649b62a7"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.4"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "9f2cbcf46d4270ea8be39fa156d86379077c8a5228d9dfdb1164ae0bb93f1faa"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: d4ec5fc9ebb2f2e056c617112aa75dcf92fc2e4faaf2ae999caa297473f75d8a
      url: "https://pub.dev"
    source: hosted
    version: "2.3.1"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: d762709c2bbe80626ecc819143013cc820fa49ca5e363620ee20a8b15a3e3daf
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "841ad54f3c8381c480d0c9b508b89a34036f512482c407e6df7a9c4aa2ef8f59"
      url: "https://pub.dev"
    source: hosted
    version: "2.3.2"
  shelf:
    dependency: transitive
    description:
      name: shelf
      sha256: ad29c505aee705f41a4d8963641f91ac4cee3c8fad5947e033390a7bd8180fa4
      url: "https://pub.dev"
    source: hosted
    version: "1.4.1"
  shelf_web_socket:
    dependency: transitive
    description:
      name: shelf_web_socket
      sha256: "9ca081be41c60190ebcb4766b2486a7d50261db7bd0f5d9615f2d653637a84c1"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.4"
  shimmer:
    dependency: transitive
    description:
      name: shimmer
      sha256: "5f88c883a22e9f9f299e5ba0e4f7e6054857224976a5d9f839d4ebdc94a14ac9"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.0"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  slide_countdown:
    dependency: "direct main"
    description:
      name: slide_countdown
      sha256: "8f45cff7c07b3f11825f52edae71e984888ccba918cef74eb479abeaa4665106"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.0"
  source_gen:
    dependency: transitive
    description:
      name: source_gen
      sha256: "373f96cf5a8744bc9816c1ff41cf5391bbdbe3d7a96fe98c622b6738a8a7bd33"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  source_helper:
    dependency: transitive
    description:
      name: source_helper
      sha256: "6adebc0006c37dd63fe05bca0a929b99f06402fc95aa35bf36d67f5c06de01fd"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.4"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: dd904f795d4b4f3b870833847c461801f6750a9fa8e61ea5ac53f9422b31f250
      url: "https://pub.dev"
    source: hosted
    version: "1.9.1"
  sqflite:
    dependency: transitive
    description:
      name: sqflite
      sha256: b4d6710e1200e96845747e37338ea8a819a12b51689a3bcf31eff0003b37a0b9
      url: "https://pub.dev"
    source: hosted
    version: "2.2.8+4"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      sha256: "8f7603f3f8f126740bc55c4ca2d1027aab4b74a1267a3e31ce51fe40e3b65b8f"
      url: "https://pub.dev"
    source: hosted
    version: "2.4.5+1"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: c3c7d8edb15bee7f0f74debd4b9c5f3c2ea86766fe4178eb2a18eb30a0bdaed5
      url: "https://pub.dev"
    source: hosted
    version: "1.11.0"
  sticky_headers:
    dependency: "direct main"
    description:
      name: sticky_headers
      sha256: "9b3dd2cb0fd6a7038170af3261f855660cbb241cb56c501452cb8deed7023ede"
      url: "https://pub.dev"
    source: hosted
    version: "0.3.0+2"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: "83615bee9045c1d322bbbd1ba209b7a749c2cbcdcb3fdd1df8eb488b3279c1c8"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  stream_duration:
    dependency: transitive
    description:
      name: stream_duration
      sha256: a29e071bf3e5d284e1e7dce98dbbaf547e16543c4802ec4f1a5c74e516c71af5
      url: "https://pub.dev"
    source: hosted
    version: "3.2.1"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: "14a00e794c7c11aa145a170587321aedce29769c08d7f58b1d141da75e3b1c6f"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.0"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: "5fcbd27688af6082f5abd611af56ee575342c30e87541d0245f7ff99faa02c60"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: ad540f65f92caa91bf21dfc8ffb8c589d6e4dc0c2267818b4cc2792857706206
      url: "https://pub.dev"
    source: hosted
    version: "0.4.16"
  timezone:
    dependency: transitive
    description:
      name: timezone
      sha256: "2236ec079a174ce07434e89fcd3fcda430025eb7692244139a9cf54fdcf1fc7d"
      url: "https://pub.dev"
    source: hosted
    version: "0.9.4"
  timing:
    dependency: transitive
    description:
      name: timing
      sha256: "70a3b636575d4163c477e6de42f247a23b315ae20e86442bebe32d3cabf61c32"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.1"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: facc8d6582f16042dd49f2463ff1bd6e2c9ef9f3d5da3d9b087e244a7b564b3c
      url: "https://pub.dev"
    source: hosted
    version: "1.3.2"
  url_launcher:
    dependency: "direct main"
    description:
      name: url_launcher
      sha256: eb1e00ab44303d50dd487aab67ebc575456c146c6af44422f9c13889984c00f3
      url: "https://pub.dev"
    source: hosted
    version: "6.1.11"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "31222ffb0063171b526d3e569079cf1f8b294075ba323443fdc690842bfd4def"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.0"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: "4ac97281cf60e2e8c5cc703b2b28528f9b50c8f7cebc71df6bdf0845f647268a"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.0"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: "9f2d390e096fdbe1e6e6256f97851e51afc2d9c423d3432f1d6a02a8a9a8b9fd"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: b7244901ea3cf489c5335bdacda07264a6e960b1c1b1a9f91e4bc371d9e68234
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "980e8d9af422f477be6948bdfb68df8433be71f5743a188968b0c1b887807e50"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.0"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: ba140138558fcc3eead51a1c42e92a9fb074a1b1149ed3c73e66035b2ccd94f2
      url: "https://pub.dev"
    source: hosted
    version: "2.0.19"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "7754a1ad30ee896b265f8d14078b0513a4dba28d358eabb9d5f339886f4a1adc"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  uuid:
    dependency: "direct main"
    description:
      name: uuid
      sha256: "648e103079f7c64a36dc7d39369cabb358d377078a051d6ae2ad3aa539519313"
      url: "https://pub.dev"
    source: hosted
    version: "3.0.7"
  vector_graphics:
    dependency: transitive
    description:
      name: vector_graphics
      sha256: ea8d3fc7b2e0f35de38a7465063ecfcf03d8217f7962aa2a6717132cb5d43a79
      url: "https://pub.dev"
    source: hosted
    version: "1.1.5"
  vector_graphics_codec:
    dependency: transitive
    description:
      name: vector_graphics_codec
      sha256: a5eaa5d19e123ad4f61c3718ca1ed921c4e6254238d9145f82aa214955d9aced
      url: "https://pub.dev"
    source: hosted
    version: "1.1.5"
  vector_graphics_compiler:
    dependency: transitive
    description:
      name: vector_graphics_compiler
      sha256: "15edc42f7eaa478ce854eaf1fbb9062a899c0e4e56e775dd73b7f4709c97c4ca"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.5"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  wakelock_plus:
    dependency: "direct main"
    description:
      name: wakelock_plus
      sha256: f268ca2116db22e57577fb99d52515a24bdc1d570f12ac18bb762361d43b043d
      url: "https://pub.dev"
    source: hosted
    version: "1.1.4"
  wakelock_plus_platform_interface:
    dependency: transitive
    description:
      name: wakelock_plus_platform_interface
      sha256: "40fabed5da06caff0796dc638e1f07ee395fb18801fbff3255a2372db2d80385"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  watcher:
    dependency: transitive
    description:
      name: watcher
      sha256: "6a7f46926b01ce81bfc339da6a7f20afbe7733eff9846f6d6a5466aa4c6667c0"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.2"
  web_socket_channel:
    dependency: transitive
    description:
      name: web_socket_channel
      sha256: d88238e5eac9a42bb43ca4e721edba3c08c6354d4a53063afaa568516217621b
      url: "https://pub.dev"
    source: hosted
    version: "2.4.0"
  webview_flutter:
    dependency: "direct main"
    description:
      name: webview_flutter
      sha256: "42393b4492e629aa3a88618530a4a00de8bb46e50e7b3993fedbfdc5352f0dbf"
      url: "https://pub.dev"
    source: hosted
    version: "4.4.2"
  webview_flutter_android:
    dependency: transitive
    description:
      name: webview_flutter_android
      sha256: "8326ee235f87605a2bfc444a4abc897f4abc78d83f054ba7d3d1074ce82b4fbf"
      url: "https://pub.dev"
    source: hosted
    version: "3.12.1"
  webview_flutter_platform_interface:
    dependency: transitive
    description:
      name: webview_flutter_platform_interface
      sha256: "6d9213c65f1060116757a7c473247c60f3f7f332cac33dc417c9e362a9a13e4f"
      url: "https://pub.dev"
    source: hosted
    version: "2.6.0"
  webview_flutter_wkwebview:
    dependency: transitive
    description:
      name: webview_flutter_wkwebview
      sha256: accdaaa49a2aca2dc3c3230907988954cdd23fed0a19525d6c9789d380f4dc76
      url: "https://pub.dev"
    source: hosted
    version: "3.9.4"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: "5a751eddf9db89b3e5f9d50c20ab8612296e4e8db69009788d6c8b060a84191c"
      url: "https://pub.dev"
    source: hosted
    version: "4.1.4"
  win32_registry:
    dependency: transitive
    description:
      name: win32_registry
      sha256: "1c52f994bdccb77103a6231ad4ea331a244dbcef5d1f37d8462f713143b0bfae"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.0"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "589ada45ba9e39405c198fe34eb0f607cddb2108527e658136120892beac46d2"
      url: "https://pub.dev"
    source: hosted
    version: "1.0.3"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: "979ee37d622dec6365e2efa4d906c37470995871fe9ae080d967e192d88286b5"
      url: "https://pub.dev"
    source: hosted
    version: "6.2.2"
  yaml:
    dependency: transitive
    description:
      name: yaml
      sha256: "75769501ea3489fca56601ff33454fe45507ea3bfb014161abc3b43ae25989d5"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.2"
sdks:
  dart: ">=2.19.6 <3.0.0"
  flutter: ">=3.7.0"
