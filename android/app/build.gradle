def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

apply plugin: 'com.android.application'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"
apply plugin: 'com.google.gms.google-services'

Properties properties = new Properties()
properties.load(project.rootProject.file('local.properties').newDataInputStream())
def pathFile = properties.getProperty('signing.path')

//def myBuildType = 'SP01'
def myBuildType = 'SP02'


android {
    compileSdkVersion 34
//    buildToolsVersion '30.0.3'

    signingConfigs {
        product {
//          @ntanh
            keyAlias 'mpos.vn-lite'
            keyPassword 'mpos1234560'
            storePassword 'mpos1234560'
            storeFile file(pathFile)
        }
//        dev {
//            keyAlias 'androiddebugkey'
//            keyPassword 'android'
////            @ntanh
//            storeFile file('/Volumes/DataWork/ws_android_studio_ps/debug.keystore')
//            storePassword 'android'
//        }
    }

    defaultConfig {
        applicationId "com.nextpay.mposxs"
        minSdkVersion 24
        targetSdkVersion 32
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName

        resConfigs "en", "vi"
        multiDexEnabled true


        if (myBuildType == 'SP01') {
            // todo note only arm64 for smartpos-sp01
            ndk {
                abiFilters 'arm64-v8a', 'arm64'
            }
        } else if (myBuildType == 'SP02'){
            // todo note only arm64 for smartpos-sp02: mini + N31
            ndk {
//                abiFilters 'armeabi', 'armeabi-v7a'
                abiFilters 'armeabi', 'armeabi-v7a', 'arm64-v8a','x86_64'
            }
        }
    }

    buildTypes {
        debug {
//            resValue "string", "app_name", "mPos-xs-test"
//            versionNameSuffix '_debug'
//            applicationIdSuffix ".test"
            testCoverageEnabled true
        }
        profile {
            initWith debug
            signingConfig signingConfigs.product
        }
        release {
            resValue "string", "app_name", "mPos.vn lite"
            // Signing with the debug keys for now, so `flutter run --release` works.
            // Signing with the debug keys for now, so `flutter run --release` works.

//            shrinkResources true
//            minifyEnabled true
//            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

            // note: sp02 can not use it
//            shrinkResources false
//            minifyEnabled false

            signingConfig signingConfigs.product
            ndk {
                abiFilters 'armeabi', 'armeabi-v7a'
            }
        }
    }
    flavorDimensions "default"
    productFlavors {
        dev {
            resValue "string", "app_name", "MPOS LITE test"
            versionNameSuffix '_debug'
            applicationIdSuffix ".test"
        }
        live {
            resValue "string", "app_name", "LITE STAGING"
            versionNameSuffix '_stating'
            applicationIdSuffix ".staging"
        }
        prod {
            resValue "string", "app_name", "MPOS LITE"
        }
    }
    buildFeatures{
        viewBinding true
    }

    applicationVariants.all { variant ->
        variant.outputs.all { output ->
            def formattedDate = new Date().format('yyyyMMdd')
//            def file = output.outputFile
            def fileName = "MPOS-lite_${variant.name}_${variant.versionName}_${formattedDate}.apk"
            outputFileName = new File("./../build/", fileName)
        }
    }
    lintOptions {
        checkReleaseBuilds false
    }
//    flavorDimensions "default"
//    productFlavors {
//        MposXSDev {
////            applicationId "com.nextpay.mpos.mposxs.dev"
//            applicationId "vn.nextpay.mpos.vab.bank.test"
//            resValue "string", "app_name", "MposXS-DEV"
//        }
//        MposXSProd {
//            resValue "string", "app_name", "MposXS"
//        }
//    }
}

flutter {
    source '../..'
}

dependencies {
    compileOnly files('extern_libs/kozen-sdk-compile-220208.jar')
    implementation files('extern_libs/mpModuleTcp.aar')
    implementation project(':lib_core-1.0.0')
    devImplementation   files("lib-mpos-sdk/core_mpos-1.0.0-dev.aar")
    liveImplementation  files("lib-mpos-sdk/core_mpos-1.0.240827-staging.aar")
    prodImplementation  files("lib-mpos-sdk/core_mpos-1.0.0.aar")

    implementation 'androidx.appcompat:appcompat:1.4.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.annotation:annotation:1.5.0'
//    implementation 'androidx.recyclerview:recyclerview:1.2.1'
    implementation 'androidx.security:security-crypto:1.0.0'
    implementation 'com.google.android.material:material:1.6.0'
    implementation 'com.google.android.gms:play-services-location:21.0.0'

//    implementation 'com.jakewharton:butterknife:10.2.1'
//    annotationProcessor 'com.jakewharton:butterknife-compiler:10.2.1'

    implementation 'com.google.code.gson:gson:2.8.6'
//    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
    implementation 'net.yslibrary.keyboardvisibilityevent:keyboardvisibilityevent:3.0.0-RC2'

    def lottieVersion = "3.5.0"
    implementation "com.airbnb.android:lottie:$lottieVersion"

    implementation 'com.google.crypto.tink:tink-android:1.7.0'
    implementation 'androidx.multidex:multidex:2.0.1'
}
