<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- Default screen margins, per the Android Design guidelines. -->
    <dimen name="activity_horizontal_margin">16dp</dimen>
    <dimen name="activity_vertical_margin">16dp</dimen>
    <dimen name="activity_vertical_margin_haft">8dp</dimen>

    <dimen name="min_width_dialog_base">250dp</dimen>

    <!-- font size -->
    <dimen name="font_tiny">12sp</dimen>
    <dimen name="font_small_mini">13sp</dimen>
    <dimen name="font_small">14sp</dimen>
    <dimen name="font_normal">16sp</dimen>
    <dimen name="font_large">18sp</dimen>
    <dimen name="font_20">20sp</dimen>
    <dimen name="font_big">22sp</dimen>
    <dimen name="font_xlarge">24sp</dimen>

    <dimen name="stroke_mini">0.5dp</dimen>
    <dimen name="stroke">1dp</dimen>
    <dimen name="stroke_2">2dp</dimen>
    <dimen name="stroke_1">-2dp</dimen>
    <dimen name="corner_item">2dp</dimen>
    <dimen name="corner_item_1">3dp</dimen>
    <dimen name="corner_item_3">4dp</dimen>
    <dimen name="corner_item_7">7dp</dimen>

    <dimen name="padding_minimum">3dp</dimen>
    <dimen name="padding_half_item">5dp</dimen>
    <dimen name="padding_item">10dp</dimen>
    <dimen name="padding_medium">15dp</dimen>
    <dimen name="padding_hight">20dp</dimen>
    <dimen name="padding_large">25dp</dimen>
    <dimen name="padding_xhigh">30dp</dimen>

    <dimen name="width_img_bank">90dp</dimen>
    <dimen name="width_left_column">150dp</dimen>
    <dimen name="item_news_width">90dp</dimen>
    <dimen name="item_news_heigh">60dp</dimen>
    <dimen name="item_ic_popup">40dp</dimen>

    <dimen name="logo_integrated_heigh">50dp</dimen>


    <dimen name="appbar_padding_top">8dp</dimen>
    <dimen name="tab_height">48dp</dimen>
    <dimen name="distance_5">5dp</dimen>
    <dimen name="distance_7">7dp</dimen>
    <dimen name="distance_10">10dp</dimen>
    <dimen name="distance_48">48dp</dimen>
    <dimen name="distance_2">2dp</dimen>
    <dimen name="distance_8">8dp</dimen>
    <dimen name="distance_4">4dp</dimen>
    <dimen name="distance_12">12dp</dimen>
    <dimen name="distance_15">15dp</dimen>
    <dimen name="distance_30">30dp</dimen>

    <dimen name="reader_w">130dp</dimen>
    <dimen name="reader_h">180dp</dimen>

    <dimen name="dot">30dp</dimen>

    <!--   printer text size -->
    <dimen name="pts_16">16sp</dimen>
    <dimen name="pts_18">18sp</dimen>
    <dimen name="pts_20">20sp</dimen>
    <dimen name="pts_22">22sp</dimen>

    <!--    printer margin line-->
<!--    <dimen name="printer_margin_line">0dp</dimen>-->
    <dimen name="printer_margin_line">-2dp</dimen>
    <dimen name="printer_margin_column">5dp</dimen>

    <dimen name="padding_xxhight">30dp</dimen>

</resources>