<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/v_root"
    android:layout_width="380dp"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:background="@android:color/white"
    >

    <TextView
        android:id="@+id/tv_remark"
        style="@style/layoutPrintTitleQR"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginBottom="1dp"
        android:gravity="right"
        android:text="remark"
        app:layout_constraintBottom_toTopOf="@+id/v_line2"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/tv_title_remark"
        app:layout_constraintTop_toTopOf="@+id/tv_title_remark" />

    <TextView
        android:id="@+id/tv_title_remark"
        style="@style/layoutPrintTitleQR"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Des/Mô tả"
        app:layout_constraintTop_toBottomOf="@+id/tv_type"
        tools:ignore="MissingConstraints" />

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/imv_logo"
        android:layout_width="220dp"
        android:layout_height="54dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@drawable/logo_mpos_printer"
        android:layout_marginStart="@dimen/padding_xxhight"
        android:layout_marginEnd="@dimen/padding_xxhight"
        />

    <TextView
        android:id="@+id/tv_mc_name"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/imv_logo"
        android:layout_marginTop="@dimen/printer_margin_line"
        android:gravity="center"
        android:textSize="@dimen/pts_20"
        android:textColor="@android:color/black"
        tools:text="NGUU MA VUONG"
        />

    <TextView
        android:id="@+id/tv_mc_address"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_mc_name"
        android:layout_marginTop="@dimen/printer_margin_line"
        android:gravity="center"
        android:textSize="@dimen/pts_20"
        android:textColor="@android:color/black"
        tools:text="Address cua mc nhe, thu 2 dong cai xem sao, mãi không xuống dòng nhỉ"
        />
    <TextView
        android:id="@+id/tv_username"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_mc_address"
        android:layout_marginTop="@dimen/printer_margin_line"
        android:gravity="center"
        android:textSize="@dimen/pts_20"
        android:textColor="@android:color/black"
        tools:text="Username"
        android:visibility="gone"
        />

    <TextView
        android:id="@+id/tv_sale"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_username"
        android:layout_marginTop="5dp"
        android:gravity="center"
        android:textAllCaps="true"
        android:textStyle="bold"
        android:textSize="@dimen/pts_22"
        android:textColor="@android:color/black"
        android:text="sale - thanh toan"
        tools:text="sale - thanh toan"
        />

    <ImageView
        android:id="@+id/v_line1"
        android:layout_width="0dp"
        android:layout_height="2dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_sale"
        android:background="@drawable/line_dashed"
        android:layout_marginTop="@dimen/mp_padding_half_item"
        />

<!--mid-->
    <TextView
        android:id="@+id/tv_title_mid"
        style="@style/layoutPrintTitleQR"
        android:text="MID"
        android:layout_marginTop="@dimen/mp_padding_half_item"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/v_line1" />

    <TextView
        android:id="@+id/tv_mid"
        style="@style/layoutPrintValueQR"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_mid"
        app:layout_constraintTop_toTopOf="@id/tv_title_mid"
        tools:text="000000060104791" />

<!--tid-->
    <TextView
        android:id="@+id/tv_title_tid"
        style="@style/layoutPrintTitleQR"
        android:text="TID"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_mid" />

    <TextView
        android:id="@+id/tv_tid"
        style="@style/layoutPrintValueQR"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_tid"
        app:layout_constraintTop_toTopOf="@id/tv_title_tid"
        tools:text="10122233" />

<!--date time-->
    <TextView
        android:id="@+id/tv_title_time"
        style="@style/layoutPrintTitleQR"
        android:text="Date/Time"

        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_tid" />

    <TextView
        android:id="@+id/tv_time"
        style="@style/layoutPrintValueQR"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_time"
        app:layout_constraintTop_toTopOf="@id/tv_title_time"
        tools:text="06/12/2022 11:26:28" />

<!-- TransId-->
    <TextView
        android:id="@+id/tv_title_invoice"
        style="@style/layoutPrintTitleQR"
        android:text="Transaction ID"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_time" />

    <TextView
        android:id="@+id/tv_trans_id"
        style="@style/layoutPrintValueQR"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_invoice"
        app:layout_constraintTop_toTopOf="@id/tv_title_invoice"
        tools:text="000217" />

<!--Ref No-->
    <TextView
        android:id="@+id/tv_title_ref"
        style="@style/layoutPrintTitleQR"
        android:text="Ref No"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_trans_id" />

    <TextView
        android:id="@+id/tv_ref"
        style="@style/layoutPrintValueQR"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_ref"
        app:layout_constraintTop_toTopOf="@id/tv_title_ref"
        tools:text="167029721662" />

<!--AppCode-->
    <TextView
        android:id="@+id/tv_title_appcode"
        style="@style/layoutPrintTitleQR"
        android:text="AppCode"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_ref" />

    <TextView
        android:id="@+id/tv_appcode"
        style="@style/layoutPrintValueQR"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_appcode"
        app:layout_constraintTop_toTopOf="@id/tv_title_appcode"
        tools:text="167029" />

<!--Type/Loai-->
    <TextView
        android:id="@+id/tv_title_type"
        style="@style/layoutPrintTitleQR"
        android:text="Type/Loai"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_appcode" />

    <TextView
        android:id="@+id/tv_type"
        style="@style/layoutPrintValueQR"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/tv_title_type"
        app:layout_constraintTop_toTopOf="@id/tv_title_type"
        tools:text="VISA" />

    <!--TOTAL-->

    <ImageView
        android:id="@+id/v_line2"
        style="@style/layoutPrintValueQR"
        android:background="@drawable/line_dashed"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_remark" />

    <TextView
        android:id="@+id/tv_title_total"
        style="@style/layoutPrintTitleQR"
        android:text="TOTAL"
        android:textSize="@dimen/pts_22"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tv_remark" />

    <!--    NO REFUND / KHONG HOAN TRA-->

    <TextView
        android:id="@+id/tv_amount"
        style="@style/layoutPrintValueQR"
        android:textSize="@dimen/pts_22"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="1.0"
        app:layout_constraintStart_toEndOf="@id/tv_title_total"
        app:layout_constraintTop_toBottomOf="@+id/v_line2"
        tools:text="1,500,500,500 VND" />

    <TextView
        android:id="@+id/tv_no_refund"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="28dp"
        android:gravity="center"
        android:text="NO REFUND / KHONG HOAN TRA\n*** CUSTOMER COPY/ LIEN DANH CHO KHACH HANG***"
        android:textColor="@color/black"
        android:textSize="@dimen/pts_16"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.533"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_title_total" />

</androidx.constraintlayout.widget.ConstraintLayout>