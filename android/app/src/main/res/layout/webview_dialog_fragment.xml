<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="300dp"
    android:layout_height="400dp"
    android:layout_gravity="center"
    android:orientation="vertical">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:cardCornerRadius="10dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/white"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="10dp">

            <TextView
                android:id="@+id/tv_title"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="Title"
                android:textColor="@android:color/black"
                android:textSize="18sp"
                android:textStyle="bold" />

            <WebView
                android:id="@+id/webView"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:layout_marginVertical="10dp"/>

            <Button
                android:id="@+id/bt_close"
                android:layout_width="150dp"
                android:padding="@dimen/mp_padding_half_item"
                android:layout_height="wrap_content"
                android:background="@drawable/round_button"
                android:textSize="18sp" />

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</RelativeLayout>