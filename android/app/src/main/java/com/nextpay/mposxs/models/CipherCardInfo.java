package com.nextpay.mposxs.models;

import android.text.TextUtils;

import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.DataProcessCard;
import com.mpos.sdk.util.ConstantsPay;

import java.util.Locale;

import static com.nextpay.mposxs.models.ResultReadCard.DECLINE;
import static com.nextpay.mposxs.models.ResultReadCard.SUCCESS;

/**
 * Create by anhnguyen on 11/08/2023
 */
public class CipherCardInfo {

    String cardInterface;
    String cardDataEncrypted;
    String ksn;
    ResultReadCard result;

    public CipherCardInfo(DataError dataError, DataProcessCard dataCard) {
        if (dataError == null && dataCard != null) {
            result = new ResultReadCard(SUCCESS, "");
            if (!TextUtils.isEmpty(dataCard.getmDataEmv())) {
                this.cardDataEncrypted = dataCard.getmDataEmv();
            }
            else if (!TextUtils.isEmpty(dataCard.getmCard())) {
                this.cardDataEncrypted = dataCard.getmCard();
            }
            this.ksn = dataCard.getKsn();
            switch (dataCard.getNameTypeCard()) {
                case ConstantsPay.CARD_MAGSTRIPE:
                case ConstantsPay.CARD_NFC:
                    this.cardInterface = dataCard.getNameTypeCard();
                    break;
                case ConstantsPay.CARD_EMV:
                    this.cardInterface = "ICC";
                    break;
            }
        }
        else {
            String msg;// = dataError.getMsg() + " - " + dataError.getErrorCode();
            if (dataError != null) {
                msg = String.format(Locale.getDefault(), "%s(%d)", dataError.getMsg(), dataError.getErrorCode());
            } else {
                msg = "Can not read card. Please try again.";
            }
            result = new ResultReadCard(DECLINE, msg);
        }
    }
}


class ResultReadCard{

    public static final String DECLINE = "DECLINE";
    public static final String SUCCESS = "SUCCESS";

    String result;
    String msg;

    public ResultReadCard(String result, String msg) {
        this.result = result;
        this.msg = msg;
    }
}