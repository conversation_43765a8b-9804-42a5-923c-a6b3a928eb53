package com.nextpay.mposxs.models;

import java.io.Serializable;

public class DataQrPrint implements Serializable {

    String mid;
    String tid;
    String rrn;
    String amount;
    String timeTrans;
    String mcName;
    String mcAddress;
    String txId;
    String desc;
    String issuerName;
    String authCode;
    String remark;

    public DataQrPrint() {
    }

    public DataQrPrint(String mid, String tid, String rrn, String amount, String timeTrans, String mcName, String mcAddress, String txId, String desc, String issuerName, String authCode) {
        this.mid = mid;
        this.tid = tid;
        this.rrn = rrn;
        this.amount = amount;
        this.timeTrans = timeTrans;
        this.mcName = mcName;
        this.mcAddress = mcAddress;
        this.txId = txId;
        this.desc = desc;
        this.issuerName = issuerName;
        this.authCode = authCode;
    }

    public String getMid() {
        return mid;
    }

    public void setMid(String mid) {
        this.mid = mid;
    }

    public String getTid() {
        return tid;
    }

    public void setTid(String tid) {
        this.tid = tid;
    }

    public String getRrn() {
        return rrn;
    }

    public void setRrn(String rrn) {
        this.rrn = rrn;
    }

    public String getAmount() {
        return amount;
    }

    public void setAmount(String amount) {
        this.amount = amount;
    }

    public String getTimeTrans() {
        return timeTrans;
    }

    public void setTimeTrans(String timeTrans) {
        this.timeTrans = timeTrans;
    }

    public String getMcName() {
        return mcName;
    }

    public void setMcName(String mcName) {
        this.mcName = mcName;
    }

    public String getMcAddress() {
        return mcAddress;
    }

    public void setMcAddress(String mcAddress) {
        this.mcAddress = mcAddress;
    }

    public String getTxId() {
        return txId;
    }

    public void setTxId(String txId) {
        this.txId = txId;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getIssuerName() {
        return issuerName;
    }

    public void setIssuerName(String issuerName) {
        this.issuerName = issuerName;
    }

    public String getAuthCode() {
        return authCode;
    }

    public void setAuthCode(String authCode) {
        this.authCode = authCode;
    }


    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

}
