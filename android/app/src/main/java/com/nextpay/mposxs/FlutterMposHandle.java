package com.nextpay.mposxs;

import android.content.Context;
import android.text.TextUtils;
import android.util.Log;

import androidx.annotation.NonNull;

import com.custom.mdm.CustomAPI;
import com.mpos.sdk.BuildConfig;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.common.MyTextHttpResponseHandler;
import com.mpos.sdk.core.control.LibKozenP5;
import com.mpos.sdk.core.control.LibLoginHandler;
import com.mpos.sdk.core.control.LibLoginHandler.ItfHandlerResultLogin;
import com.mpos.sdk.core.control.LibP20L;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.DataProcessCard;
import com.mpos.sdk.core.model.DataReversalLogin;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.mposinterface.ItfAppendLog;
import com.mpos.sdk.core.network.ApiMultiAcquirerInterface;
import com.mpos.sdk.core.network.MposRestClient;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.MacqUtil;
import com.mpos.sdk.util.Utils;
import com.nextpay.mposxs.models.CipherCardInfo;
import com.nextpay.mposxs.models.ReaderMpos;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.Arrays;

import cz.msebera.android.httpclient.Header;
import cz.msebera.android.httpclient.entity.StringEntity;

/**
 * Create by anhnguyen on 20/06/2023
 */
public class FlutterMposHandle {

    private static final String TAG = "FlutterMposHandle";

    Context context;
    SaveLogController logUtil;
    MainActivity.ItfCallbackFlutter callback;

    String dataResLogin = "";
    LibLoginHandler libLoginHandler;

    String pathGetAllConfig = "/api/users/get-config-all";

    public FlutterMposHandle(@NonNull Context context, @NonNull SaveLogController logUtil, @NonNull MainActivity.ItfCallbackFlutter cb) {
        this.context = context;
        this.logUtil = logUtil;
        this.callback = cb;
    }

    public void processSendApiMacq(String path, String content) {
        Utils.LOGD(TAG, "processSendApiMacq: called with: path = [" + path + "], content = [" + content + "]");
        StringEntity entity = ApiMultiAcquirerInterface.getInstance()
                .buildStringEntity(content);

        MposRestClient.getInstance(context).setPaymentMATimeout().post(context, BuildConfig.URL_MA_BASE + path, entity,
                ConstantsPay.CONTENT_TYPE, new MyTextHttpResponseHandler(context) {

                    @Override
                    public void onFailApi(int statusCode, Header[] headers, String rawJsonData, Throwable throwable) {
                        Utils.LOGD(TAG, "SendApiMacq onFailure() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonData = [" + rawJsonData + "], throwable = [" + throwable + "]");

                        String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonData);

                        logUtil.appendLogRequestApi("onFail  sale SendApiMacq: " + clearData);
                        Utils.LOGD(TAG, "SendApiMacq onFailure: " + clearData);
                        DataError dataError = new DataError();
                        if (statusCode == 0) {
                            dataError.build(statusCode, rawJsonData, context.getString(R.string.error_ma_default_code0, String.valueOf(statusCode)));
                        }
                        else {
                            dataError.build(statusCode, rawJsonData, context.getString(R.string.error_ma_default, String.valueOf(statusCode)));
                        }
                        callbackResult(MyGson.getGson().toJson(dataError));

                    }

                    @Override
                    public void onSuccessApi(int statusCode, Header[] headers, String rawJsonResponse) {
                        Utils.LOGD(TAG, "SendApiMacq onSuccess() called with: statusCode = [" + statusCode + "], headers = [" + Arrays.toString(headers) + "], rawJsonResponse = [" + rawJsonResponse + "]");

                        String clearData = MacqUtil.getInstance().parseAndDecryptData(rawJsonResponse);
                        Utils.LOGD(TAG, "SendApiMacq onSuccess: " + clearData);
                        readMerchantgetAllConfig(path, clearData);
//                        try {
//                            JSONObject jsonObject = new JSONObject(clearData);
//                            Utils.LOGD(TAG, "isPayMoto=" + jsonObject.getString("isPayMoto"));
//                        } catch (Exception e) {
//                            e.printStackTrace();
//                        }
//                        logUtil.appendLogAction("SendApiMacq success=" + clearData);
                        callbackResult(clearData);
                    }

                });
    }

    private void readMerchantgetAllConfig(String path, String clearData) {
        if (path.equals(pathGetAllConfig) && !TextUtils.isEmpty(clearData)) {
            logUtil.appendLogRequestApi("readMerchantConfig macq: ");
            try {
                JSONObject jRoot = new JSONObject(clearData);
                if (libLoginHandler != null) {
                    libLoginHandler.readMerchantConfig(jRoot);
                } else {
                    LibLoginHandler libLoginHandler = new LibLoginHandler(context, ConstantsPay.DEVICE_KOZEN);
                    libLoginHandler.readMerchantConfig(jRoot);
                }

                if (jRoot.has("mores") && DevicesUtil.isSP02()) {
                    JSONObject jMores = jRoot.getJSONObject("mores");
                    if (jMores.has("appIdBootStart")) {
                        String appIdBootStart = jMores.getString("appIdBootStart");
                        if (!TextUtils.isEmpty(appIdBootStart)) {
                            CustomAPI.setBootStartPkgName("");// remove package name before that
                            if (appIdBootStart.contains(".")) {
                                CustomAPI.setBootStartPkgName(appIdBootStart);
                            }
                        }
                    }
                }
//                                configUtils.readConfigFromMpos(context, new JSONObject(clearData));
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
        }
    }


    public void processLoginMacqByUserAndDevice(String user, String sn, String deviceIdentifier) {
        dataResLogin = "";
        libLoginHandler = new LibLoginHandler(context, new ItfHandlerResultLogin() {
            @Override
            public void showLoading(boolean b) {}

            @Override
            public void onFailureLogin(@NonNull DataError dataError, int i) {
                callbackResult(MyGson.getGson().toJson(dataError));
            }

            @Override
            public void onHaveTranWaitSignature(DataReversalLogin dataReversalLogin) {}

            @Override
            public void onSuccessLogin() {
                callbackResult(dataResLogin);
            }

        }, PrefLibTV.getInstance(context).getFlagDevices());
        libLoginHandler.setDeviceIdentifier(deviceIdentifier);
        libLoginHandler.setHandlerDataLogin(s -> dataResLogin = s);
        libLoginHandler.processMultiAcquirer(user, sn);
    }

    public void processCallbackCardData(DataError dataError, DataProcessCard dataProcessCard) {
        CipherCardInfo cipherCardInfo = new CipherCardInfo(dataError, dataProcessCard);
        callbackResult(MyGson.getGson().toJson(cipherCardInfo));
    }
    public void processGetSN() {
        String sn = "";
        int readerType = ConstantsPay.DEVICE_NONE;
        if (DevicesUtil.isP20L()) {
            sn = new LibP20L(context).getSerialNumber();
            readerType = ConstantsPay.DEVICE_P20L;
            PrefLibTV.getInstance(context).setFlagDevices(ConstantsPay.DEVICE_P20L);
        }
        else if(DevicesUtil.isSP02()){
            sn = new LibKozenP5(context).getSerialNumber();
            readerType = ConstantsPay.DEVICE_KOZEN;
            PrefLibTV.getInstance(context).setFlagDevices(ConstantsPay.DEVICE_KOZEN);
        }
        callbackResult(MyGson.getGson().toJson(new ReaderMpos(sn, readerType)));
    }

    private void callbackResult(Object data) {
        if (callback != null) {
            callback.callbackResult(data);
        }
    }

    public void processLoginMacq(String user, String pass, String sn, String deviceIdentifier) {
        dataResLogin = "";
        libLoginHandler = new LibLoginHandler(context, new ItfHandlerResultLogin() {
            @Override
            public void showLoading(boolean b) {}

            @Override
            public void onFailureLogin(@NonNull DataError dataError, int i) {
                callbackResult(MyGson.getGson().toJson(dataError));
            }

            @Override
            public void onHaveTranWaitSignature(DataReversalLogin dataReversalLogin) {}

            @Override
            public void onSuccessLogin() {
                callbackResult(dataResLogin);
            }

        }, PrefLibTV.getInstance(context).getFlagDevices());
        libLoginHandler.setDeviceIdentifier(deviceIdentifier);
        libLoginHandler.setHandlerDataLogin(s -> dataResLogin = s);
        libLoginHandler.processMultiAcquirer(user, sn, pass);
    }
}
