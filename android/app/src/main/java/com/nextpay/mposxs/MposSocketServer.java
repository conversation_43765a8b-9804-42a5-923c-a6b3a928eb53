package com.nextpay.mposxs;

import static android.content.Context.MODE_PRIVATE;
import static com.mpos.mp_module_socket.service.SocketServer.APPROVED;
import static com.mpos.mp_module_socket.service.SocketServer.CANCEL;
import static com.mpos.mp_module_socket.service.SocketServer.CANCEL_ORDER;
import static com.mpos.mp_module_socket.service.SocketServer.FAIL;
import static com.mpos.mp_module_socket.service.SocketServer.ORDER_CODE_CANCEL;
import static com.mpos.mp_module_socket.service.SocketServer.ORDER_CODE_FAIL;
import static com.mpos.mp_module_socket.service.SocketServer.ORDER_CODE_SUCCESS;
import static com.mpos.mp_module_socket.service.SocketServer.RESIGN;
import static com.mpos.mp_module_socket.service.SocketServer.UNABLE_CANCEL;
import static com.mpos.mp_module_socket.service.SocketServer.UPDATE_TRANS;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.media.AudioManager;
import android.media.ToneGenerator;
import android.text.TextUtils;

import com.mpos.mp_module_socket.main.ITYMpSocketManager;
import com.mpos.mp_module_socket.main.MPSocketManager;
import com.mpos.mp_module_socket.model.DataDeposit;
import com.mpos.mp_module_socket.model.DataMoto;
import com.mpos.mp_module_socket.model.DataOrder;
import com.mpos.mp_module_socket.model.DataStatusSocket;
import com.mpos.sdk.core.common.MyGson;
import com.mpos.sdk.core.control.MposIntegrationHelper;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.model.DataError;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.model.ResultPayWrapper;
import com.mpos.sdk.core.model.UserCard;
import com.mpos.sdk.util.Constants;
import com.mpos.sdk.util.Intents;
import com.mpos.sdk.util.Utils;
import com.nextpay.mposxs.models.DataPrePayTcp;

import org.json.JSONException;
import org.json.JSONObject;

public class MposSocketServer {
    private static final String TAG = MposSocketServer.class.getSimpleName();

    public static final String TYPE_CARD            = "CARD";
    public static final String TYPE_QRCODE          = "QRCODE";
    public static final String TYPE_DEPOSIT         = "DEPOSIT";
    public static final String TYPE_MOTO            = "MOTO";
    public static final String TYPE_MOTO_DEPOSIT    = "MOTO_DEPOSIT";
    public static String nameFilterActionPayment = "vn.mpos.emart_action_payment";
    public static String nameFilterActionOrder = "vn.mpos.callback_socket_data";
    private ITYMpSocketManager mpSocketManager;
    private Context context;
    private SaveLogController logUtil;
    private ReceiverTCP receiverTCP;
    private MyReceiverTcpFromSdk myReceiverFromSdk;
    private ItfReceiver itfReceiver;
    private DataPrePayTcp dataPrePayTcp;
    public MposSocketServer(Context context, SaveLogController logUtil, ItfReceiver itfReceiver) {
        this.context = context;
        this.logUtil = logUtil;
        this.itfReceiver = itfReceiver;
    }

    public void initAndStartServiceSocket() {
        mpSocketManager = ITYMpSocketManager.get(context);
        mpSocketManager.startServiceSocket();

        registerReceiverFromSdk();
        registerReceiverFromClient();
    }

    private void closeServiceSocket() {
        if (mpSocketManager != null) {
            mpSocketManager.closeServiceSocket();
            mpSocketManager = null;
        }
    }

    /**  --------    TODO ACTION RECEIVER DATA FROM CLIENT --------  **/
    private void registerReceiverFromClient() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(nameFilterActionOrder);
        receiverTCP = new ReceiverTCP();
        try {
            //Register or UnRegister your broadcast receiver here
            context.registerReceiver(receiverTCP, filter);
        } catch(IllegalArgumentException e) {
            e.printStackTrace();
        }
    }

    private class ReceiverTCP extends BroadcastReceiver {
        @Override
        public void onReceive(Context context, Intent intent) {
            String data = intent.getStringExtra("mp.it.data_soket_received");
            if (!TextUtils.isEmpty(data)) {
                handlerDataReceived(data);
            }
        }
    }

    private void handlerDataReceived(String data) {
        appendLogAct(data);
        DataStatusSocket dataStatusSocket = MyGson.parseJson(data, DataStatusSocket.class);
        if (dataStatusSocket != null) {
            if (dataStatusSocket.getStatusConnect() == MPSocketManager.StatusConnect.CONNECT) {
                ToneGenerator toneGen1 = new ToneGenerator(AudioManager.STREAM_MUSIC, 1000);
                toneGen1.startTone(ToneGenerator.TONE_CDMA_PIP,400);
                switch (dataStatusSocket.getServiceName()) {
                    case ADD_ORDER:
                        DataOrder dataOrder = dataStatusSocket.getDataOrder();
                        if ((dataOrder.getTypePayment() != null) && isSupportMethod(dataOrder.getTypePayment())) {
                            itfReceiver.doReceiverFromClient(MPSocketManager.ServiceName.ADD_ORDER, MyGson.getGson().toJson(dataOrder));
                        } else {
                            handleFailTransaction(new DataError(-1, "Payment method is not supported"));
                        }
                        break;
                    case ADD_DEPOSIT:
                        DataDeposit dataDeposit = dataStatusSocket.getDataDeposit();
                        itfReceiver.doReceiverFromClient(MPSocketManager.ServiceName.ADD_DEPOSIT, MyGson.getGson().toJson(dataDeposit));
                        break;
                    case SET_FINAL_DEPOSIT:
                        DataDeposit dataSetFinalDeposit = dataStatusSocket.getDataDeposit();
                        itfReceiver.doReceiverFromClient(MPSocketManager.ServiceName.SET_FINAL_DEPOSIT, MyGson.getGson().toJson(dataSetFinalDeposit));
                        break;
                    case FINISH_DEPOSIT:
                        DataDeposit dataFinishDeposit = dataStatusSocket.getDataDeposit();
                        itfReceiver.doReceiverFromClient(MPSocketManager.ServiceName.FINISH_DEPOSIT, MyGson.getGson().toJson(dataFinishDeposit));
                        break;
                    case ADD_MOTO:
                        DataMoto dataMoto = dataStatusSocket.getDataMoto();
                        itfReceiver.doReceiverFromClient(MPSocketManager.ServiceName.ADD_MOTO, MyGson.getGson().toJson(dataMoto));
                        break;
                    case CANCEL_ORDER:
                        processCancelTrans();
                        break;
                    case VOID_TRANS:
                        if (PrefLibTV.getInstance(context).getPermitVoid()) {
                            itfReceiver.doReceiverFromClient(MPSocketManager.ServiceName.VOID_TRANS, MyGson.getGson().toJson(dataStatusSocket.getDataVoid()));
                        } else {
                            handleFailTransaction(new DataError(-1, "Void Transaction not supported"));
                        }
                        break;
                    case NONE:
//                        callbackTrans(dataStatusSocket.getErrMsg());
                        handleFailTransaction(new DataError(-1, dataStatusSocket.getErrMsg()));
                        break;
                }
            }
        }
    }

    private void processCancelTrans() {             // put order Cancel to SDK
        Utils.LOGD(TAG, "processCancelTrans");
        appendLogAct("processCancelTrans");
        Intent intentState = new Intent(Intents.nameFilterActionCancelOrder);
        intentState.putExtra(Intents.EXTRA_DATA_BC_CANCEL_ORDER, true);
        context.sendBroadcast(intentState);

        itfReceiver.doReceiverFromClient(MPSocketManager.ServiceName.CANCEL_ORDER, "");
    }

    /*  --------   TODO ACTION RECEIVER DATA FROM APP --------  */
    private void registerReceiverFromSdk() {
        Utils.LOGD(TAG, "registerReceiver");
        logUtil.appendLogException("registerReceiver ");
        IntentFilter filter = new IntentFilter();
        filter.addAction(nameFilterActionPayment);
        myReceiverFromSdk = new MyReceiverTcpFromSdk(itfReceiver);
        try {
            //Register or UnRegister your broadcast receiver here
            context.registerReceiver(myReceiverFromSdk, filter);
        } catch(IllegalArgumentException e) {
            Utils.LOGE(TAG, "IllegalArgumentException " + e.getMessage());
            e.printStackTrace();
        }
    }

    private class MyReceiverTcpFromSdk extends BroadcastReceiver {
        ItfReceiver itfReceiver;

        public MyReceiverTcpFromSdk(ItfReceiver itfReceiver) {
            this.itfReceiver = itfReceiver;
        }

        @Override
        public void onReceive(Context context, Intent intent) {
            String actionPayment = intent.getStringExtra(Intents.EXTRA_DATA_BC_ACTION);
            Utils.LOGD(TAG, "actionPayment: " + actionPayment);
            itfReceiver.doReceiverActionPayment(actionPayment); // TODO không cần put statePayment qua bên kia, xử lý các statePayment in here
            switch (actionPayment) {
                case Constants.AP_ACTION_START:
                case com.mpos.sdk.util.Constants.AP_ACTION_END:
                    itfReceiver.doReceiverActionPayment(actionPayment);
                    break;
                case com.mpos.sdk.util.Constants.AP_ACTION_CANCEL_ORDER:
                    //todo handler callback cancel
                    String contentResult = intent.getStringExtra(Intents.EXTRA_DATA_BC_RESULT);
                    boolean isCancel = intent.getBooleanExtra(Intents.EXTRA_DATA_BC_CANCEL_ORDER, false);
                    ResultPayWrapper resultWrapper = MyGson.parseJson(contentResult, ResultPayWrapper.class);
                    if (resultWrapper.getResult() != null) {
//                        dataPrePayTcp = convertToDataPrePay(resultWrapper);
//                        String content = createDataCancelOrder(isCancel);
//                        callbackTrans(content);
                        handlerCancelTrans(resultWrapper, isCancel);
                    }
                    break;
                case com.mpos.sdk.util.Constants.AP_ACTION_BEFORE_SIGNATURE:
                case com.mpos.sdk.util.Constants.AP_ACTION_CALLBACK_DATA:
//                    if (!presenter.isGotoHomeEnter) {
//                        handleActionResultPayment(intent, actionPayment);
//                    }
                    handleActionResultPayment(intent, actionPayment);
                    break;
                case Constants.AP_ACTION_ERR:
                    String result = intent.getStringExtra(Intents.EXTRA_DATA_BC_RESULT);
                    Utils.LOGD(TAG, "ufo result: " +result);
                    try {
                        JSONObject object = new JSONObject(result);
                        String errCode = object.getString("responseCode");
                        String errMessage = object.getString("responseMsg");
                        handleFailTransaction(new DataError(Integer.parseInt(errCode), errMessage));
                    } catch (JSONException e) {
                        e.printStackTrace();
                        handleFailTransaction(new DataError(ORDER_CODE_FAIL));
                    }
                    break;
            }
        }

        private void handleActionResultPayment(Intent intent, String action) {
            String contentResult = intent.getStringExtra(Intents.EXTRA_DATA_BC_RESULT);
            ResultPayWrapper resultWrapper = MyGson.parseJson(contentResult, ResultPayWrapper.class);
            boolean isDataBeforeSignature = action.equals(com.mpos.sdk.util.Constants.AP_ACTION_BEFORE_SIGNATURE);
            handleResultPay(resultWrapper, isDataBeforeSignature);
        }
    }

    public void handlerCancelTrans(ResultPayWrapper resultWrapper, boolean isCancel) {
        dataPrePayTcp = convertToDataPrePay(resultWrapper);
        String content = createDataCancelOrder(isCancel);
        callbackTrans(content);
    }

    public void handleResultPay(ResultPayWrapper resultWrapper, boolean isDataBeforeSignature) {
        if (resultWrapper.getResult() != null) {
            dataPrePayTcp = convertToDataPrePay(resultWrapper);
            if (MposIntegrationHelper.TRANS_STATUS_APPROVED.equals(resultWrapper.getResult().status)) {
                handleSuccessTransaction(resultWrapper.getResult().method);
            }
            else if (MposIntegrationHelper.TRANS_STATUS_CANCEL.equals(resultWrapper.getResult().status)) {
                handleCancelTransaction();
            }
            else if (MposIntegrationHelper.TRANS_STATUS_ERROR.equals(resultWrapper.getResult().status)) {
                handleFailTransaction(resultWrapper.getResult().error);
//                                    createDataPrePayTcp(FAIL, resultWrapper.getResult().error);
            }
            else if (MposIntegrationHelper.TRANS_STATUS_UNSIGN.equals(resultWrapper.getResult().status)) {
                handleFailTransaction(new DataError(resultWrapper.getResult().transStatus, "Ký xác nhận giao dịch chưa thành công."));
            } else {
                handleFailTransaction(new DataError(ORDER_CODE_FAIL));
            }
        }
    }

    private void handleFailTransaction(DataError dataError) {
        String content = createDataPrePayTcp(FAIL, dataError);
        callbackTrans(content);
    }

    private void handleCancelTransaction() {
        String content = createDataPrePayTcp(CANCEL, null);
        callbackTrans(content);
    }

    private void handleSuccessTransaction(String method) {
        Utils.LOGD(TAG, "handlerSuccessTransaction method= " + method);
        String content;
        if ((method != null) && (method.equals("RESIGN"))) {
            content = createDataPrePayTcp(RESIGN, null);
        } else {
            content = createDataPrePayTcp(APPROVED, null);
        }
        callbackTrans(content);
    }

    public void handleCallbackVoidTrans(String content) {
        Utils.LOGD(TAG, "handlerSuccessTransaction method= " + content);
        callbackTrans(content);
    }

    private DataPrePayTcp convertToDataPrePay(ResultPayWrapper resultPay) {
        UserCard userCard = resultPay.getUserCard();
        DataPrePayTcp dataPrePayEMart = new DataPrePayTcp(String.valueOf(userCard.amountAuthorized), resultPay.getResult().paymentIdentifier);
        dataPrePayEMart.setAuthCode(userCard.authCode);
        dataPrePayEMart.setPan(userCard.pan);
        dataPrePayEMart.setCardHolderName(userCard.cardHolderName);
        dataPrePayEMart.setTransDate(userCard.transactionDate);
        dataPrePayEMart.setTransCode(resultPay.getResult().transId);
        dataPrePayEMart.setIssuerCode(userCard.applicationLabel);
        String orderID = "";
        if ((userCard.applicationLabel != null) && userCard.applicationLabel.contains("QR")) {
            orderID = resultPay.getResult().paymentIdentifier;
        } else {
            try {
                String mcDataInfo = resultPay.getResult().paymentIdentifier.split("\\[")[1].split("\\]")[0];
                orderID = mcDataInfo.split(",")[1];
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        Utils.LOGD(TAG, "orderId= " + orderID);
        dataPrePayEMart.setOrderId(orderID);
        if (resultPay.getWfInfo() != null && resultPay.getWfInfo().getDepositRefCode() != null && resultPay.getWfInfo().getWfId() != null) {
            dataPrePayEMart.setDepositID(resultPay.getWfInfo().getDepositRefCode());
            dataPrePayEMart.setWfId(resultPay.getWfInfo().getWfId());
        }
        return dataPrePayEMart;
    }

    private String createDataPrePayTcp(String status, DataError dataError) {
        if (dataPrePayTcp == null) {
            dataPrePayTcp = new DataPrePayTcp();
        }
        dataPrePayTcp.setServiceName(UPDATE_TRANS);
        dataPrePayTcp.setMuid(PrefLibTV.getInstance(context).getUserId());
        if (status.equals(APPROVED)) {
            dataPrePayTcp.setStatus(status);
            dataPrePayTcp.setResponseCode(ORDER_CODE_SUCCESS);
        } else if (status.equals(CANCEL)) {
            dataPrePayTcp.setStatus(CANCEL);
            dataPrePayTcp.setResponseCode(ORDER_CODE_CANCEL);
        } else if (status.equals(FAIL)) {
            dataPrePayTcp.setStatus(FAIL);
            dataPrePayTcp.setResponseCode(String.valueOf(dataError.getErrorCode()));
            dataPrePayTcp.setResponseMess(dataError.getMsg());
        }

        return MyGson.getGson().toJson(dataPrePayTcp);
    }

    private String createDataCancelOrder(boolean isCancel) {
        dataPrePayTcp.setServiceName(CANCEL_ORDER);
        if (isCancel) {
            dataPrePayTcp.setStatus(CANCEL);
        } else {
            dataPrePayTcp.setStatus(UNABLE_CANCEL);
        }
        dataPrePayTcp.setResponseCode(ORDER_CODE_CANCEL);
        dataPrePayTcp.setMuid(PrefLibTV.getInstance(context).getUserId());
        return MyGson.getGson().toJson(dataPrePayTcp);
    }

    public void callbackTrans(String msg) {
        Utils.LOGD(TAG, "callbackTrans " + msg);
        appendLogAct("step end msg callback: " + msg);

        Intent intentState = new Intent("vn.mpos.return_socket_data");
        intentState.putExtra("vn.mpos.socket_result", msg);
        context.sendBroadcast(intentState);

        dataPrePayTcp = null;

        SharedPreferences pref = context.getSharedPreferences("SOCKET", MODE_PRIVATE);
        String currentLogs = pref.getString("logs", "");
        Utils.LOGD(TAG, "currentLogs= " + currentLogs);
        appendLogAct(currentLogs);
        pref.edit().clear().apply();

        logUtil.saveLog();
        logUtil.pushLog();
    }

    public void closeSocketAndService() {
        closeServiceSocket();
        if (receiverTCP != null) {
            context.unregisterReceiver(receiverTCP);
        }
        if (myReceiverFromSdk != null) {
            context.unregisterReceiver(myReceiverFromSdk);
        }
    }


    /**  --------   OTHER FUNCTION --------  **/

    boolean isSupportMethod(String methodPayment) {
        if (methodPayment.equals(TYPE_CARD)
                || methodPayment.equals(TYPE_QRCODE)) {
            return true;
        }

        return false;
    }

    private void appendLogAct(String log) {
        logUtil.appendLogAction(log);
    }

    public interface ItfReceiver {
        void doReceiverFromClient(MPSocketManager.ServiceName serviceName, String msg);
        void doReceiverActionPayment(String action);
    }
}
