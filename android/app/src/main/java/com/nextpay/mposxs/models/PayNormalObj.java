package com.nextpay.mposxs.models;

import com.google.gson.annotations.SerializedName;

import java.io.Serializable;

/**
 * Create by xthuan
 */
public class PayNormalObj implements Serializable {

//    private String amount;
    private String rnAmount;
//    private String description;
    private String rnDescription;
    private String rnPhone;
    private String rnIdentity;
    @SerializedName("rnEmail")
    private String rnEmail;
    private String udid;


    public String getRnAmount() {
        return rnAmount;
    }

    public void setRnAmount(String rnAmount) {
        this.rnAmount = rnAmount;
    }

    public String getRnDescription() {
        return rnDescription;
    }

    public void setRnDescription(String rnDescription) {
        this.rnDescription = rnDescription;
    }

    public String getRnPhone() {
        return rnPhone;
    }

    public void setRnPhone(String rnPhone) {
        this.rnPhone = rnPhone;
    }

    public String getRnIdentity() {
        return rnIdentity;
    }

    public void setRnIdentity(String rnIdentity) {
        this.rnIdentity = rnIdentity;
    }

    public String getRnEmail() {
        return rnEmail;
    }

    public void setRnEmail(String rnEmail) {
        this.rnEmail = rnEmail;
    }

    public String getUdid() {
        return udid;
    }

    public void setUdid(String udid) {
        this.udid = udid;
    }

}
