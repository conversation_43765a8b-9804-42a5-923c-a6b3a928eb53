package com.nextpay.mposxs.models;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class DataSummaryDeposit {
    @SerializedName("timeFrom")
    @Expose
    public String timeFrom;
    @SerializedName("timeTo")
    @Expose
    public String timeTo;
    @SerializedName("totalTransApproved")
    @Expose
    public String totalTransApproved;
    @SerializedName("totalTransSettled")
    @Expose
    public String totalTransSettled;
    @SerializedName("totalTransVoid")
    @Expose
    public String totalTransVoid;
    @SerializedName("amountTransApproved")
    @Expose
    public String amountTransApproved;
    @SerializedName("amountTransSettled")
    @Expose
    public String amountTransSettled;
    @SerializedName("amountTransVoid")
    @Expose
    public String amountTransVoid;

    public DataSummaryDeposit() {
    }

    public DataSummaryDeposit(String timeFrom, String timeTo, String totalTransApproved, String totalTransSettled, String totalTransVoid, String amountTransApproved, String amountTransSettled, String amountTransVoid) {
        this.timeFrom = timeFrom;
        this.timeTo = timeTo;
        this.totalTransApproved = totalTransApproved;
        this.totalTransSettled = totalTransSettled;
        this.totalTransVoid = totalTransVoid;
        this.amountTransApproved = amountTransApproved;
        this.amountTransSettled = amountTransSettled;
        this.amountTransVoid = amountTransVoid;
    }

    public String getTimeFrom() {
        return timeFrom;
    }

    public void setTimeFrom(String timeFrom) {
        this.timeFrom = timeFrom;
    }

    public String getTimeTo() {
        return timeTo;
    }

    public void setTimeTo(String timeTo) {
        this.timeTo = timeTo;
    }

    public String getTotalTransApproved() {
        return totalTransApproved;
    }

    public void setTotalTransApproved(String totalTransApproved) {
        this.totalTransApproved = totalTransApproved;
    }

    public String getTotalTransSettled() {
        return totalTransSettled;
    }

    public void setTotalTransSettled(String totalTransSettled) {
        this.totalTransSettled = totalTransSettled;
    }

    public String getTotalTransVoid() {
        return totalTransVoid;
    }

    public void setTotalTransVoid(String totalTransVoid) {
        this.totalTransVoid = totalTransVoid;
    }

    public String getAmountTransApproved() {
        return amountTransApproved;
    }

    public void setAmountTransApproved(String amountTransApproved) {
        this.amountTransApproved = amountTransApproved;
    }

    public String getAmountTransSettled() {
        return amountTransSettled;
    }

    public void setAmountTransSettled(String amountTransSettled) {
        this.amountTransSettled = amountTransSettled;
    }

    public String getAmountTransVoid() {
        return amountTransVoid;
    }

    public void setAmountTransVoid(String amountTransVoid) {
        this.amountTransVoid = amountTransVoid;
    }
}
