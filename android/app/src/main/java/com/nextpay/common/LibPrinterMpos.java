package com.nextpay.common;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.content.ContextCompat;

import com.mpos.sdk.core.control.LibPrinterBluetooth;
import com.mpos.sdk.core.control.LibPrinterP8;
import com.mpos.sdk.core.control.LibPrinterS85;
import com.mpos.sdk.core.control.LibPrinterSp01;
import com.mpos.sdk.core.control.LibPrinterXpP210;
import com.mpos.sdk.core.control.SaveLogController;
import com.mpos.sdk.core.model.DataPay;
import com.mpos.sdk.core.model.LanguageCode;
import com.mpos.sdk.core.model.PrefLibTV;
import com.mpos.sdk.core.modelma.WfDetailRes;
import com.mpos.sdk.util.ConstantsPay;
import com.mpos.sdk.util.DevicesUtil;
import com.mpos.sdk.util.MyDialogShow;
import com.mpos.sdk.util.MyTextUtils;
import com.mpos.sdk.util.Utils;
import com.mpos.sdk.view.BluetoothDeviceList;
import com.nextpay.mposxs.R;
import com.nextpay.mposxs.databinding.LayoutPrintFormatBinding;
import com.nextpay.mposxs.databinding.LayoutPrintFormatSummaryBinding;
import com.nextpay.mposxs.databinding.LayoutPrintQrFormatRemarkBinding;
import com.nextpay.mposxs.models.DataQrPrint;
import com.nextpay.mposxs.models.DataSummaryDeposit;
import com.pos.sdk.printer.POIPrinterManager;
import com.pos.sdk.printer.models.BitmapPrintLine;
import com.pos.sdk.printer.models.PrintLine;
import com.pos.sdk.printer.models.TextPrintLine;

import java.util.Locale;

/**
 * Create by anhnguyen on 07/07/2022
 */
public class LibPrinterMpos {

    private static final String TAG = "LibPrinterMpos";

    // printer
    private LibPrinterSp01 libPrinter;
    private LibPrinterS85 libPrinterS85;
    private LibPrinterXpP210 libPrinterXpP210;
    private LibPrinterP8 libPrinterP8;
    private LibPrinterBluetooth libPrinterBluetooth;
//    String receiptDownloaded;
//
//    MyProgressDialog mPgdl;
//    BaseActivityPrint.ItfCallbackPrinter callbackPrinter;

    MyP20LPrinterListener myP20LPrinterListener;

    Context context;
    Context contextRes;

    long startTime = 0;
    SaveLogController logController;

    public LibPrinterMpos(@NonNull Context context, SaveLogController logController) {
        this.context = context;
        this.logController = logController;
        initPrinterMpos();
    }

    public void initPrinterMpos(){
        String modelMposDevice = "";
        if (DevicesUtil.isP20L()) {
            modelMposDevice = "P20L";
            myP20LPrinterListener = new MyP20LPrinterListener(context);
            if (libPrinter == null) {
                try {
                    libPrinter = new LibPrinterSp01(context, myP20LPrinterListener);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        else if (DevicesUtil.isSP02P8() || DevicesUtil.isSP02P10()) {
            modelMposDevice = "SP02P8 || SP02P10";
            if (libPrinterP8 == null) {
                try {
                    libPrinterP8 = new LibPrinterP8(context);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
        else if (DevicesUtil.isSP02()) {
            modelMposDevice = "SP02";
            String addressBluetooth = PrefLibTV.getInstance(context).getBluetoothAddressPrinter();
            String nameBluetooth = PrefLibTV.getInstance(context).getBluetoothNamePrinter();
            if (!TextUtils.isEmpty(addressBluetooth) && !TextUtils.isEmpty(addressBluetooth)) {
                if (nameBluetooth.equalsIgnoreCase("S85")) {
                    initPrinterS85();
                }else if (nameBluetooth.equalsIgnoreCase(LibPrinterXpP210.NAME_XPRINTER)) {
                    initPrinterXPP210();
                }
            }
            else if (libPrinterBluetooth == null) {
                PrefLibTV.getInstance(context).createBluetoothAddressPrinter("", "");
                libPrinterBluetooth = new LibPrinterS85(context);
            }
        }
        appendLog("initPrinterMpos_modelMposDevice: " + modelMposDevice);
    }

    private void initPrinterXPP210() {
        if (libPrinterXpP210 == null) {
            libPrinterXpP210 = new LibPrinterXpP210(context);
        }
    }

    private void initPrinterS85() {
        if (libPrinterS85 == null) {
            libPrinterS85 = new LibPrinterS85(context);
        }
    }

    public void setLanguageByLocate(@NonNull LanguageCode languageCode) {
        contextRes = changeContextByLanguage(context, new Locale(languageCode.getLanguageCode()));
    }

    private Context changeContextByLanguage(Context context, Locale requestedLocale) {
        Configuration config = new Configuration(context.getResources().getConfiguration());
        config.setLocale(requestedLocale);
        return context.createConfigurationContext(config);
    }

    public void actionPrintImageBase64(String imageBase64) {
        Bitmap bitmap = MyTextUtils.convertTextBase64ToBitmap(imageBase64);
        boolean isP20L = DevicesUtil.isP20L();
        boolean isSP02P8 = DevicesUtil.isSP02P8();
        boolean isSP02P10 = DevicesUtil.isSP02P10();
        boolean isSP02 = DevicesUtil.isSP02();
        boolean isSP02P12 = DevicesUtil.isSP02P12();
        appendLog("actionPrintImageBase64: isP20L=" + isP20L + " isSP02P8=" + isSP02P8 + " isSP02P10=" + isSP02P10 + " isSP02=" + isSP02 + " isSP02P12=" + isSP02P12);
        if (DevicesUtil.isP20L()) {
            actionPrintP20L(bitmap);
        }
        else if (DevicesUtil.isSP02P8() || DevicesUtil.isSP02P10()) {
            actionPrintP8(bitmap);
        }
        else if (DevicesUtil.isSP02() || DevicesUtil.isSP02P12()) {
            actionPrintBitmap(bitmap);
        }
    }

    private void actionPrintBitmap(Bitmap bitmap) {
        if (bitmap == null) {
            return;
        }
        if (libPrinterS85 != null) {
            actionPrintS85(bitmap);
        }
        else if (libPrinterXpP210 != null) {
            actionPrintXpP210(bitmap);
        }
        else if (libPrinterBluetooth != null) {
            libPrinterBluetooth.checkBluetooth();
        }
        else if (libPrinterP8 != null) {
            actionPrintP8(bitmap);
        }
    }

    public void printReceiptQRCodeWithRemarkLayout(DataQrPrint dataQr) {
        Bitmap bitmap = buildBitmapQRCodeWithRemark(dataQr);
        actionPrintBitmap(bitmap);
    }
    private Bitmap buildBitmapQRCodeWithRemark(DataQrPrint dataQr) {
        LayoutPrintQrFormatRemarkBinding binding = LayoutPrintQrFormatRemarkBinding.inflate(LayoutInflater.from(context));

        if (dataQr != null) {
            binding.tvMcName.setText(Utils.buildDataReceipt(dataQr.getMcName()));
            binding.tvMcAddress.setText(Utils.buildDataReceipt(dataQr.getMcAddress()));

            binding.tvMid.setText(Utils.buildDataReceipt(dataQr.getMid()));
            binding.tvTid.setText(Utils.buildDataReceipt(dataQr.getTid()));

            String dateTime = "";
            try {
                dateTime = Utils.convertTimestamp(Long.parseLong(Utils.buildDataReceipt(dataQr.getTimeTrans())), 3);
            } catch (Exception e) {
                e.printStackTrace();
            }
            binding.tvTime.setText(dateTime);

            binding.tvTransId.setText(Utils.buildDataReceipt(dataQr.getTxId()));
            binding.tvRef.setText(Utils.buildDataReceipt(dataQr.getRrn()));
            binding.tvAppcode.setText(Utils.buildDataReceipt(dataQr.getAuthCode()));
            binding.tvType.setText(Utils.buildDataReceipt(dataQr.getIssuerName()));
            binding.tvRemark.setText(Utils.buildDataReceipt(dataQr.getRemark()));
            binding.tvAmount.setText(String.format("%s VND", Utils.zenMoney(Utils.buildDataReceipt(dataQr.getAmount()))));
        }
        return getBitmapFromView(binding.getRoot());
    }


    private void actionPrintXpP210(Bitmap bitmap) {
        libPrinterXpP210.setBitmapPrintAfterConnected(bitmap);
        if (libPrinterXpP210.isConnected()) {
            libPrinterXpP210.printBitmap(bitmap);
            return;
        }
        libPrinterXpP210.checkBluetooth();
    }

    private void actionPrintS85(Bitmap bitmap) {
        if (libPrinterS85 == null) {
            appendLog("libPrinter not init");
            libPrinterS85 = new LibPrinterS85(context);
        }

        libPrinterS85.setTypePrint(LibPrinterS85.TYPE_PRINT_RECEIPT);
        libPrinterS85.setBitmapPrintAfterConnected(bitmap);

        if (libPrinterS85.isConnected()) {
            Utils.LOGD(TAG, "actionPrint: is connected ");

            long timeConvertBitmap = System.currentTimeMillis() - startTime;
            Utils.LOGD(TAG, "actionPrint: timeConvertBitmap=" + timeConvertBitmap);
            appendLog("actionPrint: timeConvertBitmap=" + timeConvertBitmap);
            startTime = System.currentTimeMillis();

            libPrinterS85.printReceipt(libPrinterS85.getmPrinter(), bitmap);

            long timePrint = System.currentTimeMillis() - startTime;
            Utils.LOGD(TAG, "actionPrint: timePrint=" + timePrint);
            appendLog( "actionPrint: timePrint=" + timePrint);
            return;
        }
        libPrinterS85.checkBluetooth();
    }


    private void actionPrintP20L(Bitmap bitmap) {
        Utils.LOGD(TAG, "actionPrint: ");
        if (libPrinter == null) {
            appendLog("libPrinter not init");
            return;
        }
        myP20LPrinterListener.resetHaveShowError();

        long timeConvertBitmap = System.currentTimeMillis() - startTime;
        Utils.LOGD(TAG, "actionPrint: timeConvertBitmap=" + timeConvertBitmap);
        appendLog("actionPrint: timeConvertBitmap=" + timeConvertBitmap);
        startTime = System.currentTimeMillis();

        libPrinter.printBitmap(bitmap);
        libPrinter.pushPage(4);

        long timePrint = System.currentTimeMillis() - startTime;
        Utils.LOGD(TAG, "actionPrint: timePrint=" + timePrint);
        appendLog( "actionPrint: timePrint=" + timePrint);
    }

    private void actionPrintP8(Bitmap bitmap) {
        if (libPrinterP8 == null) {
            appendLog("libPrinter not init");
            return;
        }

        POIPrinterManager printerManager = libPrinterP8.getPrinterManager();
        printerManager.open();
        printerManager.addPrintLine(new BitmapPrintLine(bitmap, PrintLine.CENTER));
        printerManager.addPrintLine(new TextPrintLine("\n"));
        printerManager.addPrintLine(new TextPrintLine("\n"));
        printerManager.beginPrint(new POIPrinterManager.IPrinterListener() {
            @Override
            public void onStart() {
                Utils.LOGD(TAG, "onStart: --->");
            }

            @Override
            public void onFinish() {
                Utils.LOGD(TAG, "onFinish: ---->");
                closePrinter();
            }

            @Override
            public void onError(int errorCode, String s) {
                Utils.LOGD(TAG, "onError() called with: i = [" + errorCode + "], s = [" + s + "]");
                closePrinter();
                // errorCode in POIPrinterManager.
                String msgError;
                switch (errorCode) {
                    case POIPrinterManager.ERROR_NO_PAPER:
                        msgError = getString(R.string.error_printer_out_of_paper);
                        break;
                    case POIPrinterManager.ERROR_OVERHEAT:
                        msgError = getString(R.string.error_printer_p8_overheat);
                        break;
                    default:
                        msgError = getString(R.string.error_printer_default);
                        break;
                }
                MyDialogShow.showDialogError(getString(R.string.mp_notice), msgError, context);
            }

            public void closePrinter() {
                printerManager.close();
            }
        });
    }

    public void handlerResultSelectBluetoothPrinter(int requestCode, int resultCode, @Nullable Intent data) {
        if (resultCode == Activity.RESULT_OK && requestCode == LibPrinterS85.REQUEST_ENABLE_BT) {
            if (libPrinterBluetooth == null) {
                libPrinterBluetooth = new LibPrinterS85(context);
            }
            libPrinterBluetooth.showDeviceList();
        }
        else if (resultCode == Activity.RESULT_OK && requestCode == LibPrinterS85.REQUEST_CONNECT_DEVICE) {
            String devicesName = data.getExtras().getString(BluetoothDeviceList.EXTRA_DEVICE_NAME);

            if (devicesName.equalsIgnoreCase("S85")) {
                initPrinterS85();
//                handleResultSelectS85(data);
                libPrinterS85.handlerDeviceSelected(data);
            }
            else if (devicesName.equalsIgnoreCase(LibPrinterXpP210.NAME_XPRINTER)) {
                initPrinterXPP210();
//                handleResultSelectXPP210(data);
                if (!libPrinterXpP210.isConnected()) {
                    libPrinterXpP210.handlerDeviceSelected(data);
                }
            }
        }
    }

    public void handlerResultConnectS85(int requestCode, int resultCode, @Nullable Intent data) {
        if (libPrinterS85 == null) {
            appendLog("S85 not init");
            return;
        }
        if (requestCode == LibPrinterS85.REQUEST_ENABLE_BT) {
            // check BT khi in S85
            libPrinterS85.showDeviceList();
        }
        else if ((resultCode == Activity.RESULT_OK) && (requestCode == LibPrinterS85.REQUEST_CONNECT_DEVICE)) {
            // connect S85 sau khi nhận devicesAddress s85
            libPrinterS85.handlerDeviceSelected(data);
        }
    }

    public void printReceiptOfflineByLayout(DataPay dataPay) {
        Bitmap bitmap = buildBitmapOfflineByLayout(dataPay);
        actionPrintBitmap(bitmap);
    }

    public Bitmap buildBitmapOfflineByLayout(DataPay dataPay) {
        LayoutPrintFormatBinding binding = LayoutPrintFormatBinding.inflate(LayoutInflater.from(context));

        if (dataPay.getWfDetailRes() != null) {
            putDataMacqToView(dataPay, binding);
        }
        else {
            Utils.LOGD(TAG, "buildBitmapOfflineByLayout: no data print");
            appendLog("buildBitmapOfflineByLayout: no data print");
        }

        return getBitmapFromView(binding.getRoot());
    }

    private void putDataMacqToView(DataPay dataPay, LayoutPrintFormatBinding binding) {
        Utils.LOGD(TAG, "putDataMacqToView: ====>");
        if (dataPay != null && dataPay.getWfDetailRes() != null) {
            WfDetailRes detailRes = dataPay.getWfDetailRes();

            // logo by type from server
//            String receiptLogo = DataStoreApp.getInstance().getDataByKey(DataStoreApp.receiptLogoMacq, String.class, "");
//            if ("MPOS_VCB".equalsIgnoreCase(receiptLogo)) {
//                Utils.LOGD(TAG, "=> set logo printer: VCB + mpos");
//                binding.imvLogo.setBackgroundDrawable(ContextCompat.getDrawable(context, R.drawable.logo_vcb_mpos_printer));
//                ViewGroup.LayoutParams layoutParams = binding.imvLogo.getLayoutParams();
//                layoutParams.width = 600;
//                binding.imvLogo.setLayoutParams(layoutParams);
//
//                ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) binding.imvLogo.getLayoutParams();
//                params.setMarginStart(0);
//                params.setMarginEnd(0);
//                binding.imvLogo.setLayoutParams(params);
//            }
//            else if (ConstantsPay.VPBANK.equalsIgnoreCase(detailRes.getAcquirer())) {
//                Utils.LOGD(TAG, "=> set logo printer: VPB + mpos");
//                binding.imvLogo.setBackgroundDrawable(ContextCompat.getDrawable(context, R.drawable.logo_vpb_mpos_printer));
//                ViewGroup.LayoutParams layoutParams = binding.imvLogo.getLayoutParams();
//                layoutParams.width = 600;
//                binding.imvLogo.setLayoutParams(layoutParams);
//
//                ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) binding.imvLogo.getLayoutParams();
//                params.setMarginStart(0);
//                params.setMarginEnd(0);
//                binding.imvLogo.setLayoutParams(params);
//            }

            binding.tvMcName.setText(Utils.buildDataReceipt(detailRes.getMerchantName()));
            binding.tvMcAddress.setText(Utils.buildDataReceipt(detailRes.getMerchantAddress()));
            binding.tvUsername.setText(Utils.buildDataReceipt(PrefLibTV.getInstance(context).getUserId()));

            binding.tvMid.setText(Utils.buildDataReceipt(detailRes.getMid()));
            binding.tvTid.setText(Utils.buildDataReceipt(detailRes.getTid()));

            String dateTime = "";
            try {
                dateTime = Utils.convertTimestamp(Long.parseLong(Utils.buildDataReceipt(detailRes.getTransactionDate())), 3);
            } catch (Exception e) {
                e.printStackTrace();
            }
            binding.tvTime.setText(dateTime);

            String latLng = com.nextpay.util.MyTextUtils.formatLatLng6digits(Utils.buildDataReceipt(detailRes.getLatitude()))
                    + "/" + com.nextpay.util.MyTextUtils.formatLatLng6digits(Utils.buildDataReceipt(detailRes.getLongitude()));
            binding.tvLatlng.setText(latLng);
            binding.tvBatch.setText(Utils.buildDataReceipt(detailRes.getBatchNo()));
            binding.tvInvoice.setText(Utils.buildDataReceipt(detailRes.getInvoiceNo()));
            binding.tvRef.setText(Utils.buildDataReceipt(detailRes.getRrn()));
            binding.tvAppcode.setText(Utils.buildDataReceipt(detailRes.getAuthCode()));
            binding.tvPan.setText(Utils.buildDataReceipt(detailRes.getPan()));
            binding.tvCardHoldername.setText(Utils.buildDataReceipt(detailRes.getCardHolderName()));
            binding.tvType.setText(Utils.buildDataReceipt(detailRes.getIssuerCode()));
            binding.tvAppl.setText(Utils.buildDataReceipt(detailRes.getAppl()));
            binding.tvAid.setText(Utils.buildDataReceipt(detailRes.getAid()));
            binding.tvArqc.setText(Utils.buildDataReceipt(detailRes.getArqc()));

            if (detailRes.getTrxType().equals(ConstantsPay.TRX_TYPE_PAY_DEPOSIT)) {
                binding.tvAmountDeposit.setVisibility(View.VISIBLE);
                binding.tvTitleAmountDeposit.setVisibility(View.VISIBLE);
                binding.tvTitleIdDeposit.setVisibility(View.VISIBLE);
                binding.tvIdDeposit.setVisibility(View.VISIBLE);

                binding.tvAmountDeposit.setText(String.format("%s VND", Utils.zenMoney(Utils.buildDataReceipt(detailRes.getAmountDeposit()))));
                binding.tvIdDeposit.setText(Utils.buildDataReceipt(detailRes.getDepositRefCode()));
                binding.tvSale.setText(context.getString(R.string.title_deposit_receipt));
            }

            String desc = detailRes.getDescription();
            if (TextUtils.isEmpty(desc) && !TextUtils.isEmpty(dataPay.getDesc())) {
                desc = dataPay.getDesc();
            }
            binding.tvDesc.setText(Utils.buildDataReceipt(desc));

            binding.tvAmount.setText(String.format("%s VND", Utils.zenMoney(Utils.buildDataReceipt(detailRes.getAmount()))));

            if (detailRes.getStatus().equals("VOIDED")) {
                binding.tvSale.setText("VOIDED - HUY");
            }

            if (detailRes.isFlagNoSignature()) {

                binding.tvNoRequiredSignature.setVisibility(View.VISIBLE);

                binding.tvTitleSignature.setVisibility(View.GONE);
                binding.imvSignature.setVisibility(View.GONE);
                binding.tvCardHoldernameBottom.setVisibility(View.GONE);
            } else {
                MyTextUtils myTextUtils = new MyTextUtils();
                Bitmap bitmap = myTextUtils.convertTextBase64ToBitmap(dataPay.getSignatureBase64());

                binding.imvSignature.setImageBitmap(bitmap);
                binding.tvCardHoldernameBottom.setText(Utils.buildDataReceipt(detailRes.getCardHolderName()));
            }
        }
    }


    public void printReceiptSummaryDepositOfflineByLayout(DataSummaryDeposit dataSummaryDeposit) {
        Bitmap bitmap = buildBitmapDepositOfflineByLayout(dataSummaryDeposit);

        actionPrintBitmap(bitmap);
    }

    public Bitmap buildBitmapDepositOfflineByLayout(DataSummaryDeposit dataSummaryDeposit) {
        LayoutPrintFormatSummaryBinding binding = LayoutPrintFormatSummaryBinding.inflate(LayoutInflater.from(context));
        putDataSummaryDepositToView(dataSummaryDeposit, binding);

        return getBitmapFromView(binding.getRoot());
    }

    private void putDataSummaryDepositToView(DataSummaryDeposit dataPay, LayoutPrintFormatSummaryBinding binding) {
        binding.tvUsername.setText(PrefLibTV.getInstance(context).getUserId());
//        binding.tvMcName.setText(DataStoreApp.getInstance().getMerchantName());

        binding.tvStartTime.setText(dataPay.timeFrom);
        binding.tvEndTime.setText(dataPay.timeTo);

        if (!TextUtils.isEmpty(dataPay.totalTransApproved)) {
            binding.tvSummaryTotalTransApprove.setText(dataPay.totalTransApproved);
        }

        if (!TextUtils.isEmpty(dataPay.amountTransApproved)) {
            binding.tvSummaryTotalAmountApprove.setText(String.format("%s d", Utils.zenMoney(Utils.buildDataReceipt(dataPay.amountTransApproved))));
        }

        if (!TextUtils.isEmpty(dataPay.totalTransSettled)) {
            binding.tvSummaryTotalTransSettle.setText(dataPay.totalTransSettled);
        }
        if (!TextUtils.isEmpty(dataPay.amountTransSettled)) {
            binding.tvSummaryTotalAmountSettle.setText(String.format("%s d", Utils.zenMoney(Utils.buildDataReceipt(dataPay.amountTransSettled))));
        }
        if (!TextUtils.isEmpty(dataPay.totalTransVoid)) {
            binding.tvSummaryTotalTransVoid.setText(dataPay.totalTransVoid);
        }
        if (!TextUtils.isEmpty(dataPay.amountTransVoid)) {
            binding.tvSummaryTotalAmountVoid.setText(String.format("%s d", Utils.zenMoney(Utils.buildDataReceipt(dataPay.amountTransVoid))));
        }
    }

    public Bitmap getBitmapFromView(View view) {
        view.measure(View.MeasureSpec.UNSPECIFIED, View.MeasureSpec.UNSPECIFIED);
        Bitmap bitmap = Bitmap.createBitmap(view.getMeasuredWidth(), view.getMeasuredHeight(),
                Bitmap.Config.ARGB_8888);

        Canvas canvas = new Canvas(bitmap);
        view.layout(0, 0, view.getMeasuredWidth(), view.getMeasuredHeight());
        view.draw(canvas);

        int widthPaper = 380;
        int heightPaper = widthPaper * bitmap.getHeight() / bitmap.getWidth();
        bitmap = Bitmap.createScaledBitmap(bitmap, widthPaper, heightPaper, true);

        return bitmap;
    }


    public void disconnectPrinter() {
        if (libPrinter != null) {
            libPrinter.disconnectPrinter();
        }

        if (libPrinterS85 != null) {
            libPrinterS85.disconnectPrinter();
        }

        if (libPrinterP8 != null) {
            libPrinterP8.disconnectPrinter();
        }

        if (libPrinterXpP210 != null) {
            libPrinterXpP210.disconnectPrinter();
        }
    }

    private String getString(int stringId) {
        return contextRes != null ? contextRes.getString(stringId) : context.getString(stringId);
    }

    private void appendLog(String msg) {
        if (logController != null) {
            logController.appendLogAction(msg);
        }
    }
}
