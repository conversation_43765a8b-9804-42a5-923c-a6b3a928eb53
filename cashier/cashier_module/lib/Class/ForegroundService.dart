
import 'dart:async';
import 'package:flutter_background_service/flutter_background_service.dart';
import 'package:flutter_background_service_android/flutter_background_service_android.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';

class ForegroundService{
  static final FlutterBackgroundService _service = FlutterBackgroundService();

  static Future<void> initializeService() async {
    await _service.configure(
      androidConfiguration: AndroidConfiguration(
        onStart: onStart,
        isForegroundMode: true,
        autoStart: true,
        initialNotificationTitle: "PushPayment Service",
        initialNotificationContent: "Service is running in the background",
      ),
      iosConfiguration: IosConfiguration(),
    );
  }

  static void startService() {
    _service.startService();
  }

  static void stopService() async {
    final isRunning = await _service.isRunning();
    if (isRunning) {
      _service.invoke('stopService');
    }
  }

  static void onStart(ServiceInstance service) {
    if (service is AndroidServiceInstance) {
      service.setAsForegroundService();
    }
    service.on('stopService').listen((event) async {
      await service.stopSelf();
    });
    Timer.periodic(const Duration(seconds: 5), (timer) async {
      if (service is AndroidServiceInstance) {
        timer.cancel();
      }
      service.invoke('update', {'current_time': DateTime.now().toIso8601String()});
    });
  }
}
