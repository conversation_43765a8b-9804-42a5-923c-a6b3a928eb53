import 'dart:convert';
import 'dart:math';

import 'package:cashiermodule/model_instance/app_configuration.dart';
import 'package:crypto/crypto.dart' as crypto;
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:uuid/uuid.dart';

import 'app_validation.dart';

class TextUtils{

  static String soundPath = "lib/resource/sounds/";

    static String formatAmount(double amount) {
        var oCcy = new NumberFormat.currency(locale: 'vi_VN', symbol: '');
        return oCcy.format(amount).replaceAll(" ", "");
    }

    static String formatAmountDynamicObj(dynamic objAmount) {
        if (objAmount != null){
            var amountTemp = getCleanedNumber(objAmount.toString());
            var amount = double.parse(amountTemp);
            var oCcy = new NumberFormat.currency(locale: 'vi_VN', symbol: '');
            return oCcy.format(amount).replaceAll(" ", "");
        }
        return "";
    }

    static String getFilePath(String filePath){
        var urlResouce = filePath;
        var appConfig = AppConfiguration();
        if (appConfig.appType == AppType.MODULE){
          urlResouce = filePath;
        }else if (appConfig.appType == AppType.FLUTTER_APP){
          urlResouce = "cashier_module/"+filePath;
        }else if (appConfig.appType == AppType.PACKAGE){
          urlResouce = "packages/cashiermodule/"+filePath;
        }else if (appConfig.appType == AppType.MPOS_LITE){
          urlResouce = "packages/cashiermodule/"+filePath;
        }else{
          urlResouce = "packages/cashiermodule/"+filePath;
        }
        return urlResouce;
    }

    static String getCleanedNumber(String text) {
        RegExp regExp = RegExp(r"[^0-9]");
        return text.replaceAll(regExp, '');
    }

    static String getImage(String image){
        var urlResouce = "lib/resource/images/"+image;
        var appConfig = AppConfiguration();
        if (appConfig.appType == AppType.MODULE){
            urlResouce = "lib/resource/images/"+image;
        }else if (appConfig.appType == AppType.FLUTTER_APP){
            urlResouce = "cashier_module/lib/resource/images/"+image;
        }else if (appConfig.appType == AppType.PACKAGE){
            urlResouce = "packages/cashiermodule/lib/resource/images/"+image;
        }else if (appConfig.appType == AppType.MPOS_LITE){
            urlResouce = "packages/cashiermodule/lib/resource/images/"+image;
        }else{
            urlResouce = "packages/cashiermodule/lib/resource/images/"+image;
        }
        return urlResouce;
    }

  static String getImageSVG(String image) {
    if (!image.endsWith('.svg')) {
      image += '.svg';
    }
    var urlResouce = "lib/resource/images/svg/" + image;
    var appConfig = AppConfiguration();
    if (appConfig.appType == AppType.MODULE) {
      urlResouce = "lib/resource/images/svg/" + image;
    } else if (appConfig.appType == AppType.FLUTTER_APP) {
      urlResouce = "cashier_module/lib/resource/images/svg/" + image;
    } else if (appConfig.appType == AppType.PACKAGE) {
      urlResouce = "packages/cashiermodule/lib/resource/images/svg/" + image;
    } else if (appConfig.appType == AppType.MPOS_LITE) {
      urlResouce = "packages/cashiermodule/lib/resource/images/svg/" + image;
    } else {
      urlResouce = "packages/cashiermodule/lib/resource/images/svg/" + image;
    }
    return urlResouce;
  }

    static String getImageLottie(String image){
        var urlResouce = "lib/resource/images/lottie/"+image;
        var appConfig = AppConfiguration();
        if (appConfig.appType == AppType.MODULE){
            urlResouce = "lib/resource/images/lottie/"+image;
        }else if (appConfig.appType == AppType.FLUTTER_APP){
            urlResouce = "cashier_module/lib/resource/images/lottie/"+image;
        }else if (appConfig.appType == AppType.PACKAGE){
            urlResouce = "packages/cashiermodule/lib/resource/images/lottie/"+image;
        }else if (appConfig.appType == AppType.MPOS_LITE){
            urlResouce = "packages/cashiermodule/lib/resource/images/lottie/"+image;
        }else{
            urlResouce = "packages/cashiermodule/lib/resource/images/lottie/"+image;
        }
        return urlResouce;
    }

    static String generatePaymentIdentifer(){
        var uuid = Uuid();
        DateTime now = DateTime.now();
        String timeLog = DateFormat('yyyyMMdd-hhmmss').format(now);
        String udid = "";
        try {
            udid = uuid.v1();
        } on PlatformException catch (e) {
            udid = timeLog;
        }
        return '$udid-${AppConfiguration().merchantId}';
    }

    static String paddingWithLength(String input, int length){
        if (input.length < length){
            do{
                input = input + "0";
            }while(input.length == length);
        }
        return input;
    }

    static String currencyFormat(String amountValue){
        var amounTemp = amountValue.replaceAll(RegExp(r'\B(?=(\d{3})+(?!\d))'), '.');
        return amounTemp;
    }

    static int getColorFromHex(String hexColor) {
        hexColor = hexColor.toUpperCase().replaceAll("#", "");
        if (hexColor.length == 6) {
            hexColor = "FF" + hexColor;
        }
        return int.parse(hexColor, radix: 16);
    }

    static String cardNumberFormat(String textInput){
        var text = textInput;
        var buffer = StringBuffer();
        for (int i = 0; i < text.length; i++) {
            buffer.write(text[i]);
            var nonZeroIndex = i + 1;
            if (nonZeroIndex % 4 == 0 && nonZeroIndex != text.length) {
                buffer.write(' '); // Add double spaces.
            }
        }
        var string = buffer.toString();
        return string;
    }

    static String parserTimeFormat(int timeStamp){
        return DateFormat('HH:mm  dd/MM/yyyy').format(DateTime.fromMillisecondsSinceEpoch(timeStamp));
    }

    static String getTimeNow(){
        return DateFormat("HH:mm:ss dd/MM/yyyy").format(DateTime.now());
    }

    static const _chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890';
    static const _charsNumber = '123456789';
    Random _rnd = Random();
    String getRandomString(int length) => String.fromCharCodes(Iterable.generate(
        length, (_) => _chars.codeUnitAt(_rnd.nextInt(_chars.length))));
    String getRandomNumber(int length) => String.fromCharCodes(Iterable.generate(
        length, (_) => _charsNumber.codeUnitAt(_rnd.nextInt(_charsNumber.length))));

    static String generateMd5(String input) {
        print("BEFORE MD5: $input");
        var md5 = crypto.md5;
        return md5.convert(utf8.encode(input)).toString();
    }
    static String generateSha256(String input) {
        print("BEFORE MD5: $input");
        var sha256 = crypto.sha256;
        return sha256.convert(utf8.encode(input)).toString();
    }

    static String getStringFromBytes(ByteData data) {
        final buffer = data.buffer;
        var list = buffer.asUint8List(data.offsetInBytes, data.lengthInBytes);
        return utf8.decode(list);
    }

    static String removeCharWithPositon(int from, int to, String orderIdTemp) {
        if (orderIdTemp.isEmpty){
            print("COULD_NOT_REMOVE_CHAR: $orderIdTemp");
            return orderIdTemp;
        }
        try {
            return orderIdTemp = orderIdTemp.replaceRange(from, to, "");
        }catch (exception){
            throw Exception(exception.toString());
        }
    }

    static formattedTime({required int timeInSecond}) {
        int sec = timeInSecond % 60;
        int min = (timeInSecond / 60).floor();
        String minute = min.toString().length <= 1 ? "0$min" : "$min";
        String second = sec.toString().length <= 1 ? "0$sec" : "$sec";
        return "$minute : ${second}s";
    }

    static bool isBase64(String str) {
        try {
            base64.decode(str);
            return true;
        } catch (e) {
            return false;
        }
    }

    static String getOrderWithUdid(String udid) {
      String orderIdTemp = "";
      if (udid.contains("PUSHPAYMENT")) {
        int from = udid.indexOf("[") + 1;
        int end = udid.indexOf("]");
        var temp = udid.substring(from, end);
        var lstTemp = temp.split(",");
        try {
          if (lstTemp.length >= 4) {
            for (int index = 1; index < lstTemp.length - 2; index++) {
              orderIdTemp = orderIdTemp + "," + lstTemp[index];
            }
            orderIdTemp = TextUtils.removeCharWithPositon(0, 1, orderIdTemp);
          }
        } catch (e) {}

      }
      return orderIdTemp;
    }

  static String formatCurrency(dynamic number) {
    if (isNullEmptyFalseOrZero(number) || !isNumeric(number)) {
      return '0';
    }
    dynamic numberConvert;
    if (number is String) {
      numberConvert = int.tryParse(number) ?? double.tryParse(number);
    } else {
      numberConvert = number;
    }
    return NumberFormat("#,###", "vi_VN").format(numberConvert ?? 0);
  }

}