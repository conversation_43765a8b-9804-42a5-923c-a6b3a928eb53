import 'dart:async';
import 'dart:convert';
import 'dart:developer' as developer;
import 'dart:io';
import 'dart:math' as math;
import 'dart:math';

import 'package:cashiermodule/BaseService/mpos_request.dart';
import 'package:cashiermodule/Class/SpeakerHandler.dart';
import 'package:cashiermodule/Model/amount_limit.dart';
import 'package:cashiermodule/Model/common_config.dart';
import 'package:cashiermodule/Model/device_mpos.dart';
import 'package:cashiermodule/Model/errorResponse.dart';
import 'package:cashiermodule/Model/trans_result.dart';
import 'package:cashiermodule/Pages/account_page/account_controller.dart';
import 'package:cashiermodule/Pages/account_page/account_page.dart';
import 'package:cashiermodule/Pages/atm360/controller/atm360_list_service_controller.dart';
import 'package:cashiermodule/Pages/atm360/screen/atm360_list_service_screen.dart';
import 'package:cashiermodule/Pages/home_new/controller/swipe_card_controller.dart';
import 'package:cashiermodule/Pages/home_new/lib/snapping_sheet/sheet_position_data.dart';
import 'package:cashiermodule/Pages/home_new/widget/customer_info_input.dart';
import 'package:cashiermodule/Pages/home_new/widget/enter_amount_widget.dart';
import 'package:cashiermodule/Pages/home_new/widget/mp_botom_sheet.dart';
import 'package:cashiermodule/Pages/notification/extension/notification_extension.dart';
import 'package:cashiermodule/Pages/push_payment_page/class/setting_model.dart';
import 'package:cashiermodule/Pages/service_transaction/controllers/service_transactions_controller.dart';
import 'package:cashiermodule/Pages/service_transaction/pages/service_transactions_page.dart';
import 'package:cashiermodule/Utilities/LocalizationCustom.dart';
import 'package:cashiermodule/Utilities/Logger.dart';
import 'package:cashiermodule/Utilities/NativeBridge.dart';
import 'package:cashiermodule/Utilities/TextUtils.dart';
import 'package:cashiermodule/Utilities/configuration.dart';
import 'package:cashiermodule/Utilities/constants.dart';
import 'package:cashiermodule/Utilities/ga_util.dart';
import 'package:cashiermodule/app/app_route.dart';
import 'package:cashiermodule/controller/app_controller.dart';
import 'package:cashiermodule/extension/extension_string_utils.dart';
import 'package:cashiermodule/model_instance/app_configuration.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mpos_module_base/mpos_module_base.dart';
import 'package:mpos_module_base/mpos_module_base_master.dart';
import 'package:mpos_module_base/mpos_module_base_model.dart';
import 'package:mpos_module_base/mpos_module_base_widget.dart';
import 'package:mpos_module_base/src/session/session_data.dart' as module;
import 'package:mpos_module_installment/mpos_module_installment.dart';
import 'package:mpos_module_link/mpos_module_link.dart';
import 'package:mpos_module_qr/mpos_module_qr.dart';
import 'package:nextpay_module_webapp/nextpay_module_webapp.dart';
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';

import '../../../BaseService/base_service.dart';
import '../../../Utilities/app_utils.dart';
import '../../../constants/style.dart';
import '../data/session_data.dart';
import '../lib/snapping_sheet/snapping_position.dart';
import '../lib/snapping_sheet/snapping_sheet_widget.dart';
import '../screen/installment_result_screen.dart';
import '../util/app_util.dart';
import '../util/const.dart';
import '../widget/button_block.dart';

part 'function_handle_part.dart';

class HomeNewController extends GetxController with NotificationExtension, GetSingleTickerProviderStateMixin{
  BuildContext? context;
  var _appController = Get.find<AppController>();
  final double grabbingContentHeight = 64;
  final double headerHeight = 48;
  final double headerPadding = 16;

  final Duration durationAnimation = const Duration(milliseconds: 200);
  final Duration durationAnimation100 = const Duration(milliseconds: 100);
  final ScrollController scrollController = ScrollController();
  final SnappingSheetController snappingSheetController = SnappingSheetController();
  final ScreenshotController screenshotQRViShareController = ScreenshotController();
  final ScreenshotController screenshotQRAGShareController = ScreenshotController();

  final double bodyHeight = SessionData.screenSize.height - SessionData.screenPadding.bottom;
  final double positionBottom = 64;
  final double positionMiddle = (SessionData.screenSize.height - SessionData.screenPadding.bottom - 64) * 0.55;
  final double positionTop = SessionData.screenSize.height -
      SessionData.screenPadding.bottom -
      64 -
      SessionData.screenPadding.top -
      48 -
      16 * 2;
  final List<SnappingPosition> listSnappingPosition = [];
  double heightButtonBlock = Const.heightFunctionBlockL;
  double currentGrabbingContentOffset = GrabbingContentOffset.top;

  RxDouble relativeToSnappingPositionMiddle = 0.0.obs;
  RxDouble relativeToSnappingPositionCurrent = 0.0.obs; // 0.0 <-> 1.0
  RxDouble qrSize = 0.0.obs;
  Rx<Uint8List> imageQRViCaptured = Uint8List(0).obs;
  Rx<Uint8List> imageQRAGCaptured = Uint8List(0).obs;
  RxDouble currentOffset = 1.0.obs;
  RxBool showAmount = false.obs;
  RxString amountValue = ''.obs;

  RxBool qrStaticInfoLoading = true.obs;
  RxString qrStaticInfoError = ''.obs;
  RxMap qrViStaticInfoData = RxMap();
  RxMap qrAGStaticInfoData = RxMap();

  MPDataLoginModel mpDataLoginModel = MPDataLoginModel();
  List<ButtonBlockItemModel> listItemButtonBlock = [];
  List<MPItemMenuHome> listItemButtonSub = [];
  List<MPItemBanner> listBanner = [];

  RxString currentPhone = ''.obs;
  RxString currentEmail = ''.obs;
  RxString currentIdentity = ''.obs;
  RxString currentDescription = ''.obs;
  RxString qrViCapturedForShareAndSavePath = ''.obs;
  RxString qrAGCapturedForShareAndSavePath = ''.obs;
  bool preventAutoScroll = false;

  var merchantName = "".obs;
  var isShowHistory = true.obs;
  var muid = '';
  var merchantId = 0;
  var emailMerchant = "";
  var needUpdateDevice = false;
  var flagRequireUpdateMposDevice = false.obs;
  // var flagShowMarqueeUpgradeFW = false.obs;
  // var flagReshowTopIcon = false.obs;
  bool preventAutoScrollWhenPressAmountFromMid = false;
  RxString errorAmountMessage = ''.obs;
  RxString recentConnectedDeviceSerial = ''.obs;
  RxBool recentCardInsertState = false.obs;

  RxBool enableStaticQR = false.obs;
  late DeviceMpos mposDevice;
  var mapMerchantConfigLogin;
  String installmentPromotion = '0';

  RxBool rxShowFeatureCheckCardInstallment = false.obs;
  String codeTapToPhone = 'T2P';
  var pushpaymentEnable = false.obs;
  var smeEnable = true.obs;

  Rx<StaticQRType> currentStaticQRType = StaticQRType.VietQR.obs;
  bool agPayQRDefaultValue = false;

  late AnimationController animationControllerFlipQR;
  late Animation<double> animationFlipQR;
  RxDouble valueFliQR = 0.0.obs;

  @override
  void onInit() {
    super.onInit();
    mposDevice = AppConfiguration().deviceMpos;
    // var mapConfig = AppConfiguration().mapMerchantDataInfo;
    merchantName.value = AppConfiguration().merchantName;
    isShowHistory.value = AppConfiguration().isShowTransactionHistory;
    flagRequireUpdateMposDevice.value = AppConfiguration().flagRequireUpgradeMposDevice;
    emailMerchant = AppConfiguration().mapMerchantDataInfo["emailMerchant"];
    muid = AppConfiguration().mobileUser;
    merchantId = int.tryParse(AppConfiguration().merchantId) ?? 0;
    //needUpdateDevice = mapConfig["upgradeEMVConfig"] ?? false;

    mapMerchantConfigLogin = AppConfiguration().mapMerchantDataInfo as Map<String, dynamic>;
    installmentPromotion = mapMerchantConfigLogin['mores']?['installmentPromotion'] ?? '0';
    smeEnable.value = (mapMerchantConfigLogin['mores']?['smeFee'] ?? '0') == '1';

    mpDataLoginModel = MPDataLoginModel.fromJson(mapMerchantConfigLogin);
    developer.log("map merchant config login: $mapMerchantConfigLogin");

    // check if enable static QR or not
    enableStaticQR.value = _checkIfEnableStaticQR(muid);

    // if(flagRequireUpdateMposDevice.value) {
    //   _countdownHideMarquee();
    // }
    _initButtonBlock();

    // init mpos module base
    initMposModule();
    // MPMasterAction.instance.init(
    //   baseUrl: BaseService().getApiBaseUrl(),
    //   showLog: AppConfiguration().showLog,
    //   muid: muid,
    //   serial: mposDevice.deviceSerial,
    //   merchantId: merchantId,
    //   rootNavigator: false,
    //   forceInit: true,
    //   rawMerchantConfg: jsonEncode(mapMerchantConfigLogin),
    //   mpDevice: AppConfiguration().isKozenP12 ? MPConstantDevice.n31 : null,
    // );

    SnappingPosition snappingPositionBottom = SnappingPosition.pixels(
      positionPixels: positionBottom,
      grabbingContentOffset: GrabbingContentOffset.bottom,
      snappingDuration: durationAnimation,
    );
    SnappingPosition snappingPositionMiddle = SnappingPosition.pixels(
      positionPixels: positionMiddle,
      grabbingContentOffset: GrabbingContentOffset.middle,
      snappingDuration: durationAnimation,
    );
    SnappingPosition snappingPositionTop = SnappingPosition.pixels(
      positionPixels: positionTop,
      grabbingContentOffset: GrabbingContentOffset.top,
      snappingDuration: durationAnimation,
      snappingCurve: Curves.ease,
    );

    /// calculate relativeToSnappingPositionMiddle
    double minSnappingPos = snappingPositionBottom.getPositionInPixels(bodyHeight, grabbingContentHeight);
    double maxSnappingPos = snappingPositionTop.getPositionInPixels(bodyHeight, grabbingContentHeight);
    relativeToSnappingPositionMiddle.value =
        (snappingPositionMiddle.getPositionInPixels(bodyHeight, grabbingContentHeight) - minSnappingPos) /
            (maxSnappingPos - minSnappingPos);
    listSnappingPosition.addAll([
      snappingPositionBottom,
      snappingPositionMiddle,
      snappingPositionTop,
    ]);

    heightButtonBlock =
        SessionData.screenSize.height > Const.screenHeight675 ? Const.heightFunctionBlockL : Const.heightFunctionBlockM;
    scrollController.addListener(() {
      double height = showAmount.value ? (positionTop + 2.0) : (positionMiddle - 28.0);
      if (scrollController.position.userScrollDirection == ScrollDirection.reverse) {
        // down
        if (snappingSheetController.currentSnappingPosition.grabbingContentOffset == GrabbingContentOffset.top &&
            currentOffset.value < 30.0 &&
            !preventAutoScroll) {
          preventAutoScroll = true;
          Future.delayed(Duration(milliseconds: 500), () {
            preventAutoScroll = false;
          });
          scrollController.animateTo(height, duration: durationAnimation, curve: Curves.ease);
        }
      } else if (scrollController.position.userScrollDirection == ScrollDirection.forward) {
        // up
        if (snappingSheetController.currentSnappingPosition.grabbingContentOffset == GrabbingContentOffset.top &&
            currentOffset.value < (height - 30.0) &&
            !preventAutoScroll) {
          showAmount.value = true;
          scrollController.animateTo(0.0, duration: durationAnimation100, curve: Curves.ease);
          Future.delayed(const Duration(milliseconds: 400), () {
            // force reload some widget after scroll done
            showAmount.refresh();
          });
        }
      }
      currentOffset.value = scrollController.offset;
    });

    animationControllerFlipQR = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );

    animationFlipQR = Tween<double>(begin: 0, end: 1).animate(animationControllerFlipQR)
      ..addListener(() {
        valueFliQR.value = animationFlipQR.value * pi;
      });
  }

  initMposModule() {
    MPMasterAction.instance.init(
      baseUrl: BaseService().getApiBaseUrl(),
      showLog: AppConfiguration().showLog,
      muid: muid,
      serial: mposDevice.deviceSerial,
      merchantId: merchantId,
      rootNavigator: false,
      forceInit: true,
      rawMerchantConfg: jsonEncode(mapMerchantConfigLogin),
      mpDevice: AppConfiguration().isKozenP12 ? MPConstantDevice.n31 : null,
    );
  }

  @override
  void onClose() {
    animationControllerFlipQR.dispose();
    super.onClose();
  }

  @override
  void onReady() {
    super.onReady();
    GAUtil.gaLogScreen("home_new_screen", "home_new_screen");
    // merchantFollowTopic(
    //     merchantId: AppConfiguration().merchantId,
    //     mobileUser: AppConfiguration().mobileUser.contains('@') ? '' : AppConfiguration().mobileUser);
    _subscribe();
    _initShowHintValue();
    if (enableStaticQR.value) {
      initDataStaticQR();
    } else {
      showAmountBlock(true);
    }
    pushpaymentEnable.value = AppConfiguration().enableRethinkdb;
    getAccountBalance();
    countUnReadNotification();
    pushToCashier();
    GAUtil.gaLogScreen("home_new_screen", "home_new_screen");
    Logger().PushLog();
  }

  // _countdownHideMarquee(){
  //   print("_countdownHideMarquee---");
  //
  //   flagShowMarqueeUpgradeFW.value = true;
  //   Future.delayed(Duration(seconds: 5), () {
  //     flagShowMarqueeUpgradeFW.value = false;
  //     flagReshowTopIcon.value = true;
  //   },);
  // }

  _subscribe() async {
    FirebaseMessaging messaging = FirebaseMessaging.instance;
    String? tokenFirebase = await messaging.getToken();
    subscribe(tokenFirebase: tokenFirebase);
  }

  Future<void> pushToCashier() async {
    var ppSetting = await PPSettingModel().loadPPSetting();
    if ((AppConfiguration().enableRethinkdb && ppSetting.supportScreenStartup)
        && AppConfiguration().appType != AppType.MPOS_LITE){
      _appController.hideAppLoading();
      Get.toNamed(AppRoutes.CASHER_PAGE);
    }
  }

  bool _checkIfEnableStaticQR(String muid) {
    if (muid.indexOf('@') > -1) {
      return false;
    }
    return true;
  }

  onVerticalDragEnd(double velocity) {
    if (!enableStaticQR.value) return;
    if (velocity != 0) {
      if (velocity < 0) {
        // debugPrint('/////////////// SWIPE UP: ${endDetails.primaryVelocity}');
        if (snappingSheetController.currentSnappingPosition.grabbingContentOffset == GrabbingContentOffset.bottom &&
            listSnappingPosition.length > 1) {
          snappingSheetController.snapToPosition(listSnappingPosition[1]);
        } else if (snappingSheetController.currentSnappingPosition.grabbingContentOffset ==
                GrabbingContentOffset.middle &&
            listSnappingPosition.length > 2) {
          snappingSheetController.snapToPosition(listSnappingPosition[2]);
        }
      } else {
        // debugPrint('/////////////// SWIPE DOWN: ${endDetails.primaryVelocity}');
        if (snappingSheetController.currentSnappingPosition.grabbingContentOffset == GrabbingContentOffset.top &&
            listSnappingPosition.length > 1) {
          snappingSheetController.snapToPosition(listSnappingPosition[1]);
        } else if (snappingSheetController.currentSnappingPosition.grabbingContentOffset ==
                GrabbingContentOffset.middle &&
            listSnappingPosition.isNotEmpty) {
          snappingSheetController.snapToPosition(listSnappingPosition[0]);
        }
      }
    }
  }

  onSheetMove(SheetPositionData data) {
    relativeToSnappingPositionCurrent.value = data.relativeToSnappingPositions > 1
        ? 1
        : data.relativeToSnappingPositions < 0
            ? 0
            : data.relativeToSnappingPositions;
  }

  onSnapStart(SnappingPosition position) {
    if (position.grabbingContentOffset != GrabbingContentOffset.top && showAmount.value) {
      showAmountBlock(false);
    }
  }

  onSnapCompleted(SnappingPosition position) {
    if (currentGrabbingContentOffset == 0.0 && position.grabbingContentOffset == -1.0) {
      // print('------ mid -> bot');
      GAUtil.gaLogActionEvent(ActionTracking.home_swipe_down_bottom, null);
    } else if (currentGrabbingContentOffset == 0.0 && position.grabbingContentOffset == 1.0) {
      // print('------ mid -> top');
      GAUtil.gaLogActionEvent(ActionTracking.home_swipe_up_top, null);
    } else if (currentGrabbingContentOffset == -1.0 && position.grabbingContentOffset == 0.0) {
      // print('------ bot -> mid');
      GAUtil.gaLogActionEvent(ActionTracking.home_swipe_up_center, null);
    } else if (currentGrabbingContentOffset == 1.0 && position.grabbingContentOffset == 0.0) {
      // print('------ top -> mid');
      GAUtil.gaLogActionEvent(ActionTracking.home_swipe_down_center, null);
    }else if (currentGrabbingContentOffset == -1.0 && position.grabbingContentOffset == 1.0) {
      // print('------ bot -> top');
    }else if (currentGrabbingContentOffset == 1.0 && position.grabbingContentOffset == -1.0) {
      // print('------ top -> bot');
    }
    if (position.grabbingContentOffset != GrabbingContentOffset.top) {
      _clearInputInfo();
    }
    if (!preventAutoScrollWhenPressAmountFromMid && !showAmount.value) {
      if (currentGrabbingContentOffset != position.grabbingContentOffset) {
        currentGrabbingContentOffset = position.grabbingContentOffset;
        if (currentGrabbingContentOffset != GrabbingContentOffset.top) {
          scrollController.jumpTo(0.0);
        } else {
          Future.delayed(const Duration(milliseconds: 0), () {
            double height = showAmount.value ? (positionTop + 2.0) : (positionMiddle - 28.0);
            scrollController.animateTo(height, duration: durationAnimation, curve: Curves.ease);
          });
        }
      }
    } else {
      preventAutoScrollWhenPressAmountFromMid = false;
    }
  }

  showAmountBlock(bool value) {
    showAmount.value = value;
    currentOffset.value = 1.0;
    if (value) {
      amountValue.value = '0';
      scrollController.animateTo(0.0, duration: durationAnimation, curve: Curves.easeIn);
      snapTo(2, fromShowAmount: true);
      Future.delayed(const Duration(milliseconds: 400), () {
        // force reload some widget after scroll done
        showAmount.refresh();
      });
    } else {
      _clearInputInfo();
    }
  }

  onTextInputChange(String value) {
    if (!showAmount.value) {
      showAmountBlock(true);
    } else {
      scrollController.animateTo(0.0, duration: durationAnimation, curve: Curves.easeIn);
    }
    updateAmountValue(value);
  }

  updateAmountValue(String v, {bool fromSuggest = false}) {
    errorAmountMessage.value = '';
    if (fromSuggest) {
      amountValue.value = v;
    } else {
      if (v == 'clear') {
        amountValue.value = '';
      } else if (v == 'del') {
        if (amountValue.isNotEmpty) {
          amountValue.value = amountValue.substring(0, amountValue.value.length - 1);
        } else {
          return;
        }
      } else if (v == '000') {
        String temp = amountValue.value + v;
        if (temp.length < 10) {
          amountValue.value = (int.tryParse(temp) ?? 0).toString();
        } else {
          amountValue.value = (int.tryParse(temp.substring(0, 10)) ?? 0).toString();
        }
      } else if (amountValue.value.length < 10) {
        amountValue.value = (int.tryParse(amountValue.value + v) ?? 0).toString();
      }
    }
  }

  _clearInputInfo() {
    updateAmountValue('clear');
    currentPhone.value = '';
    currentEmail.value = '';
    currentDescription.value = '';
  }

  snapTo(int index, {bool fromShowAmount = false}) {
    preventAutoScrollWhenPressAmountFromMid = index == 2 && fromShowAmount;
    snappingSheetController.snapToPosition(listSnappingPosition[index]);
  }

  double getValueFactorScrollDown() {
    double opacity = currentOffset.value / heightButtonBlock;
    return opacity < 0
        ? 0
        : opacity > 1
            ? 1
            : opacity;
  }

  double getValueFactorScrollUp() {
    double opacity = 1 - (currentOffset.value / heightButtonBlock);
    return opacity < 0
        ? 0
        : opacity > 1
            ? 1
            : opacity;
  }

  /// value return: 0.0 <-> maxValue
  double getValueFactorMidToTop(double maxValue) {
    return maxValue *
        (relativeToSnappingPositionCurrent.value <= relativeToSnappingPositionMiddle.value
            ? 0.0
            : ((relativeToSnappingPositionCurrent.value - relativeToSnappingPositionMiddle.value) /
                (1.0 - relativeToSnappingPositionMiddle.value)));
  }

  /// value return: 0.0 <-> maxValue
  double getValueFactorTopToMid(double maxValue) {
    return maxValue *
        (relativeToSnappingPositionCurrent.value <= relativeToSnappingPositionMiddle.value
            ? 1.0
            : (1.0 - relativeToSnappingPositionCurrent.value));
  }

  /// value return: 0.0 <-> maxValue
  double getValueFactorBottomToMid(double maxValue) {
    return maxValue *
        (relativeToSnappingPositionCurrent.value <= 0.0
            ? 0.0
            : relativeToSnappingPositionCurrent.value > relativeToSnappingPositionMiddle.value
                ? 1.0
                : (relativeToSnappingPositionCurrent.value / relativeToSnappingPositionMiddle.value));
  }

  /// value return: 0.0 <-> maxValue
  double getValueFactorMidToBottom(double maxValue) {
    return maxValue *
        (relativeToSnappingPositionCurrent.value >= relativeToSnappingPositionMiddle.value
            ? 0.0
            : (1.0 - (relativeToSnappingPositionCurrent.value / relativeToSnappingPositionMiddle.value)));
  }

  RxBool showHint = false.obs;

  onShowHintBlock(bool value) {
    showHint.value = value;
  }

  _initShowHintValue() async {
    String? cacheShowHintString = await MPLocalStorage.instance.getStringValue('CACHE_SHOW_HINT');
    if ((cacheShowHintString ?? '').isEmpty) {
      MPLocalStorage.instance.setStringValue('CACHE_SHOW_HINT', '1');
      onShowHintBlock(true);
    }
  }

  /// Get Static QR
  initDataStaticQRFromCache(bool allowStaticVAQR) async {
    String? cacheImageQRString = await MPLocalStorage.instance.getStringValue('CACHE_STATIC_QR');
    String? cacheImageQRAGString = await MPLocalStorage.instance.getStringValue('CACHE_STATIC_QR_AG');
    String? imageQRAGShowDefault = await MPLocalStorage.instance.getStringValue('STATIC_QR_AG_SHOW_DEFAULT');
    if ((cacheImageQRString ?? '').isNotEmpty && allowStaticVAQR) {
      try {
        imageQRViCaptured.value = base64Decode(cacheImageQRString!);
      } catch (e) {}
    }
    if ((cacheImageQRAGString ?? '').isNotEmpty) {
      try {
        imageQRAGCaptured.value = base64Decode(cacheImageQRAGString!);
      } catch (e) {}
    }
    if (imageQRViCaptured.value.length == 0 && imageQRAGCaptured.value.length == 0) {
      enableStaticQR.value = false;
    } else if (imageQRViCaptured.value.length == 0) {
      currentStaticQRType.value = StaticQRType.AGPayQR;
      enableStaticQR.value = true;
    } else if (imageQRAGCaptured.value.length == 0) {
      currentStaticQRType.value = StaticQRType.VietQR;
      enableStaticQR.value = allowStaticVAQR;
    } else {
      if (imageQRAGShowDefault == '1') {
        currentStaticQRType.value = StaticQRType.AGPayQR;
      } else {
        currentStaticQRType.value = StaticQRType.VietQR;
      }
      enableStaticQR.value = true;
    }
    if (enableStaticQR.value) {
      snapTo(1);
      showAmount.value = false;
    } else {
      snapTo(2);
      showAmount.value = true;
    }
  }

  initDataStaticQR() async {
    qrStaticInfoError.value = '';
    qrStaticInfoLoading.value = true;
    bool allowStaticVAQR =
        (mpDataLoginModel.buttonApps ?? []).firstWhereOrNull((element) => element.code == 'TAO_VIET_QR') != null;

    await initDataStaticQRFromCache(allowStaticVAQR);

    MPBaseResponse responseList = await MPApiUtil.request(MPApiConstant.urlApi, {
      'serviceName': 'GET_QR_STATIC_LIST',
      'merchantId': merchantId,
      'muid': muid,
    });
    qrStaticInfoLoading.value = false;
    if (responseList.result == true) {
      if (allowStaticVAQR) {
        qrViStaticInfoData.value = ((responseList.data?['qrStaticList'] ?? []) as List)
            .firstWhere((e) => e['qrType'] == 'VAQR', orElse: () => {});
      }
      qrAGStaticInfoData.value = ((responseList.data?['qrStaticList'] ?? []) as List)
          .firstWhere((e) => e['qrType'] == 'LINKCARD', orElse: () => {});
      qrStaticInfoError.value = '';
      if (qrViStaticInfoData['qrType'] == null && qrAGStaticInfoData['qrType'] == null) {
        qrStaticInfoError.value = responseList.message ?? 'Unknown Error';
        enableStaticQR.value = false;
      } else if (qrViStaticInfoData['qrType'] == null) {
        currentStaticQRType.value = StaticQRType.AGPayQR;
        enableStaticQR.value = true;
      } else if (qrAGStaticInfoData['qrType'] == null) {
        currentStaticQRType.value = StaticQRType.VietQR;
        enableStaticQR.value = allowStaticVAQR;
      } else {
        if (qrAGStaticInfoData['showDefault'] == true) {
          currentStaticQRType.value = StaticQRType.AGPayQR;
        } else {
          currentStaticQRType.value = StaticQRType.VietQR;
        }
        enableStaticQR.value = true;
      }
      // init image to show
      imageQRViCaptured.value =
          await generateQRImage(qrViStaticInfoData['qrCode'] ?? '', qrViStaticInfoData['qrLogoLink'] ?? '');
      imageQRAGCaptured.value =
          await generateQRImage(qrAGStaticInfoData['qrContent'] ?? '', qrAGStaticInfoData['qrLogoLink'] ?? '');
      MPLocalStorage.instance.setStringValue(
          'CACHE_STATIC_QR',
          (qrViStaticInfoData['qrCode'] ?? '').toString().isNotEmpty && allowStaticVAQR
              ? base64Encode(imageQRViCaptured.value)
              : '');
      MPLocalStorage.instance.setStringValue('CACHE_STATIC_QR_AG',
          (qrAGStaticInfoData['qrContent'] ?? '').toString().isNotEmpty ? base64Encode(imageQRAGCaptured.value) : '');
      MPLocalStorage.instance
          .setStringValue('STATIC_QR_AG_SHOW_DEFAULT', qrAGStaticInfoData['showDefault'] == true ? '1' : '0');
      agPayQRDefaultValue = qrAGStaticInfoData['showDefault'] == true;
      // init image to save and share
      Future.delayed(const Duration(seconds: 3), () async {
        if (qrViCapturedForShareAndSavePath.value.isEmpty) {
          qrViCapturedForShareAndSavePath.value = await captureQr(screenshotQRViShareController) ?? '';
        }
        if (qrAGCapturedForShareAndSavePath.value.isEmpty) {
          qrAGCapturedForShareAndSavePath.value = await captureQr(screenshotQRAGShareController) ?? '';
        }
      });
    } else {
      qrStaticInfoError.value = responseList.message ?? 'Unknown Error';
    }
    if (enableStaticQR.value) {
      snapTo(1);
      showAmount.value = false;
    } else {
      snapTo(2);
      showAmount.value = true;
    }
  }

  _initButtonBlock() async {
    rxShowFeatureCheckCardInstallment.value = !muid.contains('@') && mpDataLoginModel.isPayCard == 1;
    List<String> listException = ['CHOT_CA', 'TRANSACTION_SUMMARY'];

    print('mpDataLoginModel.buttonApps.size=${mpDataLoginModel.buttonApps?.length}');
    List<MPItemMenuHome> listButtonAllSorted = (mpDataLoginModel.buttonApps ?? []).where((element) {
      print('-element code=${element.code} => ${element.toString()}');
      if (element.code == "TT_MOTO") {
        DeviceMpos deviceMpos = AppConfiguration().deviceMpos;
        if (mapMerchantConfigLogin["isPayMoto"] == 1
            && (deviceMpos.deviceType == DeviceType.READER_SP02.index || deviceMpos.deviceType == DeviceType.READER_SP01.index)) {
          return true;
        }
        return false;
      }
      if (element.code == "LinkCard_PAYMENT" &&
          (mpDataLoginModel.createLinkPaymentFlag == 0 || mpDataLoginModel.isNormalPayLink == 0)) {
        return false;
      }
      if (element.code == "VAS_PAYMENT" && mpDataLoginModel.isSaleService == "0") {
        return false;
      }
      if (element.code == "VayMuonQR_PAYMENT" && mpDataLoginModel.installmentVaymuonInfo!.length == 0) {
        return false;
      }
      if (element.code == "INSTALLMENT_PAYMENT" && mpDataLoginModel.isShowInstallmentPayment == 0) {
        return false;
      }
      if (element.code == "QRCode_International_PAYMENT" && (mpDataLoginModel.listInternationalQR!.length == 0)) {
        return false;
      }
      if (element.code == "SUPPLIER_PROMO_CODE" && mpDataLoginModel.isSupplierPromotion == 0) {
        return false;
      }
      if (element.code == "UNIVEST_INVEST" && mpDataLoginModel.isUnivestLink == 0) {
        return false;
      }
      if (element.code == "QUET_THE" && AppConfiguration().isUseTapToPhone) {
        return false;
      }
      if (listException.contains(element.code)) {
        return false;
      }
      return true;
    }).toList();
    print('1 listButtonAllSorted.size=${listButtonAllSorted.length}');

    // loai bo cac nut co optShowMenu != 1 và parentBtnAppCode
    listButtonAllSorted
        .removeWhere((element) => element.optShowMenu != 1 && (element.parentBtnAppCode ?? '').isNotEmpty);
    listButtonAllSorted
        .sort((a, b) => (int.tryParse(a.position ?? '0') ?? 0).compareTo((int.tryParse(b.position ?? '0') ?? 0)));
    print('2 listButtonAllSorted.size=${listButtonAllSorted.length}');

    List<MPItemMenuHome> listButtonMain =
        listButtonAllSorted.length < 5 ? listButtonAllSorted : listButtonAllSorted.sublist(0, 4);
    listItemButtonSub =
        listButtonAllSorted.length < 5 ? [] : listButtonAllSorted.sublist(4, listButtonAllSorted.length);

    if (rxShowFeatureCheckCardInstallment.value) {
      listItemButtonSub.add(MPItemMenuHome(
        code: 'CHECK_CARD_INSTALLMENT_WEBVIEW',
        logo: 'ic_check_card_menu.svg',
        title: 'Kiểm tra thẻ trả góp',
        titleEn: 'Check your installment card',
      ));
    }
    listBanner = mpDataLoginModel.listBanner ?? [];

    for (int i = 0; i < listButtonMain.length; i++) {
      MPItemMenuHome buttonApp = listButtonMain[i];
      ButtonBlockItemModel buttonBlockItemModel = ButtonBlockItemModel()
        ..code = buttonApp.code
        ..name = buttonApp.title
        ..nameEn = buttonApp.titleEn
        ..subName = buttonApp.subName
        ..subNameEn = buttonApp.subNameEn
        ..onPress = () => onPressFeature(buttonApp.code ?? '', mpItemMenuHome: buttonApp);

      switch (buttonApp.code) {
        case 'TAO_VIET_QR':
          if (i < 2) {
            buttonBlockItemModel
              ..bgColor = const Color(0xFFFFA412)
              ..borderColor = const Color(0xFFFFA412)
              ..textColor = Colors.white;
          } else {
            buttonBlockItemModel
              ..bgColor = Colors.white
              ..borderColor = const Color(0xFFFFA412)
              ..textColor = const Color(0xFFFFA412);
          }
          break;
        case 'QR_E_WALLET':
          if (i < 2) {
            buttonBlockItemModel
              ..bgColor = const Color(0xFF6FA637)
              ..borderColor = const Color(0xFF6FA637)
              ..textColor = Colors.white;
          } else {
            buttonBlockItemModel
              ..bgColor = Colors.white
              ..borderColor = const Color(0xFF6FA637)
              ..textColor = const Color(0xFF6FA637);
          }
          break;
        case 'QUET_THE':
          // if (AppConfiguration().isUseTapToPhone) {
          //   buttonBlockItemModel = ButtonBlockItemModel()
          //     ..code = codeTapToPhone
          //     ..name = 'Tap to phone'
          //     ..nameEn = 'Tap to phone'
          //     ..onPress = () => onPressFeature(codeTapToPhone);
          // }
          if (i < 2) {
            buttonBlockItemModel
              ..bgColor = const Color(0xFF0979FD)
              ..borderColor = const Color(0xFF0979FD)
              ..textColor = Colors.white;
          } else {
            buttonBlockItemModel
              ..bgColor = Colors.white
              ..borderColor = const Color(0xFF0979FD)
              ..textColor = const Color(0xFF0979FD);
          }
          break;
        case 'TRA_GOP':
          if (i < 2) {
            buttonBlockItemModel
              ..bgColor = const Color(0xFFD22E2A)
              ..borderColor = const Color(0xFFD22E2A)
              ..textColor = Colors.white;
          } else {
            buttonBlockItemModel
              ..bgColor = Colors.white
              ..borderColor = const Color(0xFFD22E2A)
              ..textColor = const Color(0xFFD22E2A);
          }
          break;
        case 'TAO_LINK_TU_XA':
          if (i < 2) {
            buttonBlockItemModel
              ..bgColor = const Color(0xFF00B2D5)
              ..borderColor = const Color(0xFF00B2D5)
              ..textColor = Colors.white;
          } else {
            buttonBlockItemModel
              ..bgColor = Colors.white
              ..borderColor = const Color(0xFF00B2D5)
              ..textColor = const Color(0xFF00B2D5);
          }
          break;
        case 'TT_MOTO':
          if (i < 2) {
            buttonBlockItemModel
              ..bgColor = const Color(0xFFFF7013)
              ..borderColor = const Color(0xFFFF7013)
              ..textColor = Colors.white;
          } else {
            buttonBlockItemModel
              ..bgColor = Colors.white
              ..borderColor = const Color(0xFFFF7013)
              ..textColor = const Color(0xFFFF7013);
          }
          break;
        default:
          print('->not implemented: ${buttonApp.code}');
          if (i < 2) {
            buttonBlockItemModel
              ..bgColor = const Color(0xFFD22E2A)
              ..borderColor = const Color(0xFFD22E2A)
              ..textColor = Colors.white;
          } else {
            buttonBlockItemModel
              ..bgColor = Colors.white
              ..borderColor = const Color(0xFFD22E2A)
              ..textColor = const Color(0xFFD22E2A);
          }
          break;
      }
      listItemButtonBlock.add(buttonBlockItemModel);
    }

    // if(AppConfiguration().isUseTapToPhone){
    //   ButtonBlockItemModel buttonBlockItemModel = ButtonBlockItemModel()
    //     ..code = codeTapToPhone
    //     ..name = 'Tap to phone'
    //     ..nameEn = 'Tap to phone'
    //     ..bgColor = const Color(0xFFF376F1)
    //     ..borderColor = const Color(0xFFF376F1)
    //     ..textColor = Colors.white
    //     ..onPress = () => onPressFeature(codeTapToPhone);
    //   listItemButtonBlock.add(buttonBlockItemModel);
    // }

    //Check Pushpayment Enable
    if (AppConfiguration().enableRethinkdb && !muid.isValidEmail()) {
      var pushPayment = MPItemMenuHome(
          title: LocalizationCustom.titlePointOfSaleHome,
          titleEn: LocalizationCustom.titlePointOfSaleHome,
          logo: "ic_home_pushpayment.svg",
          status: "ACTIVE",
          menuType: "HOME_SUB",
          serviceCode: "PUSH_PAYMENT",
          code: "PUSH_PAYMENT");
      listItemButtonSub.insert(0, pushPayment);
    }

    // if (await permitDeposit()) {
    // if (AppConfiguration().allowDeposit == '1' || AppConfiguration().allowDeposit == '2') {
      var depositPayment = MPItemMenuHome(
          title: LocalizationCustom.localization('Đặt cọc'),
          titleEn: LocalizationCustom.localization('Đặt cọc'),
          logo: "ic_menu_3.svg",
          status: "ACTIVE",
          menuType: "HOME_SUB",
          serviceCode: "DEPOSIT_PAYMENT",
          code: "DEPOSIT_PAYMENT");
      listItemButtonSub.insert(1, depositPayment);
    // }

    // generateButtonAppWithFieldMenuHome();
  }

  /*void generateButtonAppWithFieldMenuHome(){
    if (listItemButtonBlock.length == 0 && listItemButtonSub.length == 0){
      List<MPItemMenuHome> lstMenuHome = (mpDataLoginModel.menuHome ?? []).where((element) => element.status == 'ACTIVE').toList();
      for (MPItemMenuHome item in lstMenuHome){
        if (item.serviceCode == "CARD_PAYMENT" ){
          var serviceCode = "QUET_THE";
          ButtonBlockItemModel buttonBlockItemModel = ButtonBlockItemModel()
            ..code = serviceCode
            ..name = LocalizationCustom.langCode == 'en' ? item.titleEn : item.title
            ..onPress = () => onPressFeature(serviceCode ?? '');
          buttonBlockItemModel
            ..bgColor = Colors.white
            ..borderColor = const Color(0xFF0979FD)
            ..textColor = const Color(0xFF0979FD);
          listItemButtonBlock.add(buttonBlockItemModel);
        }else if (item.serviceCode == "INSTALLMENT_PAYMENT"){
          var serviceCode = "TRA_GOP";
          ButtonBlockItemModel buttonBlockItemModel = ButtonBlockItemModel()
            ..code = serviceCode
            ..name = LocalizationCustom.langCode == 'en' ? item.titleEn : item.title
            ..onPress = () => onPressFeature(serviceCode ?? '');
          buttonBlockItemModel
            ..bgColor = Colors.white
            ..borderColor = const Color(0xFFD22E2A)
            ..textColor = const Color(0xFFD22E2A);
          listItemButtonBlock.add(buttonBlockItemModel);
        }else{
          var iconLink = item.iconLink;
          MPItemMenuHome mpMenuHome = MPItemMenuHome(title: item.title, titleEn: item.titleEn, logo: iconLink,iconLink: iconLink,position: item.position, serviceCode: item.serviceCode, code: item.serviceCode);
          listItemButtonSub.add(mpMenuHome);
        }
      }
      List<MPBankQR>? listBankQR = mpDataLoginModel.listBankQR ?? [];
      for (MPBankQR bankQR in listBankQR){
        if (bankQR.shortName == "VAQR"){
          ButtonBlockItemModel buttonBlockItemModel = ButtonBlockItemModel()
            ..code = "TAO_VIET_QR"
            ..name = LocalizationCustom.langCode == 'en' ? "Create VietQR" : "Tạo VietQR"
            ..onPress = () => onPressFeature("TAO_VIET_QR" ?? '');
          buttonBlockItemModel
            ..bgColor = const Color(0xFFFFA412)
            ..borderColor = const Color(0xFFFFA412)
            ..textColor = Colors.white;
          listItemButtonBlock.add(buttonBlockItemModel);
        }else if (bankQR.shortName == "QR_VI"){
          ButtonBlockItemModel buttonBlockItemModel = ButtonBlockItemModel()
            ..code = "QR_E_WALLET"
            ..name = LocalizationCustom.langCode == 'en' ? "QR e-wallet" : "QR ví điện tử"
            ..onPress = () => onPressFeature("TAO_VIET_QR" ?? '');
          buttonBlockItemModel
            ..bgColor = const Color(0xFF6FA637)
            ..borderColor = const Color(0xFF6FA637)
            ..textColor = Colors.white;
          listItemButtonBlock.add(buttonBlockItemModel);
        }
      }
    }
  }*/

  onPressAddPaymentInfo({CustomerInfoInputFocus? initFocusField, Function()? callback}) {
    GAUtil.gaLogActionEvent(ActionTracking.home_press_add_order_description, null);
    showMPBottomSheet(
      context!,
      body: CustomerInfoInput(
        onPressConfirm: (phone, email, description, identity) {
          GAUtil.gaLogActionEvent(ActionTracking.home_bs_add_details_press_continue, null);
          Get.back();
          currentPhone.value = phone;
          currentEmail.value = email;
          currentDescription.value = description;
          currentIdentity.value = identity;
          if (callback != null) {
            Future.delayed(const Duration(milliseconds: 500), callback);
          }
        },
        inputPhone: currentPhone.value,
        inputEmail: currentEmail.value,
        inputDescription: currentDescription.value,
        inputIdentity: currentIdentity.value,
        focusField: initFocusField,
      ),
      title: 'Mô tả đơn hàng',
      resizeToAvoidBottomInset: false,
    );
  }

  Future<void> saveQrToGallery() async {
    GAUtil.gaLogActionEvent(ActionTracking.home_press_download_qr, null);
    String qrCapturedForShareAndSavePath = currentStaticQRType.value == StaticQRType.AGPayQR
        ? qrAGCapturedForShareAndSavePath.value
        : qrViCapturedForShareAndSavePath.value;
    if (qrCapturedForShareAndSavePath.isNotEmpty) {
      bool? savedSuccess = await MPUtil.saveImageToGallery(qrCapturedForShareAndSavePath);
      if (savedSuccess ?? false) {
        MPUtil.showTopSnackBarMessage(
            context!, LocalizationCustom.localization("Lưu mã QR thành công vào thư viện ảnh"));
      } else {
        MPUtil.showTopSnackBarMessage(
          context!,
          LocalizationCustom.localization(
              "Lưu mã QR thất bại, vui lòng kiểm tra, cấp quyền truy cập thư viện ảnh cho ứng dụng và thử lại sau"),
          type: MPTopSnackBarType.warning,
        );
      }
    } else {
      MPUtil.showTopSnackBarMessage(
        context!,
        LocalizationCustom.localization("Đang khởi tạo ảnh, vui lòng chờ trong giây lát"),
        type: MPTopSnackBarType.warning,
      );
      Future.delayed(const Duration(seconds: 3), () async {
        if (qrViCapturedForShareAndSavePath.value.isEmpty) {
          qrViCapturedForShareAndSavePath.value = await captureQr(screenshotQRViShareController) ?? '';
        }
        if (qrAGCapturedForShareAndSavePath.value.isEmpty) {
          qrAGCapturedForShareAndSavePath.value = await captureQr(screenshotQRAGShareController) ?? '';
        }
      });
    }
  }

  Future<void> shareQrImage() async {
    GAUtil.gaLogActionEvent(ActionTracking.home_press_share_qr, null);
    String qrCapturedForShareAndSavePath = currentStaticQRType.value == StaticQRType.AGPayQR
        ? qrAGCapturedForShareAndSavePath.value
        : qrViCapturedForShareAndSavePath.value;
    if (qrCapturedForShareAndSavePath.isNotEmpty) {
      MPUtil.shareXFiles([XFile(qrCapturedForShareAndSavePath)]);
    } else {
      MPUtil.showTopSnackBarMessage(
        context!,
        LocalizationCustom.localization("Đang khởi tạo ảnh, vui lòng chờ trong giây lát"),
        type: MPTopSnackBarType.warning,
      );
      Future.delayed(const Duration(seconds: 3), () async {
        if (qrViCapturedForShareAndSavePath.value.isEmpty) {
          qrViCapturedForShareAndSavePath.value = await captureQr(screenshotQRViShareController) ?? '';
        }
        if (qrAGCapturedForShareAndSavePath.value.isEmpty) {
          qrAGCapturedForShareAndSavePath.value = await captureQr(screenshotQRAGShareController) ?? '';
        }
      });
    }
  }

  Future<String?> captureQr(ScreenshotController screenshotController) async {
    final completer = Completer<String?>();
    screenshotController.capture().then((image) async {
      if (image != null) {
        final fileName = DateTime.now().microsecondsSinceEpoch.toString();

        final directory = await getApplicationDocumentsDirectory();
        final imagePath = await File('${directory.path}/$fileName.png').create();
        await imagePath.writeAsBytes(image);

        completer.complete(imagePath.path);
      } else {
        completer.complete(null);
      }
    }, onError: (e) => completer.complete(null));
    return completer.future;
  }

  onPressFeature(String code, {MPItemMenuHome? mpItemMenuHome, bool forceAvoidBottomSheetAmount = false, String? codeBNPLItem, String? codeFather}) async {
    //check if feature have children
    List<MPItemMenuHome> listChildrenFeatures = [];
    for (MPItemMenuHome item in (mpDataLoginModel.buttonApps ?? [])) {
      if (item.parentBtnAppCode != null && item.parentBtnAppCode == mpItemMenuHome?.code) {
        listChildrenFeatures.add(item);
      }
    }
    if (listChildrenFeatures.isNotEmpty) {
      _showBottomSheetChildrenFeatures(
        context: context!,
        featureParent: mpItemMenuHome,
        featureChildren: listChildrenFeatures,
        onSelectedFeature: (MPItemMenuHome mpItemMenuHomeChild) {
          onPressFeature(
            mpItemMenuHomeChild.code ?? '',
            mpItemMenuHome: mpItemMenuHomeChild,
            forceAvoidBottomSheetAmount: forceAvoidBottomSheetAmount,
            codeBNPLItem: codeBNPLItem,
            codeFather: code,
          );
        },
      );
      return;
    }

    // check if feature do not need check amount
    List<String> listServiceCodeDoNotNeedAmount = [
      "CHECK_CARD_INSTALLMENT_WEBVIEW",
    ];
    if (listServiceCodeDoNotNeedAmount.contains(code)) {
      double amount = double.tryParse(amountValue.value) ?? 0.0;
      HomeUtil.onPressCheckCard(context!, merchantId.toString(), amount: amount);
      return;
    }

    // check if feature need amount but not at top (HOME_MAIN)
    List<String> listServiceCodeNeedAmount = [
      "QUET_THE",
      // "TT_MOTO",
      "TRA_GOP",
      "QR_E_WALLET",
      "TAO_VIET_QR",
      "QRCode_PAYMENT",
      "QRCode_International_PAYMENT",
      "MUA_TRUOC_TRA_SAU",
      "BNPL_PAYMENT",
      "TAO_LINK_TU_XA",
      "LinkCard_PAYMENT",
      "QR_MVISA",
      "QR_THE_TIN_DUNG",
      codeTapToPhone,
    ];

    // check if need show amount input bottom sheet
    String codeCheck = codeFather ?? code;
    print('codeCheck=$codeCheck');
    if (listServiceCodeNeedAmount.contains(codeCheck) && !forceAvoidBottomSheetAmount) {
      MPItemMenuHome? menuHomeCheck = listItemButtonSub.firstWhereOrNull((element) => element.code == code);
      if (menuHomeCheck != null) {
        // need open enter amount bottom sheet
        final Map resultBSAmount = await _showBottomSheetEnterAmount(
          context: context!,
          title: LocalizationCustom.langCode == 'en' ? menuHomeCheck.titleEn : menuHomeCheck.title,
          minAmount: getMinAmountByServiceCode(code, mpDataLoginModel),
          inputAmount: amountValue.value,
          inputPhone: currentPhone.value,
          inputEmail: currentEmail.value,
          inputIdentity: currentIdentity.value,
          inputDescription: currentDescription.value,
        );
        if (resultBSAmount['checkIsConfirmed'] != true) return;
        amountValue.value = resultBSAmount['amount'];
        currentPhone.value = resultBSAmount['phone'];
        currentEmail.value = resultBSAmount['email'];
        currentDescription.value = resultBSAmount['description'];
        currentIdentity.value = resultBSAmount['identity'];
        if (amountValue.value == '0') return;
      }
    }

    // check if feature required phone number
    if (currentPhone.value.trim().isEmpty) {
      if (['MUA_TRUOC_TRA_SAU', 'BNPL_PAYMENT'].contains(code) &&
          (double.tryParse(amountValue.value) ?? 0.0) >= 1000000) {
        MPUtil.showTopSnackBarMessage(
          context!,
          LocalizationCustom.localization("Vui lòng nhập số điện thoại"),
          type: MPTopSnackBarType.warning,
        );
        onPressAddPaymentInfo(
            initFocusField: CustomerInfoInputFocus.phone,
            callback: () => onPressFeature(code,
                mpItemMenuHome: mpItemMenuHome,
                forceAvoidBottomSheetAmount: forceAvoidBottomSheetAmount,
                codeBNPLItem: codeBNPLItem,
                codeFather: codeFather));
        return;
      }
    }

    MPUtil.log(
        '------ onPressFeature $code ------\n---amount = ${amountValue.value}\n---phone = ${currentPhone.value}\n---email = ${currentEmail.value}\n---description = ${currentDescription.value}\n---identity = ${currentIdentity.value}');

    var mapArgument = {};
    mapArgument["rnAmount"] = amountValue.value;
    mapArgument["rnDescription"] = currentDescription.value;
    mapArgument["rnPhone"] = currentPhone.value;
    mapArgument["rnEmail"] = currentEmail.value;
    mapArgument["rnIdentity"] = currentIdentity.value;
    mapArgument["rnServiceCode"] = code;

    //Begin HomeButton
    if (code == "QUET_THE") {
      // //Todo: hard code test speak
      // if (kDebugMode){
      //   // TextToSpeak().speak("Visa tặng bạn %amount% nghìn đồng cho giao dịch vừa thực hiện qua NextPay!");
      //   SpeakerHandler.speakWithAmount(int.parse(TextUtils().getRandomNumber(6)));
      //   return;
      // }

      GAUtil.gaLogActionEvent(ActionTracking.home_press_swipe_card, null);
      _handleSwipeCard(code, mapArgument);
      return;
    }
    //End HomeButton

    //Begin HomeSub Button
    if (code == "TT_MOTO") {
      GAUtil.gaLogActionEvent(ActionTracking.home_press_moto_payment, null);
      if (_checkEmailLogon(muid)) {
        return;
      }
      var readerNum = AppConfiguration().deviceMpos.deviceSerial;
      // if (kDebugMode){
      //   readerNum = "SP022202210725";
      //   AppConfiguration().setDeviceMpos(DeviceMpos(readerNum, DeviceType.READER_SP02.index));
      // }
      if (readerNum.isEmpty) {
        var result = await NativeBridge().callNativeGetDevice();
        if (result.deviceSerial.isEmpty) {
          AppUtils.showDialogWithMessage(context!,
              LocalizationCustom.localization("No MPOS device plugged. Please re-connect MPOS device then try again."));
          return;
        }
      }
      _appController.showAppLoading();
      String response = await MposRequest.requestGetCommonConfig(
          AppConfiguration().mobileUser,
          AppConfiguration().deviceMpos.deviceSerial);

      _appController.hideAppLoading();
      if (response.isNotEmpty) {
        var mapResponse = jsonDecode(response);
        ErrorResponse errorResponse = ErrorResponse.parserData(mapResponse);
        if (errorResponse.code == ErrorCode.DO_SERVICE_SUCCESS){
          CommonConfig commonConfig = CommonConfig.fromJson(jsonDecode(response));
          AppConfiguration().commonConfig = commonConfig;
          debugPrint('home MacqConfigInfo().motoBinRanges=${AppConfiguration().commonConfig.motoBinRanges}');
          Get.toNamed(AppRoutes.MOTO_PAYMENT_INFO_PAGE, arguments: mapArgument);
          return;
        }
      }
        AppUtils.showDialogWithMessage(context!, LocalizationCustom.localization("Không thành công"));
      // var resultGetMacqConfg = await MacqRequest.requestMacqConfig(AppConfiguration().mobileUser);
      // _appController.hideAppLoading();
      // if (resultGetMacqConfg.isSuccess) {
      //   Get.toNamed(AppRoutes.MOTO_PAYMENT_INFO_PAGE);
      // } else {
      //   AppUtils.showDialogError(context!, resultGetMacqConfg.error!.message);
      // }
      return;
    }
    if (code == "CHOT_CA" || code == "TRANSACTION_SUMMARY") {
      GAUtil.gaLogActionEvent(ActionTracking.home_press_close_shift, null);
      Get.toNamed(AppRoutes.TRANSACTIONSUM_PAGE);
      return;
    }
    if (code == "SETTING") {
      Get.toNamed(AppRoutes.SETTING_PAGE);
      return;
    }
    if (code == "ADD_SME_CARD") {
      Get.toNamed(AppRoutes.SME_CARD_PAGE);
      return;
    }
    if (code == "TRANSACTION_HISTORY") {
      // bool useFlutterHistory = await SystemPreference.getData(SystemPreference.useFlutterHistory, false);
      // dev.log('useFlutterHistory: $useFlutterHistory');
      // if (AppConfiguration().appType == AppType.FLUTTER_APP || useFlutterHistory){
      if (AppConfiguration().appType == AppType.FLUTTER_APP){
        Get.toNamed(AppRoutes.HISTORY_PAGE);
        return;
      }
      await NativeBridge().callNativeGetHistory();
      return;
    }
    if (code == "TRA_GOP") {
      GAUtil.gaLogActionEvent(ActionTracking.home_press_installment, null);
      if (!_checkRequiredAmount()) {
        return;
      }
      if (!_checkMinAmount(code)) {
        return;
      }
      var isPayCard = mpDataLoginModel.isPayCard ?? 0;
      var isPayLink = mpDataLoginModel.isPayLink ?? 0;
      if (mpDataLoginModel.mores?.tgVer == '1') {
        // new installment flow
        _onPressFeatureInstallment(isPayCard,  isPayLink, mpItemMenuHome: mpItemMenuHome, mapArgument: mapArgument);
      } else {
        // old installment flow
        if (isPayCard != 0 || isPayLink != 0){
          Map argumentSent = mapArgument;
          argumentSent['enableNewUI'] = false;
          var result = await NativeBridge().callReactModule(mapArgument, serviceCode: "INSTALLMENT_PAYMENT");
          if (result.isNotEmpty) {
            debugPrint("Result installment pay: $result");
            _processHandleResultPay(result, true);
          }
        } else{
          AppUtils.showDialogAlert(context!, description: LocalizationCustom.localization("You have not integrate payment with MPOS therefore you cannot do transaction by this way. Please contact hotline 1900.6364.88 to register re-login with MPOS reader or link card payment method."));
        }
      }
      return;
    }
    // QR group feature
    if (['QR_E_WALLET', 'TAO_VIET_QR', 'MUA_TRUOC_TRA_SAU', 'BNPL_PAYMENT', 'QR_MVISA', 'QR_THE_TIN_DUNG']
        .contains(code)) {
      switch (code) {
        case 'QR_E_WALLET':
          GAUtil.gaLogActionEvent(ActionTracking.home_press_electronic_wallet_qr, null);
          break;
        case 'TAO_VIET_QR':
          GAUtil.gaLogActionEvent(ActionTracking.home_press_generate_viet_qr, null);
          break;
        case 'MUA_TRUOC_TRA_SAU':
        case 'BNPL_PAYMENT':
          GAUtil.gaLogActionEvent(ActionTracking.home_press_bnpl, null);
          break;
        case 'QR_MVISA':
          GAUtil.gaLogActionEvent(ActionTracking.home_press_qr_mvisa, null);
          break;
        case 'QR_THE_TIN_DUNG':
          GAUtil.gaLogActionEvent(ActionTracking.home_press_qr_the_tin_dung, null);
          break;
      }
      if (!_checkRequiredAmount()) {
        return;
      }
      double amount = double.tryParse(amountValue.value) ?? 0.0;

      // TODO: Check serial number is empty
      if (code == 'QR_MVISA' && module.SessionData.sn.toString().isEmpty) {
        await Get.toNamed(AppRoutes.LOGIN_CONFIRM, arguments: true)!.then((value) async {
          // Check nếu sn vẫn chưa có sau khi trở lại từ LOGIN_CONFIRM
          initMposModule();
          if (module.SessionData.sn.toString().isNotEmpty) {
            errorAmountMessage.value = await _doFunctionQR(
              context: context!,
              mpDataLoginModel: mpDataLoginModel,
              code: code,
              title: LocalizationCustom.langCode == 'en'
                  ? mpItemMenuHome?.titleEn
                  : mpItemMenuHome?.title,
              amount: amount,
              currentPhone: currentPhone.value,
              currentEmail: currentEmail.value,
              currentDescription: currentDescription.value,
              codeBNPLItem: codeBNPLItem,
            );
          }
        });
      } else {
        errorAmountMessage.value = await _doFunctionQR(
          context: context!,
          mpDataLoginModel: mpDataLoginModel,
          code: code,
          title: LocalizationCustom.langCode == 'en'
              ? mpItemMenuHome?.titleEn
              : mpItemMenuHome?.title,
          amount: amount,
          currentPhone: currentPhone.value,
          currentEmail: currentEmail.value,
          currentDescription: currentDescription.value,
          codeBNPLItem: codeBNPLItem,
        );
      }
      return;
    }
    if (code == "ATM_360" || code == "VAS_PAYMENT") {
      GAUtil.gaLogActionEvent(ActionTracking.home_press_atm360, null);
      if (muid.isValidEmail()){
        var mess = LocalizationCustom.localization("Chức năng không hỗ trợ với tài khoản đăng nhập bằng Email. Vui lòng đăng nhập lại với Tên đăng nhập đã được cấp hoặc liên hệ số Hotline để được giải đáp.");
        AppUtils.showDialogWithMessage(context!, mess);
        return;
      }
      // await NativeBridge().callReactModule(mapArgument, serviceCode: "VAS_PAYMENT");
      Get.to(() => const ATM360ListServiceScreen(), binding: ATM360ListServiceBinding(featureCode: code));
      return;
    }
    if (code == "VAY_TIEN" || code == "NEXT_LEND") {
      GAUtil.gaLogActionEvent(ActionTracking.home_press_loan_request, null);
      var lendingLink = mpDataLoginModel.menuLendingLink ?? helpSupport;
      _gotoWebViewInfo(LocalizationCustom.localization('Vay tiền'),
          lendingLink);
      return;
    }
    if (code == "TAO_LINK_TU_XA" || code == "LinkCard_PAYMENT") {
      // old link flow
      // await NativeBridge().callReactModule(mapArgument, serviceCode: "LinkCard_PAYMENT");

      // new link flow
      GAUtil.gaLogActionEvent(ActionTracking.home_press_remote_link_creation, null);
      if (!_checkRequiredAmount()) {
        return;
      }
      double amount = double.tryParse(amountValue.value) ?? 0.0;
      _doFunctionLink(
        context: context!,
        mpDataLoginModel: mpDataLoginModel,
        amount: amount,
        currentPhone: currentPhone.value,
        currentEmail: currentEmail.value,
        currentDescription: currentDescription.value,
        isInstallment: false,
      );
      return;
    }
    if (code == "USER_MANUAL") {
      GAUtil.gaLogActionEvent(ActionTracking.home_guide_link, null);
      _gotoWebViewInfo(LocalizationCustom.localization('Hướng dẫn sử dụng'), mposUserGuide);
      return;
    }
    if (code == "HELP_CENTER") {
      _gotoWebViewInfo(LocalizationCustom.localization('Trung tâm trợ giúp'), helpSupport);
      return;
    }
    if (code == "UNIVEST_INVEST") {
      NativeBridge().callReactModule(mapArgument, serviceCode: "UNIVEST_INVEST");
      return;
    }
    if (code == "SUPPLIER_PROMO_CODE") {
      NativeBridge().callReactModule(mapArgument, serviceCode: "SUPPLIER_PROMO_CODE");
      return;
    }
    if (code == "QRCode_International_PAYMENT") {
      mapArgument["rnQrIsInternational"] = true;
      NativeBridge().callReactModule(mapArgument, serviceCode: "QRCode_International_PAYMENT");
      return;
    }
    if (code == "QRCode_PAYMENT") {
      mapArgument["rnQrIsInternational"] = false;
      NativeBridge().callReactModule(mapArgument, serviceCode: "QRCode_PAYMENT");
      return;
    }
    if (code == "ServiceTransactionContainer") {
      // NativeBridge().callReactModule(mapArgument, serviceCode: code);
      Get.to(ServiceTransactionsPage(), binding: ServiceTransactionsBinding());
      return;
    }
    if (code == "PUSH_PAYMENT") {
      Get.toNamed(AppRoutes.CASHER_PAGE);
      return;
    }
    if (code == "DEPOSIT_PAYMENT") {
      Get.toNamed(AppRoutes.DEPOSIT_PAGE);
      return;
    }
    //End HomeSub Button
    if (code == "SOFTWARE_UPDATE") {
      await NativeBridge().callNativeUpgradeDevice();
      return;
    }
    if (code == "NOTIFICATION") {
      // await NativeBridge().callNativePushNews();
      Get.toNamed(AppRoutes.NOTIFICATION_LIST);
      return;
    }
    if (code == codeTapToPhone) {
      _handleSelectPayTtp(code, mapArgument);
      return;
    }
    if (code == "GOTO_ACCOUNT") {
      Get.to(() => AccountPage(), binding: AccountBinding());
      return;
    }
  }

  _handleSwipeCard(String code, Map<dynamic, dynamic> mapArgument) async {
    if (_checkEmailLogon(muid)) {
      return;
    }
    if (!_checkRequiredAmount()) {
      return;
    }
    if (!_checkMinAmount(code)) {
      return;
    }
    var amountPay = int.parse(amountValue.value);

    var udid = code + "_" + TextUtils.generatePaymentIdentifer();
    mapArgument["amount"] = amountPay;
    mapArgument["description"] = currentDescription.value;
    mapArgument["udid"] = udid;
    mapArgument["email"] = currentEmail.value;
    MposRequest.preprareTransaction(
        udid,
        muid,
        merchantId.toString(),
        currentPhone.value,
        currentEmail.value,
        "CARD",
        currentDescription.value);
    bool isUseScreenSuccess = false;
    await NativeBridge().callNativeSetUseScreenSuccess(isUseScreenSuccess);
    var result = await NativeBridge().callNativePayNormal(mapArgument);
    if (result.isNotEmpty) {
      MPUtil.log("Result nomal pay: $result");
      _processHandleResultPay(result, isUseScreenSuccess);
    }
  }

  _handleSelectPayTtp(String code, Map<dynamic, dynamic> mapArgument) async {
    print('select pay TTP: ${mapArgument.toString()}');
    bool isAppInstalled = await NativeBridge().checkTtpInstalled();
    if (!isAppInstalled) {
      Get.toNamed(AppRoutes.TAPTOPHONE_PAGE, arguments: false);
      return;
    }
    if (_checkEmailLogon(muid)) {
      return;
    }
    if (!_checkRequiredAmount()) {
      return;
    }
    if (!_checkMinAmount(code)) {
      return;
    }
    var amountPay = int.parse(amountValue.value);

    var udid = code + "_" + TextUtils.generatePaymentIdentifer();
    mapArgument["amount"] = amountPay;
    mapArgument["description"] = currentDescription.value;
    mapArgument["udid"] = udid;
    mapArgument["email"] = currentEmail.value;
    MposRequest.preprareTransaction(
        udid,
        muid,
        merchantId.toString(),
        currentPhone.value,
        currentEmail.value,
        "TTP",
        currentDescription.value);

    await NativeBridge().callNativeSetUseScreenSuccess(false);
    var result = await NativeBridge().callNativePayTapToPhone(mapArgument);
    if (result.isNotEmpty) {
      MPUtil.log("Result ttp pay: $result");
      _processHandleResultPay(result, false);

      var resultPay = jsonDecode(result) as Map<String, dynamic>;
      TransResult transResult = TransResult.fromJson(resultPay);
      Get.toNamed(AppRoutes.TRANS_RESULT, arguments: transResult);
    }
  }

  _onPressFeatureInstallment(int isPayCard, int isPayLink, {MPItemMenuHome? mpItemMenuHome, Map? mapArgument}) async {
    List<ItemInstallmentBottomSheetOption> listItemInstallmentBottomSheetOption = [];
    bool isLoginByEmail = muid.contains('@');
    bool mcAllowCard = isLoginByEmail != true && isPayCard == 1;
    bool mcAllowLink = isPayLink == 1;
    debugPrint("allow card: $mcAllowCard | allow link: $mcAllowLink");
    MPItemMenuHome? mpItemMenuHomeBNPLCheck = (mpDataLoginModel.buttonApps ?? [])
        .firstWhereOrNull((element) => element.code == "MUA_TRUOC_TRA_SAU" || element.code == "BNPL_PAYMENT");
    if (mcAllowCard) {
      listItemInstallmentBottomSheetOption.add(ItemInstallmentBottomSheetOption(
        'CARD',
        TextUtils.getImageSVG('ic_installment_card.svg'),
        LocalizationCustom.localization("Quẹt thẻ tín dụng"),
      ));
    }
    if (mcAllowLink) {
      listItemInstallmentBottomSheetOption.add(ItemInstallmentBottomSheetOption(
        'LINK',
        TextUtils.getImageSVG('ic_installment_link.svg'),
        LocalizationCustom.localization("Tạo link trả góp"),
        subTitle: LocalizationCustom.localization("Nhập thẻ trên trang thanh toán"),
      ));
    }
    if (mpItemMenuHomeBNPLCheck != null) {
      MPBankQR bankQRBNPL = (mpDataLoginModel.listBankQR ?? []).firstWhere((element) => element.type == 'BNPL');
      for (MPQrChild item in (bankQRBNPL.qrChildren ?? [])) {
        listItemInstallmentBottomSheetOption.add(ItemInstallmentBottomSheetOption(
          'BNPL',
          item.logoChild,
          item.longNameChild,
          code: item.qrType,
          subTitle: LocalizationCustom.langCode == 'en'
              ? (mpItemMenuHomeBNPLCheck.titleEn ?? '')
              : (mpItemMenuHomeBNPLCheck.title ?? ''),
        ));
      }
    }
    if (rxShowFeatureCheckCardInstallment.value) {
      listItemInstallmentBottomSheetOption.add(ItemInstallmentBottomSheetOption(
        'CHECK_CARD',
        TextUtils.getImageSVG('ic_check_card.svg'),
        LocalizationCustom.localization("Tra cứu thẻ trả góp"),
        subTitle: LocalizationCustom.localization("Tra cứu điều kiện trả góp của thẻ"),
      ));
    }
    if (listItemInstallmentBottomSheetOption.isNotEmpty) {
      showMPBottomSheet(
        context!,
        // title: LocalizationCustom.langCode == 'en' ? (mpItemMenuHome?.titleEn ?? '') : (mpItemMenuHome?.title ?? ''),
        title: LocalizationCustom.localization("Chọn phương thức"),
        resizeToAvoidBottomInset: false,
        hasCloseBtn: false,
        action: MPTouchable(
          onPressed: openUserManual,
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              MPImageWidget.asset(
                path: TextUtils.getImageSVG("ic_guide_swipe.svg"),
              ),
              SizedBox(
                width: 4.sp,
              ),
              Text(
                LocalizationCustom.localization("Hướng dẫn"),
                style: TextStyle(
                  fontSize: 16.sp,
                  color: Color(0xFF008BF4),
                  fontWeight: FontWeight.w400,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
        body: Column(
          children: [
            for (final optionItem in listItemInstallmentBottomSheetOption)
              optionItem.type == 'CHECK_CARD'
                  ? Column(
                      children: [
                        Divider(
                          height: 0,
                          thickness: 1,
                          color: Color(0xFFE0E0E0),
                        ),
                        MPTouchable(
                          onPressed: () {
                            GAUtil.gaLogActionEvent(ActionTracking.home_bs_installment_press_check_card, null);
                            double amount = double.tryParse(amountValue.value) ?? 0.0;
                            HomeUtil.onPressCheckCard(context!, merchantId.toString(), amount: amount);
                          },
                          decoration: BoxDecoration(
                            color: Color(0xFFF4F4F4),
                          ),
                          padding: EdgeInsets.fromLTRB(32.sp, 16.sp, 16.sp, 16.sp),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: 24.0.sp,
                                height: 24.0.sp,
                                child: (optionItem.logoPath ?? '').startsWith('http')
                                    ? MPImageWidget.network(
                                        path: optionItem.logoPath ?? '',
                                      )
                                    : MPImageWidget.asset(
                                        path: optionItem.logoPath ?? '',
                                      ),
                              ),
                              SizedBox(width: 16.0.sp),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      optionItem.title ?? '',
                                      style: TextStyle(
                                        color: const Color(0xFF404041),
                                        fontSize: 18.0.sp,
                                        fontWeight: FontWeight.w600,
                                      ),
                                    ),
                                    (optionItem.subTitle ?? '').isEmpty
                                        ? const SizedBox.shrink()
                                        : Padding(
                                            padding: EdgeInsets.only(top: 2.0.sp),
                                            child: Text(
                                              optionItem.subTitle ?? '',
                                              style: TextStyle(
                                                color: const Color(0xFF808890),
                                                fontSize: 14.0.sp,
                                                fontWeight: FontWeight.w400,
                                              ),
                                            ),
                                          ),
                                  ],
                                ),
                              ),
                              MPImageWidget.asset(
                                path: TextUtils.getImageSVG('ic_chevron_right.svg'),
                              ),
                            ],
                          ),
                        ),
                      ],
                    )
                  : MPTouchable(
                      margin: EdgeInsets.only(left: 16.0.sp, right: 16.0.sp, bottom: 16.0.sp),
                      padding: EdgeInsets.all(16.0.sp),
                      onPressed: () {
                        Get.back();
                        switch (optionItem.type) {
                          case 'CARD':
                            GAUtil.gaLogActionEvent(ActionTracking.home_bs_installment_press_swipe_card, null);
                            _onPressFeatureInstallmentCard(isPayCard, isPayLink, mpItemMenuHome: mpItemMenuHome);
                            break;
                          case 'LINK':
                            GAUtil.gaLogActionEvent(ActionTracking.home_bs_installment_press_create_link, null);
                            _onPressFeatureInstallmentLink(mapArgument);
                            break;
                          case 'BNPL':
                            GAUtil.gaLogActionEvent(
                                ActionTracking.home_bs_installment_press_bnpl, {'mpos_bnpl_supplier': optionItem.code});
                            onPressFeature(mpItemMenuHomeBNPLCheck?.code ?? '',
                                mpItemMenuHome: mpItemMenuHomeBNPLCheck,
                                forceAvoidBottomSheetAmount: true,
                                codeBNPLItem: optionItem.code);
                            break;
                          default:
                            break;
                        }
                      },
                      decoration: BoxDecoration(
                        border: Border.all(color: const Color(0xFFD1D1D1), width: 1),
                        borderRadius: BorderRadius.circular(8.0.sp),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          SizedBox(
                            width: 24.0.sp,
                            height: 24.0.sp,
                            child: (optionItem.logoPath ?? '').startsWith('http')
                                ? MPImageWidget.network(
                                    path: optionItem.logoPath ?? '',
                                  )
                                : MPImageWidget.asset(
                                    path: optionItem.logoPath ?? '',
                                  ),
                          ),
                          SizedBox(width: 16.0.sp),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Text(
                                  optionItem.title ?? '',
                                  style: TextStyle(
                                    color: const Color(0xFF404041),
                                    fontSize: 18.0.sp,
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                                (optionItem.subTitle ?? '').isEmpty
                                    ? const SizedBox.shrink()
                                    : Padding(
                                        padding: EdgeInsets.only(top: 2.0.sp),
                                        child: Text(
                                          optionItem.subTitle!,
                                          style: TextStyle(
                                            color: const Color(0xFF808890),
                                            fontSize: 14.0.sp,
                                            fontWeight: FontWeight.w400,
                                          ),
                                        ),
                                      ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
            SizedBox(height: SessionData.screenPadding.bottom),
          ],
        ),
      );
    } else {
      AppUtils.showDialogAlert(context!,
          description: LocalizationCustom.localization(
              "You have not integrate payment with MPOS therefore you cannot do transaction by this way. Please contact hotline 1900.6364.88 to register re-login with MPOS reader or link card payment method."));
    }
  }

  onPressUpgradeMposDevice(){
    AppUtils.showDialogAlert(
        context!,
        image: TextUtils.getImageSVG("ic_update2"),
        title: LocalizationCustom.localization("dialog Upgrade configuration MPOS device"),
        description: LocalizationCustom.localization("The card reader needs to update the configuration (Firmware) to support more card types, improve features and security. The update process takes about %@ minutes. Please choose the appropriate time to avoid affecting the transaction.", arg: "10"),
        isTwoButton: true,
        text1stButton: LocalizationCustom.localization("Later"),
        text2ndButton: LocalizationCustom.localization("Upgrade"),
        onPress2ndButton: (){
          NativeBridge().callNativeUpgradeDevice();
        }
    );
  }

  Future<void> openUserManual() async {
    final Uri url = Uri.parse(installmentGuide);
    if (!await MPUtil.launchUrl(url)) {
      AppUtils.showDialogWithMessage(context!, LocalizationCustom.localization("Can not open link"));
    }
  }

  _onPressFeatureInstallmentLink(Map? mapArgument){
    // Old by React Native
    // Map argumentSent = mapArgument ?? {};
    // argumentSent['enableNewUI'] = true;
    // NativeBridge().callReactModule(argumentSent, serviceCode: "INSTALLMENT_PAYMENT");

    // New by Flutter
    double amount = double.tryParse(amountValue.value) ?? 0.0;
    _doFunctionInstallment(
      context: context!,
      mpDataLoginModel: mpDataLoginModel,
      amount: amount,
      installmentPromotion: installmentPromotion,
      currentPhone: currentPhone.value,
      currentEmail: currentEmail.value,
      currentDescription: currentDescription.value,
      isPayLink: true,
      dataInput: null,
      processHandleResultPay: null,
      onPressFeatureInstallmentCard: null,
      allowOtherPaymentMethod: false,
      callbackOtherPaymentMethod: null,
    );
  }

  _onPressFeatureInstallmentCard(int isPayCard, int isPayLink, {MPItemMenuHome? mpItemMenuHome}) async {
    double amount = double.tryParse(amountValue.value) ?? 0.0;
    if (isPayCard == 0 && isPayLink == 0) {
      AppUtils.showDialogAlert(context!,
          description: LocalizationCustom.localization(
              "You have not integrate payment with MPOS therefore you cannot do transaction by this way. Please contact hotline 1900.6364.88 to register re-login with MPOS reader or link card payment method."));
    } else if (isPayCard == 1) {
      MPItemMenuHome? mpItemMenuHomeSwipeCheck =
          (mpDataLoginModel.buttonApps ?? []).firstWhereOrNull((element) => element.code == 'QUET_THE');
      Map argumentsInput = {
        'title': LocalizationCustom.localization("Kiểm tra thẻ trả góp"),
        'showPayLink': isPayLink == 1,
        'amount': amount,
        'allowOtherPaymentMethod': mpItemMenuHomeSwipeCheck != null,
        'onSwipeSuccess': (resultCard) {
          if (resultCard['useOtherPaymentMethod'] == true) {
            onPressFeature('QUET_THE');
          } else if (resultCard['data'] != null) {
            _doFunctionInstallment(
              context: context!,
              mpDataLoginModel: mpDataLoginModel,
              amount: amount,
              installmentPromotion: installmentPromotion,
              currentPhone: currentPhone.value,
              currentEmail: currentEmail.value,
              currentDescription: currentDescription.value,
              isPayLink: resultCard['isPayLink'] ?? false,
              dataInput: resultCard['data'],
              processHandleResultPay: _processHandleResultPayInstallment,
              onPressFeatureInstallmentCard: () =>
                  _onPressFeatureInstallmentCard(isPayCard, isPayLink, mpItemMenuHome: mpItemMenuHome),
              allowOtherPaymentMethod: mpItemMenuHomeSwipeCheck != null,
              callbackOtherPaymentMethod: (featureName) => onPressFeature(featureName),
            );
          }
        },
      };
      Get.toNamed(AppRoutes.SWIPE_CARD, arguments: argumentsInput);
    } else if (isPayLink == 1) {
      _doFunctionInstallment(
        context: context!,
        mpDataLoginModel: mpDataLoginModel,
        amount: amount,
        installmentPromotion: installmentPromotion,
        currentPhone: currentPhone.value,
        currentEmail: currentEmail.value,
        currentDescription: currentDescription.value,
        isPayLink: true,
        dataInput: null,
        processHandleResultPay: null,
        onPressFeatureInstallmentCard: null,
        allowOtherPaymentMethod: false,
        callbackOtherPaymentMethod: null,
      );
    }
  }

  RxBool refreshingBalance = false.obs;
  RxInt accountBalance = 0.obs;

  getAccountBalance() async {
    if (refreshingBalance.value) return;
    refreshingBalance.value = true;
    MPBaseResponse response = await MPApiUtil.request(MPApiConstant.urlApi, {
      'serviceName': 'GET_ACCOUNT_BALANCE_V2',
      'merchantId': merchantId,
      'merchantEmail': emailMerchant,
    });
    refreshingBalance.value = false;
    if (response.result == true) {
      accountBalance.value = response.data['merchantBalance'] ?? 0;
    }
  }

  bool _checkRequiredAmount() {
    if (amountValue.value == '' || amountValue.value == '0') {
      showAmountBlock(true);
      errorAmountMessage.value = 'Bạn chưa nhập số tiền'.tr;
      return false;
    }
    return true;
  }

  bool _checkMinAmount(String code) {
    double minAmount = getMinAmountByServiceCode(code, mpDataLoginModel);
    double amountPay = double.tryParse(amountValue.value) ?? 0.0;
    if (amountPay < minAmount) {
      errorAmountMessage.value = LocalizationCustom.localization("Nhập tối thiểu {minAmount}đ để thực hiện thanh toán")
          .replaceFirst('{minAmount}', MPUtil.formatCurrency(minAmount));
      return false;
    }
    return true;
  }

  bool _checkEmailLogon(String user) {
    if (user.isValidEmail()) {
      var mess = LocalizationCustom.localization("Bạn đang đăng nhập ứng dụng bằng email:") +
          " $user.\n" +
          LocalizationCustom.localization(
              "Vui lòng sử dụng tài khoản thanh toán đã được cấp để sử dụng chức năng này.");
      AppUtils.showDialogAlert(
        context!,
        isTwoButton: true,
        description: mess,
        text1stButton: LocalizationCustom.localization(LocalizationCustom.close),
        text2ndButton: LocalizationCustom.localization(LocalizationCustom.agree),
        onPress2ndButton: () {
          AppConfiguration().clearAll();
          Get.offAllNamed(AppRoutes.LOGIN_PAGE);
        },
      );
      return true;
    }
    return false;
  }

  Future<bool> checkLimitAmount(int amountPay) async{
    MPUtil.log('check limit amount: muid=$muid | mid=${mpDataLoginModel.mid??''}');
    if((mpDataLoginModel.mid??'') == '') {
      return false;
    }
    _appController.showAppLoading();
    var result = await MposRequest.fetchLimitAmount(muid, mpDataLoginModel.mid??'');
    _appController.hideAppLoading();

    if(result.isNotEmpty) {
      AmountLimit amountLimit = AmountLimit.fromJson(jsonDecode(result));
      if (amountLimit.error?.code == ErrorCode.DO_SERVICE_SUCCESS) {
        MPUtil.log('limit: tran=${amountLimit.midMaxAmountPerTransation} | day=${amountLimit.midMaxAmountPerDay} '
            '| month=${amountLimit.midMaxAmountPerMonth}');
        if (amountLimit.midMaxAmountPerTransation > 0 && amountPay > amountLimit.midMaxAmountPerTransation) {
          errorAmountMessage.value = 'Bạn không thể giao dịch quá số tiền được phép trên 1 giao dịch({maxAmount}).'
              .tr
              .replaceFirst('{maxAmount}', MPUtil.formatCurrency(amountLimit.midMaxAmountPerTransation));
          return true;
        }
        if (amountLimit.midMaxAmountPerDay > 0
            && amountPay + amountLimit.midTotalAmountDay > amountLimit.midMaxAmountPerDay) {
          errorAmountMessage.value = 'Bạn đã giao dịch quá số tiền được phép trong ngày. Vui lòng thực hiện giao dịch vào hôm sau.'.tr;
          return true;
        }
        if (amountLimit.midMaxAmountPerMonth > 0
            && amountPay + amountLimit.midTotalAmountMonth > amountLimit.midMaxAmountPerMonth) {
          errorAmountMessage.value = 'Bạn đã giao dịch quá số tiền được phép trong tháng. Vui lòng đợi qua tháng để có thể tiếp tục.'.tr;
          return true;
        }
      }
    }
    return false;
  }

  _processHandleResultPay(String data, bool useScreenNativeSuccess) async {
    var result = jsonDecode(data);
    if (result['result']['status'] == 'APPROVED') {
      _resetDataPay();
      if (!useScreenNativeSuccess){
        //Edit for sound Promotion
        var transResult = TransResult.fromJson(result);
        transResult.descriptionPayment = currentDescription.value;
        Get.toNamed(AppRoutes.TRANS_RESULT, arguments: transResult);
      }
    }
  }

  _processHandleResultPayInstallment(String data, Map dataInstallment) async {
    Map result = jsonDecode(data);
    debugPrint('///////////////////// _processHandleResultPayInstallment: $result');
    debugPrint('///////////////////// _processHandleInstallmentData: $dataInstallment');
    if (result['result']['status'] == 'APPROVED') {
      // remove curr data pay
      _resetDataPay();
      bool checkPrint = NativeBridge().isSupportPrinter();
      int? timeInt = int.tryParse(result['userCard']['transactionDate'].toString());
      Get.to(
        InstallmentResultScreen(
          resultFromMposSDK: result,
          installmentResultEntity: InstallmentResultModel(
            success: true,
            errorMsg: '',
            customerName: result['userCard']?['cardHolderName'] ?? '',
            cardType: result['wfInfo']?['issuerCode'] ?? '',
            cardNumber: result['userCard']?['pan'] ?? '',
            time: timeInt != null && timeInt > 0
                ? DateFormat('HH:mm, dd/MM/yyyy').format(DateTime.fromMillisecondsSinceEpoch(timeInt))
                : '',
            tranCode: result['result']?['transactionId'] ?? '',
            amount: (result['userCard']?['amountAuthorized'] ?? 0).toString(),
            originalAmount: (dataInstallment['originalAmount'] ?? 0).toString(),
            fee: (dataInstallment['customerInstallmentFee'] ?? 0).toString(),
            period: (dataInstallment['period'] ?? 0).toString(),
            monthly: ((double.tryParse(result['userCard']['amountAuthorized'].toString()) ?? 0.0) /
                    (double.tryParse(dataInstallment['period'].toString()) ?? 1.0))
                .round()
                .toString(),
            userName: muid,
            serial: AppConfiguration().deviceMpos.deviceType == DeviceType.READER_SP01.index ||
                    AppConfiguration().deviceMpos.deviceType == DeviceType.READER_SP02.index
                ? mposDevice.deviceSerial
                : recentConnectedDeviceSerial.value,
            isFlagNoSignature: result['wfInfo']?['isFlagNoSignature'] == true,
            enablePrint: checkPrint,
            ruleNote: dataInstallment['ruleNote'] ?? '',
            iconTypeCard: dataInstallment['iconTypeCard'] ?? '',
            followKey: dataInstallment['followKey'] ?? '',
            promotionCode: dataInstallment['payloadPrepareTransaction']?['installmentInfo']?['promotionCode'] ?? '',
          ),
        ),
        opaque: false,
      );
    } else {
      Get.to(
        InstallmentResultScreen(
          resultFromMposSDK: result,
          installmentResultEntity: InstallmentResultModel(
            success: false,
            errorMsg: result['result']?['error']?['message'] ?? '',
            customerName: dataInstallment['cardholder'] ?? '',
            cardType: dataInstallment['payloadPrepareTransaction']?['installmentInfo']?['issuerCode'] ?? '',
            cardNumber: result['userCard']?['pan'] ?? '',
            time: DateFormat('HH:mm, dd/MM/yyyy').format(DateTime.now()),
            tranCode: '',
            amount: (result['userCard']?['amountAuthorized'] ?? 0).toString(),
            originalAmount: (dataInstallment['originalAmount'] ?? 0).toString(),
            fee: (dataInstallment['customerInstallmentFee'] ?? 0).toString(),
            period: (dataInstallment['period'] ?? 0).toString(),
            monthly: ((double.tryParse(result['userCard']['amountAuthorized'].toString()) ?? 0.0) /
                    (double.tryParse(dataInstallment['period'].toString()) ?? 1.0))
                .round()
                .toString(),
            userName: muid,
            serial: AppConfiguration().deviceMpos.deviceType == DeviceType.READER_SP01.index ||
                    AppConfiguration().deviceMpos.deviceType == DeviceType.READER_SP02.index
                ? mposDevice.deviceSerial
                : recentConnectedDeviceSerial.value,
            isFlagNoSignature: true,
            enablePrint: false,
            ruleNote: dataInstallment['ruleNote'] ?? '',
            iconTypeCard: dataInstallment['iconTypeCard'] ?? '',
            followKey: dataInstallment['followKey'] ?? '',
            promotionCode: dataInstallment['payloadPrepareTransaction']?['installmentInfo']?['promotionCode'] ?? '',
          ),
        ),
        opaque: false,
      );
    }
  }

  _resetDataPay() {
    amountValue.value = '0';
    currentPhone.value = '';
    currentEmail.value = '';
    currentDescription.value = '';
  }

  _gotoWebViewInfo(String title, String url) {
    // Map argument = {
    //   'url':url,
    //   'title': title
    // };
    // Get.toNamed(AppRoutes.WEBVIEW_INFO, arguments: argument);
    AppUtils.openWebAppModule(context!, url, title: title);
  }

  void updateFlagUpgradeMposDevice(bool flagRequireUpgradeMposDevice) {
    flagRequireUpdateMposDevice.value = flagRequireUpgradeMposDevice;
  }
}

class ItemInstallmentBottomSheetOption {
  String? type;
  String? logoPath;
  String? title;
  String? subTitle;
  String? code;

  ItemInstallmentBottomSheetOption(this.type, this.logoPath, this.title, {this.subTitle, this.code});
}

class HomeUtil {
  static onPressCheckCard(BuildContext context, String merchantId, {double? amount}) {
    String params = 'merchantId=$merchantId&isMobile=1&lang=${LocalizationCustom.langCode == 'en' ? 'eng' : 'vie'}';
    if (amount != null && amount > 0) {
      params += '&amount=$amount';
    }
    MPMasterAction.instance.startMposModule(
      mposModule: MposModuleWebapp(),
      getXBackupKey: Get.key,
      context: context,
      locale: Get.locale,
      designSize: const Size(375, 812),
      transitionDuration: const Duration(milliseconds: 200),
      arguments: MPInputArgs()
        ..data = WebappParam(
          url: Uri.parse(
              'https://kiemtrathe.mpos.vn/?params=${base64.encode(utf8.encode(params))}'),
          title: LocalizationCustom.localization("Kiểm tra thẻ trả góp"),
          titleStyle: style_S20_W600_WhiteColor.copyWith(fontSize: 20.0.sp),
          primaryColor: Configuration.mainColour,
          foregroundHeaderColor: Configuration.whiteColor,
          titleExitConfirm: LocalizationCustom.localization("Bạn muốn thoát trang này?"),
          enableBackForward: false,
          enableExitConfirm: false,
          enableReload: false,
          backgroundColor: Configuration.whiteColor,
          backgroundHeaderColor: Configuration.mainColour,
          backType: 'back',
          fontFamily: 'BeVietnamPro',
        ),
    );
  }
}
