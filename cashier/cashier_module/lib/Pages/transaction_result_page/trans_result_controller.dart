import 'dart:convert';

import 'package:cashiermodule/Class/SpeakerHandler.dart';
import 'package:cashiermodule/Model/device_mpos.dart';
import 'package:cashiermodule/Model/trans_result.dart';
import 'package:cashiermodule/Pages/sme_card/sme_card_controller.dart';
import 'package:cashiermodule/app/app_route.dart';
import 'package:cashiermodule/model_instance/app_configuration.dart';
import 'package:cashiermodule/widget_custom/widget_receipt/widget_receipt_pay_normal.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:screenshot/screenshot.dart';

import '../../Utilities/NativeBridge.dart';
import '../../Utilities/SystemPreference.dart';
import '../../Utilities/TextUtils.dart';
import '../../widget_custom/widget_receipt/widget_receipt_qr.dart';

class TransResultBinding extends Bindings {
  @override
  void dependencies() {

    Get.lazyPut(() => TransResultController(), fenix: true);
  }
}

class TransResultController extends GetxController {
  late BuildContext context;
  TransResult transResult = TransResult();
  bool supportPrint = false;
  bool actionAddCard = false;
  final ScreenshotController screenShotController = ScreenshotController();

  @override
  void onInit() {
    super.onInit();
    if (AppConfiguration().deviceMpos.deviceType ==
            DeviceType.READER_SP01.index ||
        AppConfiguration().deviceMpos.deviceType ==
            DeviceType.READER_SP02.index) {
      supportPrint = true;
    }
    var argument = Get.arguments;
    if (argument is TransResult) {
      transResult = argument;
      if (transResult.local_route == AppRoutes.SME_CARD_PAGE) {
        actionAddCard = true;
        supportPrint = false;
      }
    }
    getPrintConfig();
  }

  @override
  void onReady() {
    super.onReady();
    speakLstPromotion();
  }

  void speakLstPromotion(){
    if (transResult.promotionInfo != null){
      SpeakerHandler.speakWithAmount(int.parse(transResult.promotionInfo!.first.amountDiscount));
    }
  }

  void getPrintConfig() async {
    int mposSettingPrint = await SystemPreference.getData(
        SystemPreference.KEY_AUTO_PRINT, 0);
    if (mposSettingPrint != 0) {
      printCardReceiptWidget();
    }
  }

  void printCardReceiptWidget() {
    if (supportPrint && transResult.wfInfo != null) {
      WfInfo wfInfo = transResult.wfInfo!;
      UserCard userCard = transResult.userCard ?? UserCard();
      DateTime dateTime =
      DateTime.fromMillisecondsSinceEpoch(int.parse(wfInfo.transactionDate ?? "0"), isUtc: true).add(Duration(hours: 7));
      screenShotController
          .captureFromWidget(WidgetReceiptPayNormal(
        printTID: wfInfo.tid,
        printMID: wfInfo.mid,
        printRef: wfInfo.rrn,
        printInvoice: wfInfo.invoiceNo,
        printBatch: wfInfo.batchNo,
        printApprove: wfInfo.authCode,
        printPan: wfInfo.pan,
        printHolder: wfInfo.cardHolderName,
        printCardType: wfInfo.appl,
        printAmount: int.parse(wfInfo.amount ?? '0'),
        printDate: DateFormat('dd/MM/yyyy').format(dateTime),
        printTime: DateFormat('HH:mm:ss').format(dateTime),
        printSign: userCard.userSignature,
        printMCName: wfInfo.merchantName,
        printMCAddress: wfInfo.merchantAddress,
        printTxid: wfInfo.txid,
        printDes: transResult.descriptionPayment,
        printIsVoid: false,
        isTransSkipSignature: wfInfo.flagNoSignature,
        screenshotController: screenShotController,
      ))
          .then((value) {
        final imageEncoded = base64Encode(value);
        NativeBridge().callNativePrintReceipt(imageEncoded);
      });
    }
  }

  void printQRReceiptWidget(String transDate, String transId, String refNo,
      String appCode, String type, String desc, String amount) {
    if (supportPrint) {
      screenShotController
          .captureFromWidget(WidgetReceiptQR(
          merchantName: AppConfiguration().merchantName,
          mid: AppConfiguration().mId,
          tid: AppConfiguration().tId,
          transDate: transDate,
          transId: transId,
          refNo: refNo,
          appCode: appCode,
          type: type,
          desc: desc,
          amount: amount))
          .then((value) {
        final imageEncoded = base64Encode(value);
        NativeBridge().callNativePrintReceipt(imageEncoded);
      });
    }
  }

  void popToHomePage() {
    if (transResult.local_route.isNotEmpty) {
      // Get.until(ModalRoute.withName(AppRoutes.FWD_HOME_PAGE));
      if (transResult.local_route == AppRoutes.SME_CARD_PAGE) {
        Get.find<SMECardController>().fetchData(true, refresh: true);
      }
      Get.until(ModalRoute.withName(transResult.local_route));
    } else {
      Get.back();
    }
  }
}
