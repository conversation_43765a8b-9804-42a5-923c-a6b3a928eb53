import 'package:cashiermodule/Pages/transaction_result_page/trans_result_controller.dart';
import 'package:cashiermodule/Utilities/LocalizationCustom.dart';
import 'package:cashiermodule/Utilities/TextUtils.dart';
import 'package:cashiermodule/Utilities/constants.dart';
import 'package:cashiermodule/app/base_view.dart';
import 'package:cashiermodule/constants/style.dart';
import 'package:cashiermodule/model_instance/app_configuration.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:mpos_module_base/mpos_module_base_widget.dart';

import '../../Utilities/configuration.dart';

class TransResultPage extends BaseView<TransResultController> {
  @override
  Widget builder(BuildContext context) {
    controller.context = context;

    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 10),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 10),
                  child: ListView(
                    children: [
                      controller.actionAddCard ? _buildInfoAddCardWidget() : Container(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.start,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            SizedBox(height: 50,),
                            Container(
                              width: 80,
                              height: 80,
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(40),
                                  color: controller.transResult.result?.status == PaymentStatusValue.Approved
                                      ? Configuration.greenMainColour
                                      : Configuration.mainColour),
                              child: Icon(
                                controller.transResult.result?.status == PaymentStatusValue.Approved ? Icons.check : Icons.close,
                                size: 50,
                                color: Configuration.whiteColor,
                              ),
                            ),
                            SizedBox(
                              height: 30,
                            ),
                            Text(
                              controller.transResult.result?.status == PaymentStatusValue.Approved ? LocalizationCustom.localization("Transaction successful") : LocalizationCustom.localization("Transaction Fail"),
                              style: TextStyle(
                                color: Configuration.greenMainColour,
                                fontFamily: kFontFamilyBeVietnamPro,
                                fontSize: Configuration.fontTitle,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            SizedBox(
                              height: 10,
                            ),
                            Visibility(
                              visible: controller.transResult.result != null,
                              child: Text(
                                TextUtils.formatAmount(double.parse(controller.transResult.result != null ? controller.transResult.result!.amount.toString() : "0")) + " đ",
                                style: TextStyle(
                                  color: Configuration.blackMainColour,
                                  fontFamily: kFontFamilyBeVietnamPro,
                                  fontSize: Configuration.fontNormal * 2,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            // SizedBox(
                            //   height: 40,
                            // ),
                            // widgetItemTransInfo(LocalizationCustom.localization("Create date"), TextUtils.parserTimeFormat(0)),
                            SizedBox(
                              height: 20,
                            ),
                            Visibility(
                              visible: controller.transResult.result != null && controller.transResult.result!.error !=null
                                  && controller.transResult.result!.error!.message != null && controller.transResult.result!.error!.message!.isNotEmpty,
                              child: Text(
                                LocalizationCustom.localization(controller.transResult.result!.error?.message),
                                style: TextStyle(
                                  color: Configuration.mainColour,
                                  fontFamily: kFontFamilyBeVietnamPro,
                                  fontSize: Configuration.fontButton,
                                  fontWeight: FontWeight.w400,
                                ),
                              ),
                            ),
                            Visibility(
                              visible: controller.transResult.result != null && controller.transResult.result!.transactionId!=null
                                  && controller.transResult.result!.transactionId!.isNotEmpty,
                              child: widgetItemTransInfo(LocalizationCustom.localization("Transaction code"),
                                  controller.transResult.result!.transactionId??""),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              ),
              SizedBox(height: 20,),
              Container(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 10),
                      // height: 50,
                      child: Row(
                        children: [
                          Visibility(
                              visible: controller.supportPrint,
                              child: Padding(
                                padding: const EdgeInsets.only(right: 10),
                                child: MPButton(
                                  backgroundColor: Color.fromRGBO(252, 244, 244, 1),
                                  onPressed: () {
                                    controller.printCardReceiptWidget();
                                  },
                                  child: Image.asset(TextUtils.getImage("ic_print.png")),
                                ),
                              )),
                          Expanded(
                            child: MPButton(
                              titleStyle: style_S16_W400_WhiteColor,
                              backgroundColor: Configuration.blue2MainColor,
                              onPressed: () {
                                controller.popToHomePage();
                              },
                              title: controller.actionAddCard ? LocalizationCustom.localization("Hoàn tất") : LocalizationCustom.localization("Go to Home"),
                            ),
                          )
                        ],
                      ),
                    ),
                    SizedBox(height: 10,),
                    Padding(
                      padding: const EdgeInsets.only(left: 10.0, right: 10.0, bottom: 10),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Row(
                            children: [
                              SizedBox(height: 10,),
                              Text(AppConfiguration().mobileUser +
                                  (AppConfiguration().deviceMpos.deviceSerial.isNotEmpty
                                      ? " | ${AppConfiguration().deviceMpos.deviceSerial}"
                                      : ""), style: style_S12_W400_BlackColor,),
                            ],
                          ),
                          Row(
                            children: [
                              Container(child: Icon(Icons.phone_in_talk, color: Configuration.blueMainColour, size: 18,), padding: EdgeInsets.all(5),),
                              Text(Configuration.hotlineNumber, style: style_S14_W400_BlueColor,)
                            ],
                          )
                        ],
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget widgetItemTransInfo(String title, String value) {
    if (controller.transResult.result != null){
      return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocalizationCustom.localization(title),
            style: TextStyle(
              color: Configuration.hintMainColor,
              fontFamily: kFontFamilyBeVietnamPro,
              fontSize: Configuration.fontButton,
              fontWeight: FontWeight.w400,
            ),
          ),
          Text(
            LocalizationCustom.localization(value),
            style: TextStyle(
              color: Configuration.blackMainColour,
              fontFamily: kFontFamilyBeVietnamPro,
              fontSize: Configuration.fontButton,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      );
    }
    return SizedBox();
  }

  Widget _buildInfoAddCardWidget() {
    return Container(
        child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
          SizedBox(
            height: 50,
          ),
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(40),
                color: Configuration.green_success_add_card),
            child: Icon(
              Icons.check,
              size: 50,
              color: Configuration.whiteColor,
            ),
          ),
          SizedBox(
            height: 30,
          ),
          Text(
            LocalizationCustom.localization("Thêm thẻ thành công"),
            style: TextStyle(
              color: Configuration.green_success_add_card,
              fontFamily: kFontFamilyBeVietnamPro,
              fontSize: Configuration.fontTitle,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(
            height: 10,
          ),
          Container(
            child: DottedBorder(
              color: Configuration.blueMainColour,
              strokeWidth: 1,
              radius: Radius.circular(10),
              borderType: BorderType.RRect,
              dashPattern: [3, 3],
              child: Container(
                padding: EdgeInsets.all(15),
                decoration: BoxDecoration(
                  color: Configuration.blueLightMainColour.withOpacity(0.5),
                ),
                child: Text(
                    '${LocalizationCustom.localization("noticeAddCardSuccess")}'),
              ),
            ),
          ),
          SizedBox(
            height: 20,
          ),
          _buildItemInfo('${LocalizationCustom.localization('customer')}:', '${LocalizationCustom.localization('time')}:', style_S14_W400_HintColor),
          _buildItemInfo(
              '${controller.transResult.userCard?.cardHolderName} \n ${controller.transResult.userCard?.applicationLabel} - ${controller.transResult.userCard?.pan}',
              '${controller.transResult.result?.createdDate ?? ''}',
              style_S14_W400_BlackColor),
        ]));
  }

  Widget _buildItemInfo(String title, String value, TextStyle myStyle) {
    return Container(
      child: Row(
        // mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(child: Text(title, style: myStyle,)),
          SizedBox(width: 10,),
          Expanded(child: Text(value, style: myStyle,)),
        ],
      ),
    );
  }
}
