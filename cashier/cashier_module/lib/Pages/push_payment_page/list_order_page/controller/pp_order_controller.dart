import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:convert';
import 'package:crypto/crypto.dart';
import 'package:cashiermodule/Class/brightness_control.dart';
import 'package:cashiermodule/Class/connectivityStatus.dart';
import 'package:cashiermodule/Model/qrDataContent.dart';
import 'package:cashiermodule/Model/qr_dynamic_response.dart';
import 'package:cashiermodule/Model/qr_dynamic_status_response.dart';
import 'package:cashiermodule/Model/qr_static_response.dart';
import 'package:cashiermodule/Pages/home_new/controller/home_new_controller.dart';
import 'package:cashiermodule/Pages/home_new/controller/swipe_card_controller.dart';
import 'package:cashiermodule/Pages/home_new/screen/swipe_card_screen.dart';
import 'package:cashiermodule/Pages/home_new/widget/mp_botom_sheet.dart';
import 'package:cashiermodule/Pages/push_payment_page/class/setting_model.dart';
import 'package:cashiermodule/Pages/push_payment_page/pass_code_page/passs_code_page.dart';
import 'package:cashiermodule/Pages/push_payment_page/push_payment_home/controller/push_payment_home_controller.dart';
import 'package:cashiermodule/Utilities/app_utils.dart';
import 'package:cashiermodule/Utilities/constants.dart';
import 'package:cashiermodule/app/app_route.dart';
import 'package:cashiermodule/controller/app_controller.dart';
import 'package:cashiermodule/model_instance/app_configuration.dart';
import 'package:cashiermodule/model_instance/mqtt_client.dart';
import 'package:cashiermodule/widget_custom/widget_receipt/widget_receipt_qr.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:mpos_module_base/mpos_module_base_widget.dart';
import 'package:screenshot/screenshot.dart';
import 'package:uuid/uuid.dart';
import 'package:wakelock_plus/wakelock_plus.dart';

import '../../../../BaseService/base_service.dart';
import '../../../../BaseService/macq_request.dart';
import '../../../../BaseService/mpos_request.dart';
import '../../../../BaseService/nl_vcb_request.dart';
import '../../../../Class/ForegroundService.dart';
import '../../../../Model/errorResponse.dart';
import '../../../../Model/historyCardMposTrans.dart';
import '../../../../Model/orderRethink.dart';
import '../../../../Utilities/SystemPreference.dart';
import '../../../../Utilities/configuration.dart';
import '../../../../Utilities/LocalizationCustom.dart';
import '../../../../Utilities/Logger.dart';
import '../../../../Utilities/NativeBridge.dart';
import '../../../../Utilities/TextUtils.dart';
import '../../../../constants/style.dart';
import '../../../../model_instance/rethink_connect.dart';
import '../widgets/widget_confirm_password.dart';
import '../widgets/widget_noti_transqr.dart';
import '../widgets/widget_pop_order_state.dart';

class ListOrderBinding extends Bindings {
  @override
  void dependencies() {
    Get.lazyPut(() => ListOrderController(), fenix: true);
  }
}

enum GET_QR_RESPONSE_STATUS {
  GET_QR_RESPONSE_SUCCESS,
  GET_QR_RESPONSE_FAIL,
  GET_QR_RESPONSE_LOADING
}

class ListOrderController extends GetxController {
  final String TAG = "[PUSHPAYMENT]";
  late BuildContext context;
  final ScreenshotController screenShotController = ScreenshotController();
  var _pushpaymentHomeController = Get.find<PushPaymentHomeController>();
  var _appConfiguration = AppConfiguration();
  var _appController = Get.find<AppController>();
  var _nativeBridge = NativeBridge();
  var _brightnessControl = BrightnessControl();
  var rethinkConnect = RethinkConnect();
  var connectionState = EmqxClient.emqxAlive.obs;
  var blockPaymentComing = false;
  var isQRActive = false.obs;
  var isInstallmentActive = false.obs;
  var isQRLocalActive = false.obs;
  var isAutoPay = false;
  var isOnlyShowOrder = false;
  var flagEmqxEnable = false.obs;
  var flagQrNL = false.obs;
  var flagAutoBrightnessEnable = true;

  var qrStatic = QrStaticResponse().obs;
  List<String> lstTransApproved = <String>[];
  List<Map> lstOrderNotSync = <Map>[];

  var isShowHistory = false.obs;
  var posid = "".obs;
  var lstOrder = <OrderRethink>[].obs;
  var switchLanguage = false.obs;
  var urlLocalQR = "".obs;

  Timer? timerSyncTransApproved;
  Timer? timerRefreshUrlLocal;
  Timer? timerCheckOrder;
  final int timeDelay = 1;
  final int timeSync = 5;
  final int delayCloseDialog = 10;

  var supportPrintReceipt = false;
  var cardProcessing = false;
  var orderIdProcessing = "";
  var orderIdQRProcessing = "";
  var currentBrightness = 1.0;
  var password = '';

  @override
  void onInit() {
    super.onInit();
    var argument = Get.arguments;
    if (argument != null) {
      posid.value = argument;
    }
    timerProcessOrder();
    getCurrentLanguage();
    checkPrinterSupport();
  }

  @override
  void onClose() {
    super.onClose();
    if (timerCheckOrder!.isActive) {
      timerCheckOrder!.cancel();
    }
    if (timerRefreshUrlLocal != null && timerRefreshUrlLocal!.isActive) {
      timerRefreshUrlLocal!.cancel();
    }
    setEnableTouchFunction(true);
    _brightnessControl.resetBrightnessControl();
    rethinkConnect.stopRunTimeHeathCheck();
    ForegroundService.stopService();
  }

  @override
  void onReady() {
    super.onReady();
    instanceLocalFlag();
    changeFeedListOrder();
    if (flagEmqxEnable.value) {
      _pushpaymentHomeController.emqxConnect.subscribeTopic();
    } else {
      rethinkConnect.runTimeHeathCheck(
          posid.value, _appConfiguration.rethinkConfig.merchantId);
    }
    // getListOrder();
    if (password.isNotEmpty){
      setEnableTouchFunction(false);
    }
    _brightnessControl.initScreens([AppRoutes.PP_LIST_ORDER_PAGE]);
    brightnessResumeControl();
    if (PPSettingModel().supportLocalQRContent == true &&
        PPSettingModel().localQRContent.isNotEmpty) {
      isQRLocalActive.value = true;
    }
    startTimeRefreshUrlLocal();
    ForegroundService.initializeService();
    ForegroundService.startService();
    WakelockPlus.enable();
    Logger().PushLog();
  }

  void instanceLocalFlag() {
    urlLocalQR.value = PPSettingModel().localQRContent;
    flagEmqxEnable.value = AppConfiguration().emqxEnable;
    flagQrNL.value =
        AppConfiguration().configMerchant.permitQrNl == 1 ? true : false;
    debugPrint("FlagQRNL: ${flagQrNL.value}");
    if (_pushpaymentHomeController.ppSetting.value.supportLogoutConfirm) {
      password = _pushpaymentHomeController.ppSetting.value.localPassword;
    } else {
      password = AppConfiguration().rethinkConfig.muPassword;
    }
    isShowHistory.value = _appConfiguration.rethinkConfig.isShowHistory;
    qrStatic.value = _appConfiguration.dataQrStaticMa;
    isQRActive.value = _appConfiguration.vietQRPushPaymentAcive;
    isAutoPay = _appConfiguration.rethinkConfig.isAutoPay;
    isOnlyShowOrder =
        _pushpaymentHomeController.ppSetting.value.supportOnlyOrder;
    isInstallmentActive.value = _appConfiguration.installmentPushPaymentActive;
    if (flagQrNL.value) {
      isQRActive.value = true;
    }
    flagAutoBrightnessEnable = _pushpaymentHomeController.ppSetting.value.flagAutoScreenOff;
  }

  void setEnableTouchFunction(bool enable) {
    NativeBridge().setDeviceConfigDisableTouch(!enable);
    NativeBridge().callNativeDisableActionBar(!enable);
  }

  Future<void> checkPrinterSupport() async {
    if (AppConfiguration().isKozenP12){
      supportPrintReceipt = false;
      return;
    }
    var printIndexDefault = 0;
    if (AppConfiguration().appType != AppType.MPOS_LITE){
      printIndexDefault = Constants.AUTO_PRINT_DEFAULT;
    }else{
      if (!AppConfiguration().isKozenP12){
        printIndexDefault = 1;
      }
    }
    int mposSettingPrint = await SystemPreference.getData(
        SystemPreference.KEY_AUTO_PRINT, printIndexDefault);
    if (mposSettingPrint != 0) {
      supportPrintReceipt = true;
    }else{
      supportPrintReceipt = false;
    }
  }

  startTimeRefreshUrlLocal(){
    if (isQRLocalActive.value){
      if (timerRefreshUrlLocal != null && timerRefreshUrlLocal!.isActive) {
        timerRefreshUrlLocal!.cancel();
      }
      final keyExisted = "currentTime".toLowerCase();
      bool checkKeyExisted =
          PPSettingModel().localQRContent.toLowerCase().contains(keyExisted);
      timerRefreshUrlLocal = Timer.periodic(Duration(seconds: kDebugMode ? 10 : 30), (timer) {
        var currentTime = DateTime.now().millisecondsSinceEpoch;
        if (checkKeyExisted) {
          urlLocalQR.value = "${PPSettingModel().localQRContent}$currentTime";
          print("url_refresh: ${urlLocalQR.value}");
        }
      });
    }
  }

  Future<void> timerProcessOrder() async {
    var ordersToRemove = [];
    timerCheckOrder = Timer.periodic(Duration(seconds: 1), (timer) {
      if (lstOrder.isNotEmpty) {
        lstOrder.forEach((element) async {
          element.counterCheckQRStatus = element.counterCheckQRStatus - 1;
          bool isProcessing = (element.order_code == orderIdProcessing &&
              blockPaymentComing == true);
          if (!isProcessing) {
            element.counterExpired = element.counterExpired - 1;
          }
          int delayStep = element.flagOrderPushPaymentEnable ? 5 : 10;
          if ((element.counterCheckQRStatus <= 0 &&
                  (element.counterCheckQRStatus % delayStep == 0)) &&
              element.counterCheckQRStatus >= -maxExpiredCounter) {
            requestCheckStatus(element);
          }
          if (element.counterExpired <= 0) {
            ordersToRemove.add(element);
          }
        });
        if (ordersToRemove.isNotEmpty) {
          ordersToRemove.forEach((element) {
            updateOrderFromList(element, Configuration.Type_Action_Order_Remove);
            requestRemoveOrder(element);
          });
        }
      }
    });
  }

  Future<String> getPackageInfo() async {
    var packageInfo = await AppConfiguration().getPackageInfo();
    return packageInfo.packageName;
  }

  Future<void> getCurrentLanguage() async {
    var languageApp = await SystemPreference.getLanguageApp();
    if (languageApp.isEmpty) {
      languageApp = 'vi';
    }
    languageApp == 'vi'
        ? switchLanguage.value = true
        : switchLanguage.value = false;
    updateLanguage();
  }

  // Future<void> connectionChanged(dynamic connectionChanged) async {
  //   if (!flagEmqxEnable.value) {
  //     if (connectionStatus.hasConnection && rethinkConnect.connectedStatus) {
  //       connectionState.value = EmqxClient.emqxAlive;
  //     } else {
  //       Logger().write("PUSHPAYMENT_NEED_RETRY_CONNECT");
  //       connectionState.value = EmqxClient.emqxReconnecting;
  //     }
  //   }
  // }

  // Future<void> parserNotification(Map mapNoti) async {
  //   if (mapNoti.isNotEmpty) {
  //     String udid = mapNoti["data"]["udid"] ?? "";
  //     Logger().write(TAG + "PUSH_NOTIFICATION_RECEIVED-UDID_VALUE: [$udid]");
  //     var orderRethink = findOrderWithUdid(udid);
  //     if (orderRethink != null) {
  //       if (udid.isNotEmpty && orderRethink.status != OrderStatus.Approved) {
  //         orderRethink.status = OrderStatus.Processing;
  //         updateOrderFromList(
  //             orderRethink, Configuration.Type_Action_Order_Update);
  //         requestCheckStatus(orderRethink);
  //       }
  //     }
  //   }
  // }

  Future<void> showNotification(Map data) async {
    if (_appConfiguration.isKozenP12 ||
        (AppConfiguration().appType == AppType.MPOS_LITE)) {
      return;
    }
    var dataReceived = data;
    if (Platform.isAndroid) {
      dataReceived = data["data"] ?? {};
    }
    String udid = dataReceived["udid"] ?? "";
    String amount = dataReceived["amount"] ?? "";
    String timePayment = dataReceived["timePayment"] ?? "";
    String cardHolderName = dataReceived["cardholderName"] ?? "";
    String icon = dataReceived["icon"];
    String pan = dataReceived["pan"];
    String status = dataReceived["transactionStatus"];
    bool staticQR = udid.contains("VAQR_STATIC");
    if (staticQR && status == PaymentStatusValue.Approved) {
      Get.showSnackbar(
        GetSnackBar(
          duration: Duration(seconds: 10),
          backgroundColor: Colors.transparent,
          snackPosition: SnackPosition.TOP,
          messageText: WidgetNotiTransQR(
            icon: icon,
            amount: amount,
            cardHolderName: cardHolderName,
            pan: pan,
            timePayment: timePayment,
          ),
        ),
      );
    }
  }

  Future<void> refreshConnection() async {
    // reloadWebController();
    if (kDebugMode) {
      testRequest();
    } else {
      if (!flagEmqxEnable.value) {
        await rethinkConnect.reconnectRethink(
            posid.value, int.parse(_appConfiguration.merchantId));
        getListOrder();
      }
    }
  }

  Future<void> testRequest() async {
    if (kDebugMode) {
      var orderCode = "${TextUtils().getRandomString(4)}-MPOS";
      int amount = int.parse("${TextUtils().getRandomNumber(1)}000000");
      MposRequest.addOrderPushPayment(
          int.parse(_appConfiguration.merchantId),
          orderCode,
          posid.value,
          amount,
          payMethod: "",    //PaymentMethod.Card
          payType:PaymentType.Installment,  //
          // qrType: ""
      );
      // _fucTestMd5(orderCode, amount, "orderDescription");
      // MacqRequest.requestMacqBlackListBinRemove("*********", "VIETCOMBANK", "********", "***************");
    }
  }

  Future<void> _fucTestMd5(String orderCode, int amount, String orderDescription) async {
    // 10/03/2025,16:30
    var currentDate = DateTime.now();
    var timeFormat = DateFormat('dd/MM/yyyy,HH:mm').format(currentDate.add(Duration(minutes: 120)));
    var merchant_site_code = "53056";
    var returnUrl = "https://mpos.vn";
    var receiver = "<EMAIL>";
    var secure_pass = "341f5c5467cff838ebffc8a3580c3189";
    var transaction_info = "TEST_LINK_NL";
    var order_code = orderCode;
    var price = amount;
    var currency = "vnd";
    var quantity = 1;
    var tax = 0;
    var discount = 0;
    var fee_cal = 0;
    var fee_shipping = 0;
    var order_description = orderDescription;
    var buyer_info = "Nguyen_Van_A";
    var affiliate_code = "";
    var lang = "vi";
    var secure_code = generateMd5(merchant_site_code +
        ' ' +
        returnUrl +
        ' ' +
        receiver +
        ' ' +
        transaction_info +
        ' ' +
        order_code +
        ' ' +
        price.toString() +
        ' ' +
        currency +
        ' ' +
        quantity.toString() +
        ' ' +
        tax.toString() +
        ' ' +
        discount.toString() +
        ' ' +
        fee_cal.toString() +
        ' ' +
        fee_shipping.toString() +
        ' ' +
        order_description +
        ' ' +
        buyer_info +
        ' ' +
        affiliate_code +
        ' ' +
        secure_pass);
    var cancel_url = "https://mpos.vn/cancel";
    var notify_url = "https://mpos.vn/notify";
    var time_limit = timeFormat;
    var urlGenerate = "https://sandbox.nganluong.vn/nl35/checkout.php"+
        "?merchant_site_code=$merchant_site_code"+
        "&return_url=$returnUrl"+
        "&receiver=$receiver"+
        "&transaction_info=$transaction_info"+
        "&order_code=$order_code"+
        "&price=$price"+
        "&currency=$currency"+
        "&quantity=$quantity"+
        "&tax=$tax"+
        "&discount=$discount"+
        "&fee_cal=$fee_cal"+
        "&fee_shipping=$fee_shipping"+
        "&order_description=$order_description"+
        "&buyer_info=$buyer_info"+
        "&affiliate_code=$affiliate_code"+
        "&lang=$lang"+
        "&secure_code=$secure_code"+
        "&cancel_url=$cancel_url"+
        "&notify_url=$notify_url"+
        "&time_limit=$time_limit";
    print("urlGenerate: $urlGenerate");
    var response = await BaseService().getData(fullUrl: urlGenerate);
    print("response get nl_link: $response");
  }

  String generateMd5(String input) {
    print("prepare md5: $input");
    return md5.convert(utf8.encode(input)).toString();
  }

  Future<void> getListOrder() async {
    var lstOrderTemp = [];
    if (flagEmqxEnable.value) {
      if (!isAutoPay && !flagQrNL.value){
        var responseGetList = await MposRequest.reqGetOrderPending(
            AppConfiguration().merchantId, posid.value);
        if (responseGetList.isNotEmpty) {
          try{
            lstOrderTemp = jsonDecode(responseGetList) ?? [];
            lstOrderTemp.sort((a, b) {
              var objA = DateFormat("yyyy-MM-dd hh:mm:ss").parse(a['createdDate'] ?? "");
              var objB = DateFormat("yyyy-MM-dd hh:mm:ss").parse(b['createdDate'] ?? "");
              return objA.compareTo(objB);
            });
          }catch (ex){
            Logger().write("SORT_LIST_ORDER Exception: ${ex.toString()}");
          }
        }
      }
    } else {
      lstOrderTemp = await rethinkConnect.fetchDataUseIndex(
          posid.value, _appConfiguration.rethinkConfig.merchantId);
      lstOrderTemp.sort((a, b) {
        var objA = a['created_date'] as DateTime;
        var objB = b['created_date'] as DateTime;
        return objA.compareTo(objB);
      });
    }
    if (lstOrderTemp.isNotEmpty) {
      lstOrderTemp.forEach((element) {
        OrderRethink? orderObj;
        if (flagEmqxEnable.value) {
          orderObj = OrderRethink.parserDataEmqx(element);
        } else {
          orderObj = OrderRethink.parserData(element);
        }
        var timeExpired =
            orderObj.created_date.add(Duration(minutes: 5));
        var timeCurrent = DateTime.now();
        if (timeCurrent.compareTo(timeExpired) == -1) {
          processOrderWithState(orderObj, getListFlow: true);
        }
      });
      // if (lstOrder.isNotEmpty) {
      //   var orderFist = lstOrder.first;
      //   orderIdQRProcessing = orderFist.order_code;
      //   if (orderFist.paymentMethod == PaymentMethod.Card) {
      //     processOrderWithState(orderFist);
      //   }
      // }
    }
  }

  Future<void> changeFeedListOrder() async {
    if (flagEmqxEnable.value) {
      _pushpaymentHomeController.emqxConnect.changeFeedPushPaymentOrder = (element) async {
        String printData = element['printData'] ?? "";
        if (printData.isNotEmpty) {
          NativeBridge().callNativePrintReceipt(printData);
          return;
        }
        var orderRethink = OrderRethink.parserDataEmqx(element);
        processOrderWithState(orderRethink);
      };
      _pushpaymentHomeController.emqxConnect.changeFeedConnection = (connectionElement) async {
        connectionState.value = connectionElement;
        if (connectionElement == EmqxClient.emqxReconnect){
          Logger().write(
              "EMQX_NEED_RENEW_CONNECT: ${posid.value} | DEVICE: ${AppConfiguration().deviceMpos.deviceSerial} | MUID: ${AppConfiguration().mobileUser}");
          if (AppConfiguration().deviceMpos.deviceSerial.isNotEmpty){
            posid.value = AppConfiguration().deviceMpos.deviceSerial;
            await emqxRenewConnection();
          }
          Logger().PushLog();
        }
      };
    } else {
      rethinkConnect.changesFeed(
          posid.value, _appConfiguration.rethinkConfig.merchantId);
      rethinkConnect.changeFeedListener = (element) async {
        var orderRethink = OrderRethink.parserData(element);
        processOrderWithState(orderRethink);
      };
    }
  }

  void processOrderWithState(OrderRethink orderRethink,
      {bool? getListFlow}) async {
    if (orderRethink == null) {
      return;
    }
    if (orderRethink.status == OrderStatus.Pending) {
      var result = pushActionBackground(orderRethink);
      if (result) {
        return;
      }
      var orderPushed = await updateOrderFromList(
          orderRethink, Configuration.Type_Action_Order_Add);
      if (orderPushed != null) {
        if ((orderPushed.pos_id == posid.value &&
            orderPushed.status == OrderStatus.Pending)) {
          if (orderPushed.paymentMethod == PaymentMethod.QR) {
            orderIdQRProcessing = orderPushed.order_code;
            requestGetQR(orderPushed);
          } else {
            var result = enableWidgetDynamicQR(orderPushed);
            if (result) {
              orderIdQRProcessing = orderPushed.order_code;
              requestGetQR(orderPushed);
            }
            if (isAutoPay &&
                orderPushed.paymentType != PaymentType.Installment &&
                getListFlow == null) {
              await requestUpdateUdidWithCardPayment(orderPushed);
            }
          }
        }
      }
    } else {
      if (orderRethink.status == OrderStatus.Rejected){
        resetOrderProcess(orderReset: orderRethink);
        updateOrderFromList(orderRethink, Configuration.Type_Action_Order_Remove);
      }
      if (orderRethink.status == OrderStatus.Approved) {
        int delayTemp = 10;
        await Future.delayed(Duration(seconds: delayTemp));
        updateOrderFromList(orderRethink, Configuration.Type_Action_Order_Remove);
      }
    }
  }

  void _startOrderProcessCardPayment(String orderId) {
    if (orderId.isNotEmpty) {
      orderIdProcessing = orderId;
      blockPaymentComing = true;
    }
  }

  bool pushActionBackground(OrderRethink orderRethink) {
    int amount = orderRethink.amount;
    bool state = amount == 1 ? true : false;
    bool result = false;
    String orderCode = orderRethink.order_code;
    if (orderCode.startsWith(PushAction.ACTION_RESET_PASSWORD)) {
      password = '';
      PPSettingModel().localPassword = '';
      PPSettingModel().supportLogoutConfirm = false;
      PPSettingModel().savePPSetting();
      Logger()
          .write("ACTION_RESET_PASSWORD_SUCCESS WITH POSID: ${posid.value}");
      result = true;
    }
    if (orderCode.startsWith(PushAction.ACTION_PING)) {
      Logger().write("ACTION_PING_ORDER_SUCCESS WITH POSID: ${posid.value}");
      result = true;
    }
    if (orderCode.startsWith(PushAction.ACTION_EMQX_DISCONNECT)) {
      Logger().write("ACTION_EMQX_DISCONNECT WITH POSID: ${posid.value}");
      _pushpaymentHomeController.emqxConnect.forceDisconnect();
      result = true;
    }
    if (orderCode.startsWith(PushAction.ACTION_SHOW_HISTORY_MODE)) {
      Logger()
          .write("ACTION_HISTORY_HIDDEN_SUCCESS WITH POSID: ${posid.value}");
      isShowHistory.value = state;
      result = true;
    }
    if (orderCode.startsWith(PushAction.ACTION_RELOAD_CONFIG)) {
      Logger().write("ACTION_RELOAD_CONFIG_SUCCESS WITH POSID: ${posid.value}");
      if (state == true){
        _requestReGetAllConfig().then((value) => (){
          instanceLocalFlag();
        });
      }
      result = true;
    }
    if (orderCode.startsWith(PushAction.ACTION_SHOW_ORDER_MODE)) {
      Logger()
          .write("ACTION_SHOW_ONLY_ORDER_SUCCESS WITH POSID: ${posid.value}");
      PPSettingModel().setSupportOnlyOrder = state;
      PPSettingModel().savePPSetting();
      result = true;
    }
    if (orderCode.startsWith(PushAction.ACTION_EMQX_ACTIVE)) {
      Logger().write("ACTION_EMQX_ACTIVE_SUCCESS WITH POSID: ${posid.value}");
      if (!flagEmqxEnable.value && state == true) {
        emqxRenewConnection();
      }
      result = true;
    }
    if (result) {
      Logger().PushLog();
      requestRemoveOrder(orderRethink, forceRemove: true);
    }
    return result;
  }

  void onPressHistoryTrans() {
    // if (kDebugMode){
    //   var nlResponse = NLVCBRequest.requestGetOrderList("01-12-2024", "11-12-2024", "QRCODE:", 1);
    //   return;
    // }
    Logger()
        .writeLog(Configuration.LOGGER_TYPE_ACTION, TAG + 'List Transaction');
    Get.toNamed(AppRoutes.HISTORY_PAGE)?.then((_) {
      brightnessResumeControl();
    });
    return;
  }

  Future<void> emqxRenewConnection() async {
    await Future.delayed(Duration(seconds: 5));
    _pushpaymentHomeController.requestGetAuth(posid.value).then((value) async {
      Logger().write("Re_getAuthInfo: $value");
      if (value != null && value.isNotEmpty) {
        var connected = await _pushpaymentHomeController.emqxConnection(value);
        connectionState.value = connected == true ? EmqxClient.emqxAlive : EmqxClient.emqxDisconnected;
        if (connected) {
          connectionState.value = EmqxClient.emqxAlive;
          if (!flagEmqxEnable.value) {
            rethinkConnect.connectionDispose();
          }
        }
      } else {
        connectionState.value = EmqxClient.emqxDisconnected;
      }
    });
  }

  Future<void> requestUpdateUdidWithCardPayment(OrderRethink orderRethink,
      {String? payType}) async {
    if (orderRethink.amount < AppConfiguration().paymentAmountMin){
      Logger().write(
          "ORDER_UNDER_AMOUNT: ${orderRethink.order_code}| ORDER_PROCESSING: $orderIdProcessing | AMOUNT_MIN: ${AppConfiguration().paymentAmountMin}");
      return;
    }
    if (blockPaymentComing || orderRethink.order_code == orderIdProcessing) {
      Logger().write(
          "BLOCK_PAY_ORDER: ${orderRethink.order_code}| ORDER_PROCESSING: $orderIdProcessing");
      Logger().PushLog();
      return;
    }
    debugPrint("REQUEST_UPDATE_UDID: ${orderRethink.order_code}");
    setEnableTouchFunction(false);
    _startOrderProcessCardPayment(orderRethink.order_code);
    Map paymentInfo = Map();
    paymentInfo['amount'] = orderRethink.amount;
    paymentInfo['posId'] = orderRethink.pos_id;
    paymentInfo['orderId'] = orderRethink.order_code;
    paymentInfo['rethinkId'] = orderRethink.id;
    paymentInfo['description'] = orderRethink.description;
    var udid = orderRethink.udidCard;
    if (payType == PaymentType.Installment && orderRethink.flagOrderPushPaymentEnable == false) {
      udid = PaymentType.Installment + '_' + orderRethink.udidCard;
    } else {
      orderRethink.status = OrderStatus.Processing;
      await updateOrderFromList(
          orderRethink, Configuration.Type_Action_Order_Update);
    }
    paymentInfo['udid'] = udid;
    var errorResponse;
    if (!orderRethink.flagOrderPushPaymentEnable){
      errorResponse =
      await requestUpdateUdidWithCard(orderRethink, udid);
    }else{
      //Hard code SUCCESS with UDID created by Server
      errorResponse = ErrorResponse(code: ErrorCode.DO_SERVICE_SUCCESS, message: "DO_SERVICE_SUCCESS");
    }
    var result = '';
    if (orderIdProcessing.isEmpty) {
      cardProcessing = false;
      return;
    }
    if (errorResponse.code == ErrorCode.DO_SERVICE_SUCCESS) {
      cardProcessing = true;
      orderRethink.udidCard = udid;
      updateOrderFromList(orderRethink, Configuration.Type_Action_Order_Update);
      if (orderRethink.depositId.isNotEmpty) {
        paymentInfo['depositID'] = orderRethink.depositId;
        paymentInfo['description'] = '';
        paymentInfo['trxType'] = TrxType.DepositPayment.index;
        String stringJson = jsonEncode(paymentInfo);
        result = await _nativeBridge.callNativePayDeposit(stringJson);
      } else {
        if (payType == PaymentType.Installment) {
          payOrderInstallment(orderRethink);
          return;
        }
        String jsonString = jsonEncode(paymentInfo);
        result = await _nativeBridge.callNativePayPushPayment(jsonString);
      }
      cardProcessing = false;
    }
    print('result scan card: $result');
    Logger().write(TAG + "PAY_CALLBACK: $result");
    await processCallbackCardTransaction(result, orderRethink);
  }

  Future<ErrorResponse> requestUpdateUdidWithCard(
      OrderRethink orderRethink, String udid) async {
    showLoadingHud(isShow: true);
    await Future.delayed(Duration(seconds: timeDelay));
    Logger().write(TAG +
        "REQUEST_UPDATE_UDID:[$udid]| OrderCode:[${orderRethink.order_code}]");
    String jsonObject = await MposRequest.updateUdid(
        orderRethink.mpos_key_id,
        orderRethink.order_code,
        orderRethink.pos_id,
        _appConfiguration.rethinkConfig.merchantId,
        orderRethink.amount,
        udid);
    showLoadingHud(isShow: false);
    ErrorResponse errorResponse = ErrorResponse();
    try {
      Map<String, dynamic>? response = jsonDecode(jsonObject);
      errorResponse = ErrorResponse.parserData(response);
    } catch (exception) {
      Logger().write(
          TAG + "REQUEST_UPDATE_UDID_EXCEPTION:${exception.toString()}]");
    }
    return errorResponse;
  }

  Future<ErrorResponse> requestUpdateUdidWithQRCode(
      OrderRethink orderRethink, String udid) async {
    await Future.delayed(Duration(seconds: timeDelay));
    String jsonObject = await MposRequest.updateUdid(
        orderRethink.mpos_key_id,
        orderRethink.order_code,
        orderRethink.pos_id,
        _appConfiguration.rethinkConfig.merchantId,
        orderRethink.amount,
        udid);
    ErrorResponse errorResponse = ErrorResponse();
    if (jsonObject.isNotEmpty) {
      try {
        Map<String, dynamic>? response = jsonDecode(jsonObject);
        errorResponse = ErrorResponse.parserData(response);
      } catch (exception) {
        Logger().write(
            TAG + "REQUEST_UPDATE_UDID_QR_EXCEPTION:${exception.toString()}]");
      }
    }
    return errorResponse;
  }

  Future<void> requestUpdateUdidSuccess(
      String paymentIdentifier,
      String transactionId,
      String issuerCode,
      String maskPan,
      OrderRethink orderRethink,
      String authCode,
      String rrn) async {
    if (!orderRethink.flagOrderPushPaymentEnable){
      Map<String, dynamic> dataObject = Map();
      dataObject['mposKeyId'] = orderRethink.mpos_key_id;
      dataObject['orderId'] = orderRethink.order_code;
      dataObject['posId'] = orderRethink.pos_id;
      dataObject['merchantId'] = _appConfiguration.rethinkConfig.merchantId;
      dataObject['amount'] = orderRethink.amount;
      dataObject['udid'] = paymentIdentifier;
      dataObject['txid'] = transactionId;
      dataObject['muid'] = _appConfiguration.rethinkConfig.muid;
      dataObject['issuerCode'] = issuerCode;
      dataObject['pan'] = maskPan;
      if (authCode.isNotEmpty) {
        dataObject['authCode'] = authCode;
      }
      if (rrn.isNotEmpty) {
        dataObject['rrn'] = rrn;
      }
      Logger().write(TAG +
          "UpdateSuccess: ID:[${orderRethink.mpos_key_id}] OrderCode:[${orderRethink.order_code}] | Txid: $transactionId");
      var response = await MposRequest.updateOrderSuccess(dataObject);
      orderRethink.status = OrderStatus.PaidFail;
      if (response.isNotEmpty) {
        var mapObj = jsonDecode(response);
        var errorCode = ErrorResponse.parserData(mapObj);
        if (errorCode.code == ErrorCode.DO_SERVICE_SUCCESS ||
            errorCode.code == ErrorCode.ORDER_ALREADY_PAID) {
          orderRethink.status = OrderStatus.Approved;
        }
      }
      if (orderRethink.status != OrderStatus.Approved &&
          transactionId.isNotEmpty) {
        lstOrderNotSync.add(dataObject);
      }
      startSyncTrans();
    }else{
      orderRethink.status = OrderStatus.Approved;
    }
    await updateOrderFromList(
        orderRethink, Configuration.Type_Action_Order_Update);
    int delayTemp = timeDelay;
    if (orderRethink.status == OrderStatus.Approved) {
      delayTemp = 15;
    }
    await Future.delayed(Duration(seconds: delayTemp));
    updateOrderFromList(orderRethink, Configuration.Type_Action_Order_Remove);
  }

  void syncTransApprovedProcess() async {
    if (lstOrderNotSync.isNotEmpty) {
      var mapPush = lstOrderNotSync.first;
      Logger().write("begin sync trans with Order: ${mapPush["orderId"]}");
      var response = await MposRequest.updateOrderSuccess(mapPush);
      if (response.isNotEmpty) {
        lstOrderNotSync.remove(mapPush);
      }
    } else {
      stopSyncTrans();
    }
  }

  Future<void> processCallbackCardTransaction(
      String callback, OrderRethink orderRethink) async {
    if (password.isEmpty){
      setEnableTouchFunction(true);
    }
    String status = "";
    if (callback.isNotEmpty) {
      Map result = jsonDecode(callback);
      status = result['result']['status'] ?? "";
      String wfInfo = '';
      if (status == PaymentStatusValue.Approved) {
        lstTransApproved.add(callback);
        String paymentIdentifier = '';
        String transactionId = '';
        String issuerCode = '';
        String maskPan = '';
        String authCode = '';
        String rrn = '';
        try {
          paymentIdentifier = result['result']['paymentIdentifier'] ?? '';
          transactionId = result['result']['transactionId'] ?? '';
          issuerCode = result['userCard']['applicationLabel'] ?? '';
          maskPan = result['userCard']['pan'] ?? '';
          authCode = result['userCard']['authCode'] ?? "";
          rrn = result['userCard']['rrn'] ?? "";
          wfInfo = jsonEncode(result['wfInfo'] ?? "");
        } catch (exception) {
          Logger()
              .write("CALLBACK_TRANSACTION_EXCEPTION:${exception.toString()}");
        }
        if (paymentIdentifier == orderRethink.udidCard) {
          orderRethink.status = OrderStatus.Processing;
          await updateOrderFromList(
              orderRethink, Configuration.Type_Action_Order_Update);
          requestUpdateUdidSuccess(paymentIdentifier, transactionId, issuerCode,
              maskPan, orderRethink, authCode, rrn);
          printReceiptWithDataPay(wfInfo);
        } else {
          var orderTemp = getOrderWithUdid(paymentIdentifier);
          if (orderTemp != null) {
            requestUpdateUdidSuccess(paymentIdentifier, transactionId,
                issuerCode, maskPan, orderTemp, authCode, rrn);
            printReceiptWithDataPay(wfInfo);
          }else{
            // requestRemoveOrder(orderRethink);
            orderRethink.status = OrderStatus.Approved;
            updateOrderFromList(
                orderRethink, Configuration.Type_Action_Order_Update);
            int delayTemp = timeDelay;
            if (orderRethink.status == OrderStatus.Approved) {
              delayTemp = 15;
            }
            await Future.delayed(Duration(seconds: delayTemp));
            updateOrderFromList(orderRethink, Configuration.Type_Action_Order_Remove);
          }
        }
        resetOrderProcess();
        return;
      }else{
        if (status == "CANCEL"){
          orderRethink.status = OrderStatus.Pending;
          await updateOrderFromList(
              orderRethink, Configuration.Type_Action_Order_Update);
          resetOrderProcess();
          return;
        }
      }
    }
    Logger().write(TAG + "PAY_FAIL_ORDER: ${orderRethink.order_code}");
    requestRemoveOrder(orderRethink, forceRemove: status == "TRANS_ERROR" ? true : false);
    orderRethink.status =
        LocalizationCustom.localization(OrderStatus.UpdateFail);
    await updateOrderFromList(
        orderRethink, Configuration.Type_Action_Order_Update);
    await Future.delayed(Duration(seconds: 5));
    updateOrderFromList(orderRethink, Configuration.Type_Action_Order_Remove);
    resetOrderProcess();
  }

  void printReceiptWithDataPay(String data, {bool? isPayQR}) async {
    if (supportPrintReceipt) {
      try {
        if (data.isNotEmpty && !_appConfiguration.isKozenP12) {
          if (isPayQR == true) {
            requestGetViewTrans(data);
          } else {
            if (_appConfiguration.appType == AppType.MPOS_LITE) {
              NativeBridge().callNativePrintOfflineWithData(data);
            }
          }
        }
      } catch (e) {
        Logger().write('print_err: ${e.toString()}');
      }
    }
  }

  Future<void> resetOrderProcess({OrderRethink? orderReset}) async {
    blockPaymentComing = false;
    var orderCodeReject = "";
    if (orderReset != null){
      orderCodeReject = orderReset.order_code;
    }
    if (cardProcessing && orderCodeReject == orderIdProcessing) {
      NativeBridge().callNativeCancelPayment();
    }
    orderIdProcessing = "";
    Logger().PushLog();
  }

  void onBack() {
    NativeBridge().setDisplayNavbar(true);
    Get.back();
  }

  void logoutCashier(BuildContext context) async {
    if (_pushpaymentHomeController.ppSetting.value.supportLogoutConfirm) {
      showMPBottomSheet(
        context,
        title: LocalizationCustom.localization("Xác nhận mật khẩu"),
        body: PassCodePage(
          localPassword: password,
          confirmLogoutEnable: true,
          onPressConfirm: (value) async {
            if (value != password) {
              _showDialogErrorConfirm(context);
              return;
            }
            var clearSuccess = await logoutPushPayment();
            if (clearSuccess) {
              onBack();
            }
          },
          onCallbackAutoClose: (value) {
            Navigator.pop(context);
          },
        ),
        delay: isAutoPay == true ? delayCloseDialog : null,
        // delay: null
      );
    } else {
      if (password.isNotEmpty) {
        showMPBottomSheet(
          context,
          title: LocalizationCustom.localization("Xác nhận mật khẩu"),
          body: WidgetConfirmPassword(
            onPressConfirm: (value) async {
              if (value != password) {
                Logger().writeLog(Configuration.LOGGER_TYPE_ACTION,
                    TAG + 'Logout wrong password');
                _showDialogErrorConfirm(context);
                return;
              }
              var clearSuccess = await logoutPushPayment();
              if (clearSuccess) {
                onBack();
              }
            },
            onCallbackAutoClose: (value) {
              if (value) {
                Navigator.pop(context);
              }
            },
          ),
          delay: isAutoPay == true ? delayCloseDialog : null,
        );
      } else {
        if (lstOrder.isNotEmpty){
          AppUtils.showDialogAlert(context,
            title: LocalizationCustom.localization("Warning"),
            description: LocalizationCustom.localization("You still have outstanding invoices. Are you sure you want to exit this session?"),
            text1stButton: LocalizationCustom.localization("Close"),
            text2ndButton: LocalizationCustom.localization("logout"),
              isTwoButton: true,
            delay: 10,
            onPress2ndButton: () async {
              Logger().write("LOGOUT_CASHIER WHEN EXISTED ORDER: ${lstOrder.length}");
              var clearSuccess = await logoutPushPayment();
              if (clearSuccess) {
                onBack();
              }
            }
          );
          return;
        }
        var clearSuccess = await logoutPushPayment();
        if (clearSuccess) {
          onBack();
        }
      }
    }
  }

  void _showDialogErrorConfirm(BuildContext context) {
    AppUtils.showDialogWithMessage(context,
        LocalizationCustom.localization('Không đúng mật khẩu đã đăng nhập.'),
        delay: isAutoPay == true ? delayCloseDialog : null);
  }

  void onPressOrderWithIndex(OrderRethink orderRethink,
      {String? payType}) async {
    Logger().writeLog(
        Configuration.LOGGER_TYPE_ACTION,
        TAG +
            'ACTION_PAY_ORDER:${orderRethink.order_code}| Amount: ${orderRethink.amount}| Status:${orderRethink.status} | FLAG_BLOCK: ${blockPaymentComing == true ? "TRUE" : "FALSE"}');
    bool isPaied = showWarningOrderApproved(orderRethink.order_code);
    if (isPaied) {
      return;
    }
    if (lstOrder.length > 1) {
      String _titleConfirmOrder =
          LocalizationCustom.localization('Thanh toán đơn: ');
      String _titleConfirmAmount = LocalizationCustom.localization('Số tiền: ');
      Logger().writeLog(
          Configuration.LOGGER_TYPE_ACTION,
          TAG +
              'OrderRethink Selected: ${orderRethink.order_code} Amount: ${orderRethink.amount} Status:${orderRethink.status}');
      AppUtils.showDialogAlert(
        context,
        isTwoButton: true,
        title: LocalizationCustom.localization('Xác nhận thanh toán hoá đơn'),
        descriptionTextAlign: TextAlign.left,
        description:
            "$_titleConfirmOrder ${orderRethink.order_code}\n$_titleConfirmAmount ${TextUtils.formatAmount(orderRethink.amount.toDouble())} đ",
        text1stButton: LocalizationCustom.localization("Không"),
        text2ndButton: LocalizationCustom.localization('Đồng ý'),
        onPress2ndButton: () {
          requestUpdateUdidWithCardPayment(orderRethink, payType: payType);
        },
        delay: isAutoPay == true ? delayCloseDialog : null,
        // delay: null,
      );
    } else {
      requestUpdateUdidWithCardPayment(orderRethink, payType: payType);
    }
  }

  Future<void> payOrderInstallment(OrderRethink orderRethink) async {
    double amount = double.parse(orderRethink.amount.toString());
    Map argumentsInput = {
      'title': LocalizationCustom.localization("Kiểm tra thẻ trả góp"),
      'showPayLink': false,
      'amount': amount,
      'allowOtherPaymentMethod': false,
      'udidInstallmentPushPayment': orderRethink.udidCard,
      'onCallbackPushPayment': (onCallbackPushPayment) {
        resetOrderProcess();
      },
      'onSwipeSuccess': (resultCard) {
        Logger().write(TAG + "ONSWIPECARD_RESULT: $resultCard");
        if (resultCard['data'] != null) {
          doFunctionInstallmentPublic(
            context: context,
            mpDataLoginModel: AppConfiguration().mpDataLoginModel!,
            amount: amount,
            currentPhone: '',
            currentEmail: '',
            currentDescription: orderRethink.description,
            isPayLink: false,
            dataInput: resultCard['data'],
            processHandleResultPay: (String data, Map dataInstallment) {
              Logger().write(TAG + "PAY_CALLBACK: $data");
              Logger().write(TAG +
                  "INSTALMENT_DATA: ${dataInstallment != null ? dataInstallment.toString() : ''}");
              if (data.isNotEmpty) {
                processCallbackCardTransaction(data, orderRethink);
              } else {
                resetOrderProcess();
              }
            },
            onPressFeatureInstallmentCard: () {
              print("Pushpayment_FeatureInstallmentCard");
              resetOrderProcess();
            },
            allowOtherPaymentMethod: false,
            callbackOtherPaymentMethod: (featureName) {
              print("Pushpayment_other_method");
              resetOrderProcess();
            },
          );
        } else {
          resetOrderProcess();
        }
      },
    };
    var result = await Get.to(
        SwipeCardScreen(
          marginHeader: 0,
        ),
        binding: SwipeCardBinding(),
        arguments: argumentsInput,
        opaque: false);
    print('Result Swipe Card Controller: $result');
    resetOrderProcess();
  }

  Widget genDefaultMessageWhenListEmpty() {
    if (flagEmqxEnable.value) {
      return Text(
        LocalizationCustom.localization("Waiting new order..."),
        style: style_S16_W600_BlackColor,
      );
    }
    return Column(
      children: [
        SelectableText.rich(
          TextSpan(
              text: LocalizationCustom.localization("Vui lòng ấn"),
              style: style_S16_W400_BlackColor,
              children: [
                TextSpan(
                    text: " " + LocalizationCustom.localization("Làm mới") + " ",
                    style: style_S16_W600_BlueColor),
                TextSpan(
                    text: LocalizationCustom.localization(
                        "khi gửi đơn không thành công."),
                    style: style_S16_W400_BlackColor)
              ]),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 20,),
        MPButton(
          onPressed: (){
            refreshConnection();
          },
          title: LocalizationCustom.localization('Làm mới'),
          titleStyle: style_S16_W600_WhiteColor,
          height: 50,
        )
        // Container(
        //   height: 50,
        //     width: 50,
        //     decoration: BoxDecoration(
        //       color: Configuration.blue2MainColor,
        //       borderRadius: BorderRadius.all(Radius.circular(25))
        //     ),
        //     child: IconButton(
        //         onPressed: () {
        //           refreshConnection();
        //         },
        //         icon: Icon(
        //           Icons.refresh,
        //           size: 25,
        //           color: Configuration.whiteColor,
        //         ))),
        // Text(' '+LocalizationCustom.localization('Làm mới')+' ', style: style_S16_W600_BlueColor,)
      ],
    );
  }

  void showLoadingHud({required bool isShow, String? title}) {
    if (isShow) {
      _appController.showAppLoading();
    } else {
      _appController.hideAppLoading();
    }
  }

  Future<bool> logoutPushPayment() async {
    if (flagEmqxEnable.value) {
      try {
        _pushpaymentHomeController.emqxConnect.forceDisconnect();
      } catch (exception) {
        print("clear storage exception: $exception");
      }
      return true;
    } else {
      try {
        rethinkConnect.connectionDispose();
        rethinkConnect.stopRunTimeHeathCheck();
      } catch (exception) {
        print("clear storage exception: $exception");
        return false;
      }
      return true;
    }
  }

  OrderRethink? findOrderWithUdid(String udid) {
    print("udid receipt notification: $udid");
    if (udid.isNotEmpty) {
      for (int index = 0; index < lstOrder.length; index++) {
        var orderObj = lstOrder[index];
        print("udid from list order: ${orderObj.udidQR}");
        if (orderObj.udidQR == udid) {
          return orderObj;
        }
      }
    }
    return null;
  }

  OrderRethink? findOrderWithIdRethinbk(String id) {
    if (id.isNotEmpty) {
      for (int index = 0; index < lstOrder.length; index++) {
        var orderObj = lstOrder[index];
        if (orderObj.id == id) {
          return orderObj;
        }
      }
    }
    return null;
  }

  Future<OrderRethink?> updateOrderFromList(
      OrderRethink? orderRethink, String typeActionOrder) async {
    if (typeActionOrder != Configuration.Type_Action_Order_Refresh) {
      if (orderRethink == null) {
        Logger()
            .write(TAG + 'Error update list with - rethink connection NULL');
        Logger().PushLog();
        return null;
      }
      if (typeActionOrder == Configuration.Type_Action_Order_Add) {
        brightnessDisableControl();
        if (!orderRethink.flagOrderPushPaymentEnable) {
          final udid = genUDIDPayment(orderRethink: orderRethink);
          orderRethink.udidQR = udid + "_[QR]";
          orderRethink.udidCard = udid + "_[CARD]";
        }
        if (lstOrder.isNotEmpty) {
          var existed = lstOrder.firstWhereOrNull(
                  (element) => element.order_code == orderRethink.order_code);
          if (existed == null) {
            Logger().write(
                "ADD_ORDER_NEW: ${orderRethink
                    .order_code}| AMOUNT:${orderRethink
                    .amount}| STATUS:${orderRethink
                    .status}| POSID:${orderRethink
                    .pos_id} | MODIFY_DATE: ${orderRethink.modifiedDate}");
            if (isOnlyShowOrder) {
              lstOrder[0] = orderRethink;
            } else {
              lstOrder.insert(0, orderRethink);
            }
            lstOrder.refresh();
            return orderRethink;
          } else {
            int index = lstOrder.indexOf(existed);
            bool updated = false;
            if (!orderRethink.flagOrderPushPaymentEnable) {
              if (orderRethink.amount != existed.amount) {
                orderRethink.udidQR =
                    genUDIDPayment(orderRethink: orderRethink) + "_[QR]";
                Logger().write(
                    "ORDER_ADD_EXISTED: ${orderRethink
                        .order_code}| AMOUNT:${orderRethink
                        .amount}| STATUS:${orderRethink
                        .status}| POSID:${orderRethink.pos_id}");
                lstOrder[index] = orderRethink;
                updated = true;
              }
            } else if (orderRethink.qrCodePushPayment.isNotEmpty) {
              Logger().write(
                  "ORDER_ADD_UPDATE: ${orderRethink
                      .order_code}| AMOUNT:${orderRethink
                      .amount}| STATUS:${orderRethink
                      .status}| POSID:${orderRethink.pos_id}");
              print("QRCONTENT_UPDATE: ${orderRethink.qrCodePushPayment}");
              lstOrder[index] = orderRethink;
              updated = true;
            }else{
              Logger().write(
                  "ORDER_ADD_EXITED: ${existed.order_code}| AMOUNT:${existed
                      .amount}| STATUS:${existed.status}| POSID:${existed
                      .pos_id}");
            }
            if (updated) {
              lstOrder.refresh();
              return orderRethink;
            }
            lstOrder.refresh();
          }
        } else {
          Logger().write(
              "ORDER_ADD_NEW: ${orderRethink.order_code}| AMOUNT:${orderRethink.amount}| STATUS:${orderRethink.status}| POSID:${orderRethink.pos_id}");
          print("QRCONTENT_NEW: ${orderRethink.qrCodePushPayment}");
          lstOrder.add(orderRethink);
          lstOrder.refresh();
          return orderRethink;
        }
      } else if (typeActionOrder == Configuration.Type_Action_Order_Remove) {
        if (lstOrder.isNotEmpty) {
          Logger().write(
              "ORDER_REMOVE: ${orderRethink.order_code}| AMOUNT:${orderRethink.amount}| STATUS:${orderRethink.status}| POSID:${orderRethink.pos_id}");
          lstOrder.removeWhere(
              (element) => element.order_code == orderRethink.order_code);
          finishOrderQRProcessing(orderRethink.order_code);
        }
        if (lstOrder.isEmpty) {
          brightnessResumeControl();
        }
        lstOrder.refresh();
      } else if (typeActionOrder == Configuration.Type_Action_Order_Update) {
        var existed = lstOrder.firstWhereOrNull(
            (element) => element.order_code == orderRethink.order_code);
        if (existed != null) {
          Logger().write(
              "ORDER_UPDATE: ${orderRethink.order_code}| AMOUNT:${orderRethink.amount}| STATUS:${orderRethink.status}| POSID:${orderRethink.pos_id}");
          debugPrint("qrCodePushPayment_Update: ${orderRethink.qrCodePushPayment}");
          int index = lstOrder.indexOf(existed);
          lstOrder[index] = orderRethink;
          lstOrder.refresh();
        }
      }
    }
    return null;
  }

  String genUDIDPayment({required OrderRethink orderRethink}) {
    var uuid = Uuid();
    DateTime now = DateTime.now();
    String timeLog = DateFormat('yyyyMMddHHmmss').format(now);
    String udid = timeLog;
    if (udid.isEmpty) {
      try {
        udid = uuid.v1();
      } on PlatformException catch (e) {
        udid = timeLog;
        Logger().write(TAG + 'Gen UDID exception: ${e.message}');
      }
    }
    var udidPayment = "PUSHPAYMENT" +
        "[${_appConfiguration.rethinkConfig.muid},${orderRethink.order_code},${orderRethink.pos_id},${orderRethink.mpos_key_id}]" +
        udid;
    if (kDebugMode) {
      getOrderWithUdid(udidPayment);
    }
    return udidPayment;
  }

  OrderRethink? getOrderWithUdid(String udid) {
    if (udid.contains("PUSHPAYMENT")) {
      var orderRethink = OrderRethink();
      int from = udid.indexOf("[") + 1;
      int end = udid.indexOf("]");
      var temp = udid.substring(from, end);
      var lstTemp = temp.split(",");
      try {
        orderRethink.udidCard = udid;
        String orderIdTemp = "";
        if (lstTemp.length >= 4) {
          orderRethink.id = lstTemp[lstTemp.length - 1];
          orderRethink.pos_id = lstTemp[lstTemp.length - 2];
          for (int index = 1; index < lstTemp.length - 2; index++) {
            orderIdTemp = orderIdTemp + "," + lstTemp[index];
          }
          orderIdTemp = TextUtils.removeCharWithPositon(0, 1, orderIdTemp);
          orderRethink.order_code = orderIdTemp;
        }
      } catch (e) {}
      return orderRethink;
    }
    return null;
  }

  Future<void> requestGetQR(OrderRethink orderRethink) async {
    if (orderRethink.flagOrderPushPaymentEnable == true) {
      return;
    }
    if (orderRethink.depositId.isNotEmpty) {
      print("REQUEST_GET_QR REJECT by AUTOPAY OR DEPOSIT TYPE");
      return;
    }
    if (orderRethink.paymentMethod == PaymentMethod.Card) {
      return;
    }
    if (orderRethink.qrDataContent.qrData.isNotEmpty) {
      Logger().write(TAG +
          "REQUEST_GET_QR Udid_Existed withOrderId: [${orderRethink.order_code}] UDID:[${orderRethink.udidQR}]");
      return;
    }
    if (flagQrNL.value) {
      var response = await NLVCBRequest.requestCreateOrder(
          orderRethink.order_code,
          orderRethink.description,
          orderRethink.amount);
      print("response NL: $response");
      String message = "";
      String urlCheckOut = "";
      String tokenCode = "";
      String qrData = "";
      if (response.isNotEmpty) {
        var mapJson = jsonDecode(response);
        String resultCode = mapJson["result_code"] ?? "";
        message = mapJson["result_message"] ?? "";
        if (resultCode == "0000") {
          message = "";
          urlCheckOut = mapJson["result_data"]["checkout_url"] ?? "";
          tokenCode = mapJson["result_data"]["token_code"] ?? "";
          String data = mapJson["result_data"]["data"] ?? "";
          if (data.isNotEmpty){
            qrData = data;
          }else{
            var qrResponse = await NLVCBRequest.requestGetQrCode(urlCheckOut);
            if (qrResponse.isNotEmpty) {
              var mapQRData = jsonDecode(qrResponse);
              qrData = mapQRData["result_data"]["data_qr"] ?? "";
            }
          }
        } else {
          message = LocalizationCustom.localization("GET_QR_CODE_FAIL");
        }
        orderRethink.udidQR = tokenCode;
      }
      var qrDataContent = QRDataContent(
          qrData: qrData, finish: true, message: message, token: tokenCode);
      orderRethink.setQrDataContent = qrDataContent;
      updateOrderFromList(orderRethink, Configuration.Type_Action_Order_Update);
    } else {
      if (AppConfiguration().mobileUser.isNotEmpty &&
          isQRActive.value == true) {
        if (orderRethink.udidQR.isNotEmpty) {
          Logger().write(TAG +
              "GET_QR_CODE: [${orderRethink.order_code}] UDID:[${orderRethink.udidQR}]");
          orderRethink.qrStatus = null;
          ErrorResponse errorResponse = await requestUpdateUdidWithQRCode(
              orderRethink, orderRethink.udidQR);
          var qrData = '';
          var message = LocalizationCustom.localization("GET_QR_CODE_FAIL");
          if (errorResponse.code == ErrorCode.DO_SERVICE_SUCCESS) {
            var response = await MposRequest.getDynamicVAQR(
                orderRethink.udidQR,
                _appConfiguration.rethinkConfig.muid,
                _appConfiguration.rethinkConfig.merchantId,
                orderRethink.order_code,
                orderRethink.amount);
            if (response.isNotEmpty) {
              var mapObj = jsonDecode(response);
              var errorCode = mapObj['error']['code'] ?? 0;
              if (errorCode == 8003) {
                orderRethink.udidQR =
                    genUDIDPayment(orderRethink: orderRethink) + "_[QR]";
                Logger().write(
                    "UPDATE_UDID_ORDER: ${orderRethink.order_code}| AMOUNT:${orderRethink.amount}| STATUS:${orderRethink.status}| POSID:${orderRethink.pos_id} | UDID: ${orderRethink.udidQR}");
              }
              var qrObject = QRDynamicResponse.fromJson(mapObj);
              if (qrObject.qrCode != null) {
                message = '';
                qrData = qrObject.qrCode ?? '';
              }
            }
          }
          orderRethink.setQrDataContent = QRDataContent(
              qrData: qrData,
              finish: true,
              message: message,
              token: orderRethink.udidQR);
          updateOrderFromList(
              orderRethink, Configuration.Type_Action_Order_Update);
        }
      } else {
        print(
            "REQUEST_GET_QR FAIL With UserMobile: ${AppConfiguration().mobileUser} QR_ACTIVE: ${isQRActive.value}");
      }
    }
  }

  Future<void> requestRemoveOrder(OrderRethink orderRethink, {bool? forceRemove}) async {
    if (flagQrNL.value == false || forceRemove == true) {
      await MposRequest.removeOrderPushPayment(
          _appConfiguration.rethinkConfig.merchantId,
          orderRethink.order_code,
          posid.value,
          orderRethink.amount);
      Logger().PushLog();
    }
  }

  Future<void> requestCheckStatus(OrderRethink orderRethink) async {
    if (orderRethink.status == OrderStatus.Approved) {
      return;
    }
    if (orderRethink.flagOrderPushPaymentEnable == true && orderRethink.qrCodePushPayment.isNotEmpty) {
      var response = await MposRequest.reqGetTransactionStatus(
          AppConfiguration().merchantId, posid.value, orderRethink.order_code);
      if (response.isEmpty){
        return;
      }
      debugPrint("GET_TRANSACTION_STATUS: $response");
      var mapResponse = jsonDecode(response);
      var status = mapResponse['transStatus'] ?? 0;
      var flagQR = mapResponse["paymentMethod"] == "QR" ? true : false;
      if (status == PaymentStatusCode.orderRejected){
        await updateOrderFromList(
            orderRethink, Configuration.Type_Action_Order_Remove);
        if (cardProcessing && orderIdProcessing == orderRethink.order_code){
          resetOrderProcess();
          return;
        }
      }
      if (status == PaymentStatusCode.Settle_code ||
          status == PaymentStatusCode.Approved_code) {
        orderRethink.status = OrderStatus.Approved;
        await updateOrderFromList(
            orderRethink, Configuration.Type_Action_Order_Update);
        if (flagQR){
          try{
            String transDate = DateFormat('dd/MM/yyyy HH:mm').format(
                DateTime.fromMillisecondsSinceEpoch(mapResponse["transDate"]));
            String transId = mapResponse["transCode"];
            String refNo = mapResponse["rrn"];
            String appCode = mapResponse["authCode"];
            String type = mapResponse["issuerCode"];
            String desc = orderRethink.description;
            String amount = mapResponse["amount"].toString();
            printQRReceiptWidget(transDate, transId, refNo, appCode, type, desc, amount);
          } catch (e){
            print("Exception PrintReceipt: ${e.toString()}");
          }
        }
        int delayTemp = timeDelay;
        if (orderRethink.status == OrderStatus.Approved) {
          Logger().write("RESPONSE_GET_TRANSACTION_STATUS: $response");
          delayTemp = 15;
        }
        await Future.delayed(Duration(seconds: delayTemp));
        updateOrderFromList(
            orderRethink, Configuration.Type_Action_Order_Remove);
      }
      return;
    }
    if (orderRethink.qrDataContent.qrData.isEmpty ||
        orderRethink.depositId.isNotEmpty) {
      return;
    }
    if (flagQrNL.value) {
      var responseCheckOrder = await NLVCBRequest.requestCheckOrder(
          orderRethink.qrDataContent.token,
          orderRethink.description,
          orderRethink.amount);
      if (responseCheckOrder.isNotEmpty) {
        var mapJson = jsonDecode(responseCheckOrder);
        var resultData = mapJson["result_data"];
        var status = resultData["status"];
        var tokenCode = resultData["token_code"] ?? "";
        var authCode = resultData["authCode"] ?? "";
        var refNo = resultData["ref_no"] ?? "";
        if (status == QRStatus.qrStatusPaid) {
          orderRethink.status = OrderStatus.Approved;
          await updateOrderFromList(
              orderRethink, Configuration.Type_Action_Order_Update);
          Future.delayed(Duration(seconds: timeDelay));
          requestUpdateUdidSuccess(orderRethink.udidQR, tokenCode, QRTYPE_NL,
              "", orderRethink, authCode, refNo);
          // updateOrderFromList(
          //     orderRethink, Configuration.Type_Action_Order_Remove);
        }
      }
    } else {
      if (orderRethink.udidQR.isNotEmpty) {
        var response = await MposRequest.getQRStatus(orderRethink.udidQR);
        Logger().write("CheckQRStatus with UDID: ${orderRethink.udidQR}");
        String authCode = "";
        String rrn = "";
        String txid = '';
        if (response.isNotEmpty) {
          var mapObj = jsonDecode(response);
          var qrStatus = QrDynamicStatusResponse.fromJson(mapObj);
          orderRethink.qrStatus = qrStatus;
          txid = qrStatus.txid ?? '';
          if (qrStatus.transactionStatus == PaymentStatusValue.Approved) {
            if (qrStatus.orderCode.isNotEmpty && txid.isEmpty) {
              txid = 'MPQR_' + qrStatus.orderCode;
            }
            if (txid.isNotEmpty) {
              requestUpdateUdidSuccess(orderRethink.udidQR, txid, QRTYPE_VAQR,
                  qrStatus.pan, orderRethink, authCode, rrn);
              printReceiptWithDataPay(txid, isPayQR: true);
            }
          }
        }
      }
    }
  }

  void finishOrderQRProcessing(String orderCode, {String? orderSelected}) {
    if (orderCode == orderIdQRProcessing) {
      orderIdQRProcessing = "";
    }
    if (orderSelected != null) {
      orderIdQRProcessing = orderSelected;
    } else {
      if (orderIdQRProcessing.isEmpty && lstOrder.isNotEmpty) {
        orderIdQRProcessing = lstOrder.first.order_code;
      }
    }
    if (lstOrder.isEmpty) {
      orderIdQRProcessing = '';
    }
  }

  Future<String> requestGetTransFromAllTrans(String udid) async {
    String txid = "";
    var responseAllTrans = await MposRequest.requestGetAllTrans(
        _appConfiguration.rethinkConfig.muid,
        _appConfiguration.rethinkConfig.merchantId.toString(),
        _appConfiguration.emailMerchant);
    if (responseAllTrans.isNotEmpty) {
      var mapObj = jsonDecode(responseAllTrans);
      print("data all trans response: $mapObj");
      try {
        var dataList = mapObj["data"];
        var listAllTrans = List<HistoryCardMposTrans>.from(dataList.map((x) {
          var hisTrans = HistoryCardMposTrans.fromJson(x);
          return hisTrans;
        }));
        for (int index = 0; index < listAllTrans.length; index++) {
          var objTrans = listAllTrans[index];
          if (objTrans.udid == udid) {
            txid = objTrans.txid ?? "";
            break;
          }
        }
        print("result get history: $dataList");
      } catch (exception) {}
    }
    return txid;
  }

  Future<void> requestGetViewTrans(String txid, {bool tryAgain = false}) async {
    if (txid.isNotEmpty) {
      if (flagQrNL.value == false) {
        var resViewTrans = await MposRequest.requestTransactionView(
            _appConfiguration.rethinkConfig.muid, txid);
        if (resViewTrans.isNotEmpty) {
          print("data trans response: $resViewTrans");
          Map mapObj = jsonDecode(resViewTrans);
          Map newMapObj = Map.from(mapObj);
          newMapObj["printTid"] = AppConfiguration().tId;
          newMapObj["printMid"] = AppConfiguration().mId;
          newMapObj["printBusinessName"] = AppConfiguration().businessName;
          newMapObj["printBusinessAddress"] =
              AppConfiguration().businessAddress;
          var trxIdTemp = mapObj["txid"] ?? "";
          var jsonString = "";
          try {
            jsonString = jsonEncode(newMapObj);
          } catch (exception) {
            Logger().write(
                "Exception requestGetViewTrans: ${exception.toString()}");
          }
          if (trxIdTemp.isNotEmpty) {
            NativeBridge().callNativePrintQrReceipt(jsonString);
          }
        }
      }
    }
  }

  Future<void> _requestStaticQR() async {
    if (flagQrNL.value) {
      return;
    }
    if (isQRActive.value &&
        _appConfiguration.rethinkConfig.muid.isNotEmpty &&
        qrStatic.value.qrCode.isEmpty) {
      var response = await MposRequest.requestGetStaticQR(
          _appConfiguration.rethinkConfig.muid,
          _appConfiguration.rethinkConfig.merchantId);
      if (response.isNotEmpty) {
        qrStatic.value = QrStaticResponse.fromJson(jsonDecode(response));
      }
      lstOrder.refresh();
    }
  }

  void startSyncTrans() {
    if (lstOrderNotSync.isEmpty) {
      return;
    }
    Logger().write(
        TAG + " start sync trans - trans not sync: ${lstOrderNotSync.length}");
    timerSyncTransApproved =
        Timer.periodic(Duration(seconds: timeSync), (timer) {
      syncTransApprovedProcess();
    });
  }

  void stopSyncTrans() {
    if (timerSyncTransApproved != null && timerSyncTransApproved!.isActive) {
      timerSyncTransApproved!.cancel();
    }
  }

  void setLanguage() {
    switchLanguage.value = !switchLanguage.value;
    updateLanguage();
  }

  void updateLanguage() {
    _appController.changeLanguageApp(switchLanguage.value ? "vi" : "en");
  }

  bool enableWidgetDynamicQR(OrderRethink orderRethink) {
    bool qrEnable = isQRActive.value;
    if (flagEmqxEnable.value) {
      if (orderRethink.paymentMethod == PaymentMethod.Card ||
          orderRethink.depositId.isNotEmpty) {
        qrEnable = false;
      }
    } else {
      if (orderRethink.depositId.isNotEmpty || isAutoPay == true) {
        qrEnable = false;
      }
    }
    return qrEnable;
  }

  void brightnessResumeControl({bool? onTap}) {
    if (flagAutoBrightnessEnable){
      _brightnessControl.monitorBrightnessControl(onTap: onTap);
    }
  }

  void brightnessDisableControl() {
    // if (flagAutoBrightnessEnable){
      _brightnessControl.holdBrightnessControl();
    // }
  }

  bool showWarningOrderApproved(String orderId) {
    var mapTrans = {};
    lstTransApproved.forEach((element) {
      var mapElement = jsonDecode(element);
      var paymentIdentifier = mapElement['result']['paymentIdentifier'] ?? '';
      var orderRethink = findOrderWithUdid(paymentIdentifier);
      if (orderRethink != null && orderId == orderRethink.order_code) {
        mapTrans = mapElement;
      }
    });
    if (mapTrans.isNotEmpty) {
      Get.showSnackbar(
        GetSnackBar(
          duration: Duration(seconds: 5),
          backgroundColor: Colors.transparent,
          snackPosition: SnackPosition.TOP,
          messageText: WidgetPopOrderState(
            orderCode: orderId,
            orderState: OrderStatus.Approved,
          ),
        ),
      );
      return true;
    }
    return false;
  }

  Future<void> _requestReGetAllConfig() async {
    await MacqRequest.requestMacqGetAllConfg(
        AppConfiguration().mobileUser, AppConfiguration().merchantId,
        deviceIdentifier: AppConfiguration().deviceIdentifier,
        serialNumber: AppConfiguration().isUseTingBox
            ? 'TINGBOX'
            : AppConfiguration().deviceMpos.deviceSerial);
  }

  void printQRReceiptWidget(String transDate, String transId, String refNo,
      String appCode, String type, String desc, String amount) {
    if (supportPrintReceipt) {
      screenShotController
          .captureFromWidget(WidgetReceiptQR(
              merchantName: AppConfiguration().merchantName,
              mid: AppConfiguration().mId,
              tid: AppConfiguration().tId,
              transDate: transDate,
              transId: transId,
              refNo: refNo,
              appCode: appCode,
              type: type,
              desc: desc,
              amount: amount))
          .then((value) {
        final imageEncoded = base64Encode(value);
        NativeBridge().callNativePrintReceipt(imageEncoded);
      });
    }
  }
//
}
