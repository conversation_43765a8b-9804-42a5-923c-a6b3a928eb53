// To parse this JSON data, do
//
//     final transResult = transResultFromJson(jsonString);

import 'dart:convert';
import 'dart:io';

TransResult transResultFromJson(String str) => TransResult.fromJson(json.decode(str));

String transResultToJson(TransResult data) => json.encode(data.toJson());

class TransResult {
  Result? result;
  UserCard? userCard;
  List<PromotionInfo>? promotionInfo;
  WfInfo? wfInfo;
  String local_route = "";
  String descriptionPayment = "";

  TransResult({
    this.result,
    this.userCard,
    this.promotionInfo,
    this.wfInfo,
  });

  factory TransResult.fromJson(Map<String, dynamic> json) => TransResult(
    result: json["result"] == null ? null : Result.fromJson(json["result"]),
    userCard: json["userCard"] == null ? null : UserCard.fromJson(json["userCard"]),
    promotionInfo: json["promotionInfo"] == null ? null : List<PromotionInfo>.from(json["promotionInfo"].map((x) => PromotionInfo.fromJson(x))),
    wfInfo: json["wfInfo"] == null ? null : WfInfo.fromJson(json["wfInfo"]),
  );

  Map<String, dynamic> toJson() => {
    "result": result?.toJson(),
    "userCard": userCard?.toJson(),
    "promotionInfo": promotionInfo == null ? [] : List<dynamic>.from(promotionInfo!.map((x) => x.toJson())),
    "wfInfo": wfInfo?.toJson(),
  };
}

class Result {
  String? status;
  int? transStatus;
  int amount;
  String? paymentIdentifier;
  String? transactionId;
  String? createdDate;
  Error? error;

  Result({
    this.status,
    this.transStatus,
    this.amount = 0,
    this.paymentIdentifier,
    this.transactionId,
    this.createdDate,
    this.error,
  });

  factory Result.fromJson(Map<String, dynamic> json) => Result(
        status: json["status"],
        transStatus: json["transStatus"],
        amount: int.parse(json["amount"] ?? '0'),
        paymentIdentifier: json["paymentIdentifier"],
        transactionId: json["transactionId"],
        createdDate: json["createdDate"] ?? '',
        error: json["error"] == null ? null : Error.fromJson(json["error"]),
      );

  Map<String, dynamic> toJson() => {
    "status": status,
    "transStatus": transStatus,
    "amount": amount,
    "paymentIdentifier": paymentIdentifier,
    "transactionId": transactionId,
    "createdDate": createdDate,
    "error": error,
  };
}

class UserCard {
  String? cardHolderName;
  String? pan;
  int? amountAuthorized;
  String? applicationLabel;
  String? authCode;
  String? tranReq;
  String? userSignature;

  UserCard({
    this.cardHolderName,
    this.pan,
    this.amountAuthorized,
    this.applicationLabel,
    this.authCode,
    this.tranReq,
    this.userSignature,
  });

  factory UserCard.fromJson(Map<String, dynamic> json) => UserCard(
    cardHolderName: json["cardHolderName"],
    pan: json["pan"],
    amountAuthorized: Platform.isAndroid ? json["amountAuthorized"] ?? 0 : int.parse(json["amountAuthorized"] ?? '0'),
    applicationLabel: json["applicationLabel"],
    authCode: json["authCode"],
    tranReq: json["tranReq"],
    userSignature: json["userSignature"],
  );

  Map<String, dynamic> toJson() => {
    "cardHolderName": cardHolderName,
    "pan": pan,
    "amountAuthorized": amountAuthorized,
    "applicationLabel": applicationLabel,
    "authCode": authCode,
    "tranReq": tranReq,
    "userSignature": userSignature,
  };
}

class Error {
  String? message;
  int? code;

  Error({
    this.message,
    this.code,
  });

  factory Error.fromJson(Map<String, dynamic> json) => Error(
    message: json["message"],
    code: json["code"],
  );

  Map<String, dynamic> toJson() => {
    "message": message,
    "code": code,
  };
}

class PromotionInfo {
  String timeExpired;
  String optional;
  String promotionCode;
  String followKey;
  String textSound;
  String amountDiscount;
  String description;
  String amountPayment;
  String orderAmount;

  PromotionInfo({
    required this.timeExpired,
    required this.optional,
    required this.promotionCode,
    required this.followKey,
    required this.textSound,
    required this.amountDiscount,
    required this.description,
    required this.amountPayment,
    required this.orderAmount,
  });

  factory PromotionInfo.fromJson(Map<String, dynamic> json) => PromotionInfo(
    timeExpired: json["timeExpired"],
    optional: json["optional"],
    promotionCode: json["promotionCode"],
    followKey: json["followKey"],
    textSound: json["textSound"],
    amountDiscount: json["amountDiscount"],
    description: json["description"],
    amountPayment: json["amountPayment"],
    orderAmount: json["orderAmount"],
  );

  Map<String, dynamic> toJson() => {
    "timeExpired": timeExpired,
    "optional": optional,
    "promotionCode": promotionCode,
    "followKey": followKey,
    "textSound": textSound,
    "amountDiscount": amountDiscount,
    "description": description,
    "amountPayment": amountPayment,
    "orderAmount": orderAmount,
  };
}

class WfInfo {
  String? muid;
  String? readerSerial;
  String? latitude;
  String? appl;
  String? merchantName;
  String? transactionDate;
  String? merchantAddress;
  String? trxType;
  String? batchNo;
  String? issuerCode;
  String? rrn;
  String? aid;
  String? wfId;
  String? acquirer;
  bool? flagPassPinRequired;
  String? authCode;
  String? merchantId;
  String? saleResCode;
  String? promotionCodes;
  String? arqc;
  String? longitude;
  String? status;
  String? tid;
  String? pan;
  String? mposTid;
  String? invoiceNo;
  bool? flagNoSignature;
  String? mid;
  String? amountDiscount;
  String? txid;
  String? cardHolderName;
  String? mposMid;
  String? udid;
  String? amount;

  WfInfo({
    this.muid,
    this.readerSerial,
    this.latitude,
    this.appl,
    this.merchantName,
    this.transactionDate,
    this.merchantAddress,
    this.trxType,
    this.batchNo,
    this.issuerCode,
    this.rrn,
    this.aid,
    this.wfId,
    this.acquirer,
    this.flagPassPinRequired,
    this.authCode,
    this.merchantId,
    this.saleResCode,
    this.promotionCodes,
    this.arqc,
    this.longitude,
    this.status,
    this.tid,
    this.pan,
    this.mposTid,
    this.invoiceNo,
    this.flagNoSignature,
    this.mid,
    this.amountDiscount,
    this.txid,
    this.cardHolderName,
    this.mposMid,
    this.udid,
    this.amount,
  });

  factory WfInfo.fromJson(Map<String, dynamic> json) => WfInfo(
    muid: json["muid"],
    readerSerial: json["readerSerial"],
    latitude: json["latitude"],
    appl: json["APPL"],
    merchantName: json["merchantName"],
    transactionDate: json["transactionDate"],
    merchantAddress: json["merchantAddress"],
    trxType: json["trxType"],
    batchNo: json["batchNo"],
    issuerCode: json["issuerCode"],
    rrn: json["rrn"],
    aid: json["AID"],
    wfId: json["wfId"],
    acquirer: json["acquirer"],
    flagPassPinRequired: json["flagPassPinRequired"],
    authCode: json["authCode"],
    merchantId: json["merchantId"],
    saleResCode: json["saleResCode"],
    promotionCodes: json["promotionCodes"],
    arqc: json["ARQC"],
    longitude: json["longitude"],
    status: json["status"],
    tid: json["tid"],
    pan: json["pan"],
    mposTid: json["mposTID"],
    invoiceNo: json["invoiceNo"],
    flagNoSignature: json["flagNoSignature"],
    mid: json["mid"],
    amountDiscount: json["amountDiscount"],
    txid: json["txid"],
    cardHolderName: json["cardHolderName"],
    mposMid: json["mposMID"],
    udid: json["udid"],
    amount: json["amount"],
  );

  Map<String, dynamic> toJson() => {
    "muid": muid,
    "readerSerial": readerSerial,
    "latitude": latitude,
    "APPL": appl,
    "merchantName": merchantName,
    "transactionDate": transactionDate,
    "merchantAddress": merchantAddress,
    "trxType": trxType,
    "batchNo": batchNo,
    "issuerCode": issuerCode,
    "rrn": rrn,
    "AID": aid,
    "wfId": wfId,
    "acquirer": acquirer,
    "flagPassPinRequired": flagPassPinRequired,
    "authCode": authCode,
    "merchantId": merchantId,
    "saleResCode": saleResCode,
    "promotionCodes": promotionCodes,
    "ARQC": arqc,
    "longitude": longitude,
    "status": status,
    "tid": tid,
    "pan": pan,
    "mposTID": mposTid,
    "invoiceNo": invoiceNo,
    "flagNoSignature": flagNoSignature,
    "mid": mid,
    "amountDiscount": amountDiscount,
    "txid": txid,
    "cardHolderName": cardHolderName,
    "mposMID": mposMid,
    "udid": udid,
    "amount": amount,
  };
}
